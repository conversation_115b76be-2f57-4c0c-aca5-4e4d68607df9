@extends('admin.admin_master')
@section('admin')
    <style>
        .list-group-item.active {
    background: ##007bff;
}
/* end common class */
.top-status ul {
    list-style: none;
    display: flex;
    justify-content: space-around;
    justify-content: center;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
}
.top-status ul li {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: #fff;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    border: 8px solid #ddd;
    box-shadow: 1px 1px 10px 1px #ddd inset;
    margin: 10px 5px;
}
.top-status ul li.active {
    border-color: #007bff;
    box-shadow: 1px 1px 20px 1px ##007bff inset;
}
/* end top status */

ul.timeline1 {
    list-style-type: none;
    position: relative;
}
ul.timeline1:before {
    content: ' ';
    background: #d4d9df;
    display: inline-block;
    position: absolute;
    left: 29px;
    width: 2px;
    height: 100%;
    z-index: 400;
}
ul.timeline1 > li {
    margin: 20px 0;
    padding-left: 30px;
}
ul.timeline1 > li:before {
    content: '\2713';
    background: #fff;
    display: inline-block;
    position: absolute;
    border-radius: 50%;
    border: 0;
    left: 5px;
    width: 50px;
    height: 50px;
    z-index: 400;
    text-align: center;
    line-height: 50px;
    color: #d4d9df;
    font-size: 24px;
    border: 2px solid #d4d9df;
}
ul.timeline1 > li.active:before {
    content: '\2713';
    background: #28a745;
    display: inline-block;
    position: absolute;
    border-radius: 50%;
    border: 0;
    left: 5px;
    width: 50px;
    height: 50px;
    z-index: 400;
    text-align: center;
    line-height: 50px;
    color: #fff;
    font-size: 30px;
    border: 2px solid #28a745;
}

ul.timeline1 > li.inactive:before {
    content: '\2715';
    background: #dc3545;
    display: inline-block;
    position: absolute;
    border-radius: 50%;
    border: 0;
    left: 5px;
    width: 50px;
    height: 50px;
    z-index: 400;
    text-align: center;
    line-height: 50px;
    color: #fff;
    font-size: 30px;
    border: 2px solid #dc3545;
}

ul.timeline1 > li.pending:before {
    content: '\003F';
    background: #ffc107;
    display: inline-block;
    position: absolute;
    border-radius: 50%;
    border: 0;
    left: 5px;
    width: 50px;
    height: 50px;
    z-index: 400;
    text-align: center;
    line-height: 50px;
    color: #fff;
    font-size: 30px;
    border: 2px solid ##ffc107;
}
/* end timeline */

.rating-color{
    color:#fbc634 !important;
}
    </style>
    <div class="content-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <br>
                    <h2><u>Active Employee Profile With Similar NIC</u></h2>
                </div><!-- /.col -->
                {{-- <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active"></li>
                    </ol>
                </div><!-- /.col --> --}}
            </div><!-- /.row -->
        </div><!-- /.container-fluid -->
    </div>
    <section class="content">
        <div class="container">
            <div class="main-body">
                <div class="row">


                    <div class="col-lg-12">


                        <div class="card">
                            <div class="card-body">
                            <h4>
                                Employee Details</h4>
                            <hr>
                               @foreach ($empSimilarData as $empSimilarData)

                               <div class="row">

                                <div class="col-12 p-1">
                                    <p><b>Found an active employee with the given NIC. Please deactivate it and try again.</b></p>
                                </div>

                            </div>
                                <div class="row">
                                    <div class="col-9">
                                        <div class="row">
                                            <div class="col-3 p-1">
                                                <label>Emp. No.</label>
                                            </div>
                                            <div class="col-4 p-1">
                                                <p><b>:</b> {{$empSimilarData->employee_no}}</p>
                                            </div>
                                            <div class="col-2 p-1">
                                                <label>NIC.</label>
                                            </div>
                                            <div class="col-3 p-1">
                                                <p><b>:</b> {{$empSimilarData->nic }}</p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-3 p-1">
                                                <label>Name</label>
                                            </div>
                                            <div class="col-9 p-1">
                                                <p><b>:</b> {{$empSimilarData->name_denoted_by_initials}} {{$empSimilarData->last_name}}</p>
                                            </div>

                                        </div>
                                        <div class="row">
                                            <div class="col-3 p-1">
                                                <label>Department</label>
                                            </div>
                                            <div class="col-4 p-1">
                                                <p><b>:</b> {{ $empSimilarData->getDepartmentName->department_name }} </p>
                                            </div>
                                            <div class="col-2 p-1">
                                                <label>Faculty</label>
                                            </div>
                                            <div class="col-3 p-1">
                                                <p><b>:</b> {{ $empSimilarData->getFacultyName->faculty_name }} </p>
                                            </div>

                                        </div>
                                        <div class="row">
                                            <div class="col-3 p-1">
                                                <label>Designation</label>
                                            </div>
                                            <div class="col-4 p-1">
                                                <p><b>:</b> {{$empSimilarData->designationName->designation_name}} </p>
                                            </div>

                                            <div class="col-2 p-1">
                                                <label>File Operator</label>
                                            </div>
                                            <div class="col-3 p-1">
                                                <p><b>:</b> {{ $empSimilarData->getAssignOperator->initials }} {{$empSimilarData->getAssignOperator->last_name }} </p>
                                            </div>

                                        </div>


                                    </div>
                                    <div class="col-3">
                                        <div class="text-center">
                                        @if (file_exists(public_path('backend/dist/img/profile/'.$empSimilarData->employee_no.'.jpg')))
                                        <img src="{{ asset('backend/dist/img/profile/'.$empSimilarData->employee_no.'.jpg') }}" class="img-responsive mx-auto d-block img-fluid border border-dark img-thumbnail" style="width: 150px;" alt="profile-image">
                                        @else
                                        <img src="{{ asset('backend/dist/img/profile.jpg') }}" class="img-responsive mx-auto d-block img-fluid border border-dark img-thumbnail" style="width: 150px;" alt="profile-image">
                                        @endif
                                        <br>
                                        <a href="{{ route('employee.show', encrypt($empSimilarData->employee_no)) }}" class="btn btn-md bg-yellow mb-2">
                                              <i class="fas fa-user"></i> View Full Profile
                                            </a>
                                            <br>

                                           <a href="{{ route('nac.vacancy.interview.completed.list') }}" class="btn btn-md bg-danger mb-2">
                                              <i class="fa fa-backward"></i> Back to List
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
