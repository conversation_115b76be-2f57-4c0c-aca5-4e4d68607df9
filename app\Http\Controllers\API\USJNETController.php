<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Models\incrementsProcessAc;
use App\Models\NonIncrement;
use App\Models\PromotionApplicationsNonacademic;
use App\Models\User;
use App\Models\Vacancy;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class USJNETController extends Controller
{
    public function nonUserEligibility(Request $request)
    {

        if (isset($request->sjpemail)) {

            return response()->json([
                "non_promo_info" => [
                    "eligibility" => $this->nonAcademicPromotionEligibility($request->sjpemail),
                    "message" => "Non Academic Employee Promotion Eligibility",
                ],
                "research_allownece" => [
                    "eligibility" => $this->researchAllownceEligibility($request->sjpemail),
                    "message" => "Academic Employee Research Allownece Eligibility",
                ],
                "insurance" => [
                    "eligibility" => $this->insuranceEligibility($request->sjpemail),
                    "message" => "Perment Employee Insurance Eligibility",
                ],
                "vehical_pass" => [
                    "eligibility" => $this->gatePassEligibility($request->sjpemail),
                    "message" => "Employee Vehical Pass Eligibility",
                ],
                "resource_monitoring" => [
                    "eligibility" => $this->resourceMonitoringEligibility($request->sjpemail),
                    "message" => "Employee Resource Monitoring System Eligibility",
                ],
                "academic_increment" => [
                    "eligibility" => $this->academicIncrementEligibility($request->sjpemail),
                    "message" => "Academic Employee Increment Eligibility",
                ],
                "employee_tin" => [
                    "eligibility" => $this->employeeTINEligibility($request->sjpemail),
                    "message" => "Employee TIN Eligibility",
                ]
            ], 200);
        }
    }



    public function userEligibility(Request $request)
    {

        if (isset($request->email)) {

            if ($this->ActiveEmployeeCheck($request->email) > 0) {

                if ($this->SystemUserCheck($request->email) > 0) {

                    $user = User::where('email', $request->email)->first();

                    if ($user->hasAnyRole(['cc', 'sc', 'lc', 'email-admin', 'stat'])) {

                        return response()->json([
                            "user_infomation" => [
                                "active_status" => 1,
                                "user_type" => 1,
                                "main_branch" => $this->userMainBranchget($request->email)->main_branch_id,
                                "message" => "Employee show with Dashboard",
                            ]
                        ], 200);

                    } elseif ($user->hasAnyRole(['vc','reg','head','dean']) && !$user->hasAnyRole(['super-admin', 'administrator', 'est-head'])) {

                        if ($user->hasAnyRole(['reg'])) {

                            return response()->json([
                                "user_infomation" => [
                                    "active_status" => 1,
                                    "user_type" => 2,
                                    "main_branch" => $this->userMainBranchget($request->email)->main_branch_id,
                                    "message" => "Employee function show USJNET only Registrar",
                                ],
                                "non_academic_promotion" => [
                                    "promo_count" => $this->nonAcademicPromotionHeadRecommendation($request->email)
                                ],
                                "non_academic_increment_head" => [
                                    "increment_count" => $this->nonAcademicIncrementHeadRecommendation($request->email)
                                ],
                                "non_academic_increment_registar" => [
                                    "increment_count" => $this->nonAcademicIncrementRegistrarApproval()
                                ],
                                "mohe_leave_approval" => [
                                ],

                            ], 200);

                        } elseif ($user->hasAnyRole(['vc'])) {

                            return response()->json([
                                "user_infomation" => [
                                    "active_status" => 1,
                                    "user_type" => 2,
                                    "main_branch" => $this->userMainBranchget($request->email)->main_branch_id,
                                    "message" => "Employee function show USJNET only Vice Chancellor",
                                ],
                                "non_academic_promotion" => [
                                    "promo_count" => $this->nonAcademicPromotionHeadRecommendation($request->email)
                                ],
                                "non_academic_increment_head" => [
                                    "increment_count" => $this->nonAcademicIncrementHeadRecommendation($request->email)
                                ],
                                "academic_vacancy" => [
                                    "vacancy_count" => $this->vancancyHeadShortListPendingTileShow($request->email),
                                    "pending_vacancy_count" => $this->vancancyHeadShortListPendingCountShow($request->email)
                                ],
                                "academic_increment_head" => [
                                    "increment_count" => $this->AcademicIncrementHeadRecommendation($request->email)
                                ],
                                "academic_increment_dean" => [
                                    "increment_count" => $this->AcademicIncrementDeanRecommendation($request->email)
                                ],
                                "academic_increment_vc" => [
                                    "increment_count" => $this->AcademicIncrementViceChancellorApproval($request->email)
                                ],
                                "mohe_leave_approval" => [
                                ],

                            ], 200);

                        } elseif ($user->hasExactRoles(['dean','head','user'])) {

                            return response()->json([
                                "user_infomation" => [
                                    "active_status" => 1,
                                    "user_type" => 2,
                                    "main_branch" => $this->userMainBranchget($request->email)->main_branch_id,
                                    "message" => "Employee function show USJNET only faculty dean and department head both role holders",
                                ],
                                "non_academic_promotion" => [
                                    "promo_count" => $this->nonAcademicPromotionHeadRecommendation($request->email)
                                ],
                                "non_academic_increment_head" => [
                                    "increment_count" => $this->nonAcademicIncrementHeadRecommendation($request->email)
                                ],
                                "academic_vacancy" => [
                                    "vacancy_count" => $this->vancancyHeadShortListPendingTileShow($request->email),
                                    "pending_vacancy_count" => $this->vancancyHeadShortListPendingCountShow($request->email)
                                ],
                                "academic_increment_head" => [
                                    "increment_count" => $this->AcademicIncrementHeadRecommendation($request->email)
                                ],
                                "academic_increment_dean" => [
                                    "increment_count" => $this->AcademicIncrementDeanRecommendation($request->email)
                                ],
                                "mohe_leave_approval" => [
                                ],

                            ], 200);

                        } elseif ($user->hasExactRoles(['dean','user'])) {

                            return response()->json([
                                "user_infomation" => [
                                    "active_status" => 1,
                                    "user_type" => 2,
                                    "main_branch" => $this->userMainBranchget($request->email)->main_branch_id,
                                    "message" => "Employee function show USJNET only faculty deans",
                                ],
                                "non_academic_promotion" => [
                                    "promo_count" => $this->nonAcademicPromotionHeadRecommendation($request->email)
                                ],
                                "non_academic_increment_head" => [
                                    "increment_count" => $this->nonAcademicIncrementHeadRecommendation($request->email)
                                ],
                                "academic_vacancy" => [
                                    "vacancy_count" => $this->vancancyHeadShortListPendingTileShow($request->email),
                                    "pending_vacancy_count" => $this->vancancyHeadShortListPendingCountShow($request->email)
                                ],
                                "academic_increment_dean" => [
                                    "increment_count" => $this->AcademicIncrementDeanRecommendation($request->email)
                                ],
                                "mohe_leave_approval" => [
                                ],

                            ], 200);

                        } elseif ($user->hasExactRoles(['head','user'])) {

                            return response()->json([
                                "user_infomation" => [
                                    "active_status" => 1,
                                    "user_type" => 2,
                                    "main_branch" => $this->userMainBranchget($request->email)->main_branch_id,
                                    "message" => "Employee function show USJNET only department heads",
                                ],
                                "non_academic_promotion" => [
                                    "promo_count" => $this->nonAcademicPromotionHeadRecommendation($request->email)
                                ],
                                "non_academic_increment_head" => [
                                    "increment_count" => $this->nonAcademicIncrementHeadRecommendation($request->email)
                                ],
                                "academic_vacancy" => [
                                    "vacancy_count" => $this->vancancyHeadShortListPendingTileShow($request->email),
                                    "pending_vacancy_count" => $this->vancancyHeadShortListPendingCountShow($request->email)
                                ],
                                "academic_increment_head" => [
                                    "increment_count" => $this->AcademicIncrementHeadRecommendation($request->email)
                                ],
                                "mohe_leave_approval" => [
                                ],

                            ], 200);

                        }

                    } elseif ($user->hasAnyRole(['super-admin', 'administrator', 'est-head'])) {

                        if ($user->hasAnyRole(['super-admin'])) {

                            return response()->json([
                                "user_infomation" => [
                                    "active_status" => 1,
                                    "user_type" => 3,
                                    "main_branch" => $this->userMainBranchget($request->email)->main_branch_id,
                                    "message" => "Employee function show USJNET + dashboard",
                                ],
                                "non_academic_promotion" => [
                                    "promo_count" => $this->nonAcademicPromotionHeadRecommendation($request->email)
                                ],
                                "non_academic_increment_head" => [
                                    "increment_count" => $this->nonAcademicIncrementHeadRecommendation($request->email)
                                ],
                                "non_academic_increment_registar" => [
                                    "increment_count" => $this->nonAcademicIncrementRegistrarApproval()
                                ],
                                "academic_vacancy" => [
                                    "vacancy_count" => $this->vancancyHeadShortListPendingTileShow($request->email),
                                    "pending_vacancy_count" => $this->vancancyHeadShortListPendingCountShow($request->email)
                                ],
                                "academic_increment_head" => [
                                    "increment_count" => $this->AcademicIncrementHeadRecommendation($request->email)
                                ],
                                "academic_increment_dean" => [
                                    "increment_count" => $this->AcademicIncrementDeanRecommendation($request->email)
                                ],
                                "academic_increment_vc" => [
                                    "increment_count" => $this->AcademicIncrementViceChancellorApproval($request->email)
                                ],
                                "mohe_leave_approval" => [
                                ],

                            ], 200);

                        } elseif ($user->hasAnyRole(['administrator'])) {

                            return response()->json([
                                "user_infomation" => [
                                    "active_status" => 1,
                                    "user_type" => 3,
                                    "main_branch" =>  $this->userMainBranchget($request->email)->main_branch_id,
                                    "message" => "Employee function show USJNET + dashboard",
                                ],
                                "non_academic_promotion" => [
                                    "promo_count" => $this->nonAcademicPromotionHeadRecommendation($request->email)
                                ],
                                "non_academic_increment_head" => [
                                    "increment_count" => $this->nonAcademicIncrementHeadRecommendation($request->email)
                                ],
                                "academic_vacancy" => [
                                    "vacancy_count" => $this->vancancyHeadShortListPendingTileShow($request->email),
                                    "pending_vacancy_count" => $this->vancancyHeadShortListPendingCountShow($request->email)
                                ],
                                "mohe_leave_approval" => [
                                ],
                            ], 200);

                        } elseif ($user->hasAnyRole(['est-head'])) {

                            return response()->json([
                                "user_infomation" => [
                                    "active_status" => 1,
                                    "user_type" => 3,
                                    "main_branch" =>  $this->userMainBranchget($request->email)->main_branch_id,
                                    "message" => "Employee function show USJNET + dashboard",
                                ],
                                "non_academic_promotion" => [
                                    "promo_count" => $this->nonAcademicPromotionHeadRecommendation($request->email)
                                ],
                                "non_academic_increment_head" => [
                                    "increment_count" => $this->nonAcademicIncrementHeadRecommendation($request->email)
                                ]
                            ], 200);

                        }
                    }
                } else {

                    return response()->json([
                        "user_infomation" => [
                            "active_status" => 0,
                            "message" => "Employee not in System User"
                        ]
                    ], 200);
                }
            } else {

                return response()->json([
                    "user_infomation" => [
                        "active_status" => 0,
                        "message" => "Employee Not in Active Employee List"
                    ]
                ], 200);
            }
            /****end of user chech condition */
        } else {

            return response()->json([
                "user_infomation" => [
                    "active_status" => 0,
                    "message" => "Email Not Found in HR System"
                ]
            ], 200);
        }
    }

    private function researchAllownceEligibility($sjpemail)
    {

        if ($sjpemail == "<EMAIL>") {

            $data = Employee::where('employees.email', '=', $sjpemail)->where('employee_status_id', 110)->count();

        } else {

            $data = Employee::where('employees.email', '=', $sjpemail)
                ->join('designations', 'employees.designation_id', '=', 'designations.id')
                ->where('designations.research_allowance_status', 1)
                ->where('employee_status_id', 110)
                //->WhereNotIn('employees.employee_no', [11512,12075,12850,15011,7204])
                ->count();
        }
        return $data;
    }


    private function nonAcademicPromotionEligibility($sjpemail)
    {

        $data = Employee::where('employees.email', '=', $sjpemail)->where('employees.promo_eligibility', 1)->count();

        return $data;
    }

    private function insuranceEligibility($sjpemail)
    {

        $data = Employee::where('employees.email', '=', $sjpemail)
            ->where('employees.employee_status_id', 110)
            ->wherein('employees.employee_work_type', [139, 138])
            ->count();

        return $data;
    }


    private function gatePassEligibility($sjpemail)
    {

        $data = Employee::where('employees.email', '=', $sjpemail)
            ->where('employees.employee_status_id', 110)
            ->count();

        return $data;
    }

    private function resourceMonitoringEligibility($sjpemail)
    {
        //Faculty of Dental Sciences (Acting) - palangasinghe (Ms  S.N. Palangasinghe )
        //Faculty of Urban and Aquatic Bioresources (Acting) - indum ( Ms Indu Malalgoda )
        if ($sjpemail == "<EMAIL>" || $sjpemail == "<EMAIL>" || $sjpemail == "<EMAIL>" || $sjpemail == "<EMAIL>" || $sjpemail == "<EMAIL>") {

            return 1;
        }

        $facultydeanfilter = User::join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->join('employees', 'users.employee_no', '=', 'employees.employee_no')
            ->where('roles.id', '=', '9')
            ->where('employees.email', '=', $sjpemail)
            ->count();

        $facultyadminofficerfilter = Employee::where('employees.email', '=', $sjpemail)
            ->whereNotIn('employees.faculty_id', [50, 51])
            ->whereIn('employees.designation_id', [18, 126, 422, 729])
            ->where('employee_status_id', 110)
            ->count();

        return $facultydeanfilter + $facultyadminofficerfilter;
    }

    private function academicIncrementEligibility($sjpemail)
    {

        $data = Employee::where('employees.email', '=', $sjpemail)
            ->wherein('employees.employee_work_type', [139, 138])
            ->where('employees.employee_status_id', 110)
            //->where('employees.main_branch_id', 52)
            ->count();

        return $data;
    }

    private function employeeTINEligibility($sjpemail)
    {
        if ($sjpemail == "<EMAIL>"){

            $data = 1;

        }else{

           $data = Employee::join('designations','employees.designation_id', '=', 'designations.id')
                ->where('employees.email', '=', $sjpemail)
                ->where('employees.employee_status_id', 110)
                ->where('employees.tin_no', 0)
                ->whereIN('designations.ugc_mis', [100, 102, 103, 135, 136])
                ->where('employees.main_branch_id', 52)
                ->count();
        }

        return $data;
    }

    private function SystemUserCheck($sjpemail)
    {

        $data = User::where('users.email', '=', $sjpemail)->where('users.status_id', 1)->count();

        return $data;
    }

    private function ActiveEmployeeCheck($sjpemail)
    {

        $data =  Employee::where('employees.email', '=', $sjpemail)->where('employees.employee_status_id', 110)->count();

        return $data;
    }

    private function userIdget($sjpemail)
    {

        $data =  User::where('users.email', '=', $sjpemail)->select('id')->first();

        return $data;
    }

    private function userMainBranchget($sjpemail)
    {

        $data =  User::where('users.email', '=', $sjpemail)->select('main_branch_id')->first();

        return $data;
    }

    private function nonAcademicPromotionHeadRecommendation($sjpemail)
    {

        //$promoYear = date("Y") - 1;

        if ($this->userIdget($sjpemail)->id == 1) {

            $data = PromotionApplicationsNonacademic::where('promotion_applications_nonacademics.head_status', '=', 1)->count();
        } else {

            $data = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->where('promotion_applications_nonacademics.head_status', '=', 1)
                ->where('departments.head_email', '=', $sjpemail)
                ->count();
        }

        return $data;
    }

    private function vancancyHeadShortListPendingCountShow($sjpemail)
    {

        if ($this->userIdget($sjpemail)->id == 1) {

            $data = Vacancy::join('departments', 'vacancies.department_id', '=', 'departments.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'departments.head_email',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 34 THEN 1 ELSE 0 END), 0) as booked'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist')
                )->groupBy('vacancies.id', 'departments.head_email')
                ->where('vacancies.vacancy_status_type_id', 258)
                ->havingRaw('booked != 0 OR sortlist != 0')
                ->count();
        } else {

            $data = Vacancy::join('departments', 'vacancies.department_id', '=', 'departments.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'departments.head_email',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 34 THEN 1 ELSE 0 END), 0) as booked'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist')
                )
                ->groupBy('vacancies.id', 'departments.head_email')
                ->where('vacancies.vacancy_status_type_id', 258)
                ->where('departments.head_email', '=', $sjpemail)
                ->havingRaw('booked != 0 OR sortlist != 0')
                ->count();
        }



        return $data;
    }

    private function vancancyHeadShortListPendingTileShow($sjpemail)
    {

        if ($this->userIdget($sjpemail)->id == 1) {

            $data = Vacancy::whereIn('vacancy_status_type_id', array(29, 258, 31))->count();
        } else {

            $data = Vacancy::join('departments', 'vacancies.department_id', '=', 'departments.id')
                ->where('departments.head_email', '=', $sjpemail)
                ->whereIn('vacancy_status_type_id', array(29, 258, 31))
                ->count();
        }

        return $data;
    }

    private function nonAcademicIncrementHeadRecommendation($sjpemail)
    {

        //$IncrementYear = date("Y");

        if ($this->userIdget($sjpemail)->id == 1) {

            $data = NonIncrement::where('non_increments.status', 3)->count();

        } else {

            $data =  NonIncrement::join('departments', 'non_increments.department_id', '=', 'departments.id')
                ->where('non_increments.status', 3)
                ->where('departments.head_email', '=', $sjpemail)
                ->count();
        }


        return $data;
    }


    private function nonAcademicIncrementRegistrarApproval()
    {

        $data =  NonIncrement::where('non_increments.status', 5)->count();

        return $data;
    }

    private function AcademicIncrementHeadRecommendation($sjpemail){

        if ($this->userIdget($sjpemail)->id == 1) {

            $data = incrementsProcessAc::where('increments_process_acs.head_status', 1)->count();

        } else {

            $data =  incrementsProcessAc::join('department_heads', 'department_heads.department_id', '=', 'increments_process_acs.dep_no')
                ->where('increments_process_acs.head_status', 1)
                ->where('department_heads.email', '=', $sjpemail)
                ->count();
        }


        return $data;

    }

    private function AcademicIncrementDeanRecommendation($sjpemail){

        if ($this->userIdget($sjpemail)->id == 1) {

            $data = incrementsProcessAc::where('increments_process_acs.dean_status', 1)->count();

        } else {

            $data =  incrementsProcessAc::join('departments', 'increments_process_acs.dep_no', '=', 'departments.id')
                     ->join('faculties', 'departments.faculty_code', '=', 'faculties.id')
                     ->where('increments_process_acs.dean_status', 1)
                     ->where('faculties.dean_email', '=', $sjpemail)
                     ->count();
        }

        return $data;

    }

    private function AcademicIncrementViceChancellorApproval($sjpemail){

        if ($this->userIdget($sjpemail)->id == 1) {

            $data = incrementsProcessAc::where('increments_process_acs.vc_status', 1)->count();

        }else{

            $data = incrementsProcessAc::where('increments_process_acs.vc_status', 1)->count();
        }

        return $data;

    }
}
