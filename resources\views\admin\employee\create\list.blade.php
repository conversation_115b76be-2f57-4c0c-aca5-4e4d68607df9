@extends('admin.admin_master')
@section('admin')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">New Employees List</h1>
            </div><!-- /.col -->
            @role('super-admin|administrator|cc|sc')
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <a href="{{ route('employee.generate.add.view') }}" style="float: right;" class="btn btn-success mb-5">Create New Employee</a>
                </ol>
            </div><!-- /.col -->
            @endrole
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <!-- /.box-header -->
                    <div class="card card-warning">
                        <div class="card-header">
                            <h3 class="card-title">Employee Number Generate Pending List <span class="badge badge-pill badge-danger">{{ $pendingnewEmployees->count() }}</span></h3>
                          </div>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example1" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="5%">SN</th>
                                        <th>Name</th>
                                        <th>NIC</th>
                                        <th>Department</th>
                                        <th>Designation</th>
                                        <th>Employee Type</th>
                                        @role('super-admin')
                                        <th>Main Branch</th>
                                        @endrole
                                        @role('super-admin|est-head')
                                        <th>Added Operator</th>
                                        @endrole
                                        @role('super-admin')
                                        <th width="16%" data-priority="1">Action</th>
                                        @else
                                        <th width="16%" data-priority="1">Action</th>
                                        @endrole
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($pendingnewEmployees as $key => $employee)
                                    <tr>
                                        <td>{{ $key+1 }}</td>
                                        <td>{{ ucfirst($employee->initials) }} {{ ucfirst($employee->last_name) }}</td>
                                        <td>{{ ucfirst($employee->nic) }}</td>
                                        <td>{{ $employee->getDepartmentName->department_name }}</td>
                                        <td>{{ $employee->designationName->designation_name }} @if ($employee->category_name != '') ({{ $employee->category_name }}) @endif</td>
                                        <td>
                                        @if($employee->employee_work_type  == 138)
                                           <span class="badge badge-pill badge-success">permanent</span>
                                        @elseif($employee->employee_work_type  == 140)
                                        <span class="badge badge-pill badge-primary">Tempory</span>
                                        @elseif($employee->employee_work_type  == 141)
                                        <span class="badge badge-pill badge-info">Contract</span>
                                        @else
                                           <span class="badge badge-pill bg-purple">Assignment Basis</span>
                                        @endif
                                        </td>
                                        @role('super-admin')
                                        <td>{{ $employee->mainBranch->category_name }}</td>
                                        @endrole
                                        @role('super-admin|est-head')
                                        @if($employee->added_ma_id != 0)
                                        <td>{{ $employee->getAddedOperator->initials }} {{ $employee->getAddedOperator->last_name }}</td>
                                        @else
                                        <td class="text-danger">No File Assign Operator</td>
                                        @endif
                                        @endrole
                                        @role('super-admin|administrator')
                                        <td>
                                            @if ($employee->employee_work_type == 138 ||  $employee->employee_work_type == 139)
                                            <a href="{{ route('employee.generate.edit.permanent', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Create</a>
                                            @elseif ($employee->employee_work_type == 140)
                                            <a href="{{ route('employee.generate.edit.tempory', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Create</a>
                                            @elseif ($employee->employee_work_type == 141)
                                            <a href="{{ route('employee.generate.edit.contract', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Create</a>
                                            @elseif ($employee->employee_work_type == 142)
                                            <a href="{{ route('employee.generate.edit.assignment.basis', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Create</a>
                                            @endif
                                            <a href="{{ route('employee.generate.delete', encrypt($employee->id)) }}" class="btn btn-sm btn-danger"  id="delete"><i class="fas fa-trash"></i> Delete</a>
                                        </td>
                                        @endrole
                                        @role('est-head')
                                        <td>
                                            @if ($employee->employee_work_type == 138 ||  $employee->employee_work_type == 139)
                                            <a href="{{ route('employee.generate.edit.permanent', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Create</a>
                                            @elseif ($employee->employee_work_type == 140)
                                            <a href="{{ route('employee.generate.edit.tempory', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Craete</a>
                                            @elseif ($employee->employee_work_type == 141)
                                            <a href="{{ route('employee.generate.edit.contract', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Create</a>
                                            @elseif ($employee->employee_work_type == 142)
                                            <a href="{{ route('employee.generate.edit.assignment.basis', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Create</a>
                                            @endif
                                        </td>
                                        @endrole
                                        @role('cc|sc')
                                        <td>
                                            @if ($employee->employee_work_type == 138 ||  $employee->employee_work_type == 139)
                                            <a href="{{ route('employee.generate.edit.permanent', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Edit</a>
                                            @elseif ($employee->employee_work_type == 140)
                                            <a href="{{ route('employee.generate.edit.tempory', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Edit</a>
                                            @elseif ($employee->employee_work_type == 141)
                                            <a href="{{ route('employee.generate.edit.contract', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Edit</a>
                                            @elseif ($employee->employee_work_type == 142)
                                            <a href="{{ route('employee.generate.edit.assignment.basis', encrypt($employee->id)) }}" class="btn btn-sm btn-info"><i class="fas fa-pencil-alt"></i> Edit</a>
                                            @endif
                                        </td>
                                        @endrole
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <br>
                    <div class="card card-success">
                        <div class="card-header">
                            <h3 class="card-title">Employee Number Generated List  <span class="badge badge-pill badge-danger">{{ $completednewEmployees->count() }}</span></h3>
                          </div>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example2" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="5%">SN</th>
                                        <th>Emp No</th>
                                        <th>Name</th>
                                        <th>NIC</th>
                                        <th>Department</th>
                                        <th>Designation</th>
                                        <th>Employee Type</th>
                                        @role('super-admin')
                                        <th>Main Branch</th>
                                        @endrole
                                        <th width="10%">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($completednewEmployees as $key => $trashEmployee)
                                    <tr>
                                        <td>{{ $key+1 }}</td>
                                        <td>{{ ucfirst($trashEmployee->employee_no) }}</td>
                                        <td>{{ ucfirst($trashEmployee->initials) }} {{ ucfirst($trashEmployee->last_name) }}</td>
                                        <td>{{ ucfirst($trashEmployee->nic) }}</td>
                                        <td>{{ $trashEmployee->getDepartmentName->department_name ?? 'N/A'}}</td>
                                        <td>{{ $trashEmployee->designationName->designation_name }}</td>
                                        <td>
                                            @if($trashEmployee->employee_work_type  == 138)
                                               <span class="badge badge-pill badge-success">permanent</span>
                                            @elseif($trashEmployee->employee_work_type  == 140)
                                            <span class="badge badge-pill badge-primary">Tempory</span>
                                            @elseif($trashEmployee->employee_work_type  == 141)
                                            <span class="badge badge-pill badge-info">Contract</span>
                                            @else
                                               <span class="badge badge-pill bg-purple">Assignment Basis</span>
                                            @endif
                                        </td>
                                        @role('super-admin')
                                        <td>{{ $trashEmployee->mainBranch->category_name }}</td>
                                        @endrole
                                        <td>
                                            <a href="{{ route('employee.show', encrypt($trashEmployee->employee_no)) }}" class="btn btn-sm btn-primary"><i class="fa fa-eye"></i> Show</a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- /.box-body -->
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection
