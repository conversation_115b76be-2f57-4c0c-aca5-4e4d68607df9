<?php

namespace App\Console\Commands;

use App\Models\Employee;
use Illuminate\Console\Command;

class ServiceTerminationCron extends Command
{

    protected $signature = 'servicetermination:cron';

    protected $description = '114-Vacation of Post';

    public function handle()
    {
        $employees = Employee::whereDate('salary_termination_date_1', '<', date('Y-m-d'))
            ->whereIn('employee_work_type', [138, 139])
            ->where('employee_status_id', 110)
            ->get();

        foreach ($employees as $employee) {
            $employee->employee_status_id = 111;
            $employee->employee_status_type_id = 114;
            $employee->save();

            $this->employeeRecordAdd($employee->employee_no);
        }

        return Command::SUCCESS;
    }
}
