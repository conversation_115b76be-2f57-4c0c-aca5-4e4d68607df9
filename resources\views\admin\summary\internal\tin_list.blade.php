@extends('admin.admin_master')
@section('admin')

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1 class="m-0">Taxpayer Idendification Number Summary</h1>
            </div>
            <div class="col-sm-3">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="#">Home</a></li>
                    <li class="breadcrumb-item active">Taxpayer Idendification Number Summary</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">

        {{-- Employees with Salary Revision --}}
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <div class="card card-success">
                        <div class="card-header">
                            <h3 class="card-title">Taxpayer Idendification Number Summary List Data</h3>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example5" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Employee No</th>
                                        <th>Name with Initials</th>
                                        <th>Full Name</th>
                                        <th>Email</th>
                                        <th>Mobile No.</th>
                                        <th>Main Branch</th>
                                        <th>Designation</th>
                                        <th>NIC</th>
                                        <th>Faculty</th>
                                        <th>Department</th>
                                        <th>Working Type</th>
                                        <th>Employee Category</th>
                                        <th>Employee Status</th>
                                        <th>Employee Status Type</th>
                                        <th data-priority="1">TIN</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($TinEmployees as $data)
                                    <tr>
                                        <td>{{ $data->employee_no }}</td>
                                        <td>{{ $data->title_name }} {{ $data->initials }} {{ $data->last_name }}</td>
                                        <td>{{ $data->name_denoted_by_initials }} {{ $data->last_name }}</td>
                                        <td>{{ $data->email }}</td>
                                        <td>{{ $data->mobile_no }}</td>
                                        <td>{{ $data->mainBranch->category_name }}</td>
                                        <td>{{ $data->designationName->designation_name }} ( {{ $data->designationName->salary_code }} )  @if($data->staff_grade_name != "")- {{ $data->staff_grade_name }}@endif</td>
                                        <td>{{ $data->nic }}</td>
                                        <td>{{ $data->getFacultyName->faculty_name }}</td>
                                        <td>{{ $data->getDepartmentName->department_name }}</td>
                                        <td>
                                            @if ($data->employee_work_type == 138 || $data->employee_work_type == 139)
                                                <span
                                                    class="badge badge-pill badge-success">{{ $data->workTypeName->category_name }}</span>
                                            @elseif ($data->employee_work_type == 140)
                                                <span
                                                    class="badge badge-pill badge-info">{{ $data->workTypeName->category_name }}</span>
                                            @elseif ($data->employee_work_type == 141 || $data->employee_work_type == 142)
                                                <span
                                                    class="badge badge-pill badge-primary">{{ $data->workTypeName->category_name }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $data->ugs_mis_name }}</td>
                                        <td>
                                            @if ($data->employee_status_id == 110)
                                            <span class="badge badge-pill badge-success">Active</span>
                                            @else
                                            <span class="badge badge-pill badge-danger">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge badge-pill badge-dark">{{ $data->empStatusTypeName->category_name }}</span>
                                        </td>
                                        <td data-priority="1">
                                            {{ $data->tin_no }}
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Employees without Salary Revision --}}
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <div class="card card-warning">
                        <div class="card-header">
                            <h3 class="card-title">Taxpayer Idendification Number Pending List</h3>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example6" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Employee No</th>
                                        <th>Name with Initials</th>
                                        <th>Full Name</th>
                                        <th>Email</th>
                                        <th>Mobile No.</th>
                                        <th>Designation</th>
                                        <th>NIC</th>
                                        <th>Faculty</th>
                                        <th>Department</th>
                                        <th>Working Type</th>
                                        <th>Employee Category</th>
                                        <th>Employee Status</th>
                                        <th>Employee Staus Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($TinPendingEmployees as $data)
                                    <tr>
                                        <td>{{ $data->employee_no }}</td>
                                        <td>{{ $data->title_name }} {{ $data->initials }} {{ $data->last_name }}</td>
                                        <td>{{ $data->name_denoted_by_initials }} {{ $data->last_name }}</td>
                                        <td>{{ $data->email }}</td>
                                        <td>{{ $data->mobile_no }}</td>
                                        <td>{{ $data->designationName->designation_name }} ( {{ $data->designationName->salary_code }} )  @if($data->staff_grade_name != "")- {{ $data->staff_grade_name }}@endif</td>
                                        <td>{{ $data->nic }}</td>
                                        <td>{{ $data->getFacultyName->faculty_name }}</td>
                                        <td>{{ $data->getDepartmentName->department_name }}</td>
                                        <td>
                                            @if ($data->employee_work_type == 138 || $data->employee_work_type == 139)
                                                <span
                                                    class="badge badge-pill badge-success">{{ $data->workTypeName->category_name }}</span>
                                            @elseif ($data->employee_work_type == 140)
                                                <span
                                                    class="badge badge-pill badge-info">{{ $data->workTypeName->category_name }}</span>
                                            @elseif ($data->employee_work_type == 141 || $data->employee_work_type == 142)
                                                <span
                                                    class="badge badge-pill badge-primary">{{ $data->workTypeName->category_name }}</span>
                                            @endif
                                        </td>
                                        <td> {{ $data->ugs_mis_name }}</td>
                                        <td>
                                            @if ($data->employee_status_id == 110)
                                            <span class="badge badge-pill badge-success">Active</span>
                                            @else
                                            <span class="badge badge-pill badge-danger">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge badge-pill badge-dark">{{ $data->empStatusTypeName->category_name }}</span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>



    </div>
</section>

@endsection
