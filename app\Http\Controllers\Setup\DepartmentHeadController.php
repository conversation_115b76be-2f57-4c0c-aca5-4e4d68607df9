<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Department;
use App\Models\DepartmentHead;
use App\Models\Employee;
use App\Models\Faculty;
use App\Models\FacultyDean;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class DepartmentHeadController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function departmentHeadIndex()
    {

        $departmentHeads = DepartmentHead::orderby('department_id')->get();

        return view('admin.setups.department_head.index', compact('departmentHeads'));
    }

    public function departmentHeadAdd()
    {
        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;

        if ($mainBranch == 51) {

            $employees = Employee::where('main_branch_id', 52)
                ->where('employee_status_id', 110)
                ->where('email', '!=', NULL)
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head', 'cc'])) {

                $employees = Employee::where('main_branch_id', 52)
                    ->where('employee_status_id', 110)
                    ->where('employee_status_type_id',112)
                    ->where('email', '!=', NULL)
                    ->get();
            } elseif (Auth()->user()->hasRole(['sc'])) {

                $employees = Employee::where('main_branch_id', 52)
                    ->where('employee_status_id', 110)
                    ->where('employee_status_type_id',112)
                    ->where('email', '!=', NULL)
                    ->where('assign_ma_user_id', $empNo)
                    ->get();
            }
        }

        $departments = Department::all();
        // Get the list of faculty IDs from the faculty_deans table
        $usedFacultyIds = DepartmentHead::pluck('department_id');
        //dd($usedFacultyIds);

        //Filter the $faculties collection to get only the unused faculties
        $departments = $departments->reject(function ($faculty) use ($usedFacultyIds) {
            return $usedFacultyIds->contains($faculty->id);
        });


        $categories = $this->getCategories([33, 45]);
        $appointmentTypes = $categories->where('category_type_id', '33');

        $headPositions = Category::where('category_type_id', 45)->orderBy('category_name', 'ASC')->get();

        return view('admin.setups.department_head.add', compact('employees', 'appointmentTypes', 'departments', 'headPositions'));
    }



    public function departmentHeadStore(Request $request)
    {
        Log::info('DepartmentHeadController -> department head store started');

        $validatedData = $request->validate([
            //'faculty_id' => 'required',
            'department_id' => 'required',
            'department_head' => 'required',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            'appointmemt_type' => 'required',
            'head_position' => 'required'
        ], [
            //'faculty_id.required' => 'select relevent faculty',
            'department_id.required' => 'select relevent department',
            'department_head.required' => 'select relevent department head',
            'start_date.required' => 'choose department head appointment date',
            'start_date.date' => 'department head appointment date invalid',
            'end_date.date' => 'department head appointment close date invalid',
            'appointmemt_type.required' => 'select the relavent appointment type',
            'head_position.required' => 'select the relavent head position'
        ]);

        //get head email
        $heademail = Employee::where('employee_no', $request->department_head)
            ->select('email as value')
            ->get();

        $heademail = json_decode($heademail, true);
        $heademail = $heademail[0]["value"];

        //get head belongs sc
        $operator = Employee::where('employee_no', $request->department_head)
            ->select('assign_ma_user_id as value')
            ->get();

        $operator = json_decode($operator, true);
        $operator = $operator[0]["value"];


        //get faculty dean office status
        $is_faculty_office = Department::where('id', $request->department_id)
            ->select('is_faculty_office as value')
            ->get();

        $is_faculty_office = json_decode($is_faculty_office, true);
        $is_faculty_office = $is_faculty_office[0]["value"];



        if ($is_faculty_office == 1) {

            //task 01
            $data = new DepartmentHead();
            $data->department_id = $request->department_id;
            $data->email = $heademail;
            $data->emp_no = $request->department_head;
            $data->appointmemt_type = $request->appointmemt_type;
            $data->head_position = $request->head_position;
            $data->start_date = date("Y-m-d", strtotime($request->start_date));
            $data->end_date = date("Y-m-d", strtotime($request->end_date));
            $data->added_user_id = $operator;
            $data->active_status = 1;
            $data->last_update_user = Auth()->user()->employee_no;
            $data->last_update_date = date('Y-m-d');
            $data->created_at = Carbon::now();
            $data->save();

            if ($request->department_id != 5001) {

                //get faculty id
                $facultyID = Department::where('id', $request->department_id)
                    ->select('faculty_code as value')
                    ->get();

                $facultyID = json_decode($facultyID, true);
                $facultyID = $facultyID[0]["value"];
            } else {

                $facultyID = 51;
            }


            $data1 = new FacultyDean();
            $data1->faculty_id = $facultyID;
            $data1->email = $heademail;
            $data1->emp_no = $request->department_head;
            $data1->appointmemt_type = $request->appointmemt_type;
            $data1->start_date = date("Y-m-d", strtotime($request->start_date));
            $data1->end_date = date("Y-m-d", strtotime($request->end_date));
            $data1->added_user_id = $operator;
            $data1->active_status = 1;
            $data1->created_at = Carbon::now();
            $data1->save();

            if (User::where('email', $heademail)->exists()) {

                // record exists
                $user = User::where('email', $heademail)->first();

                if (!$user->hasExactRoles(['head', 'dean'])) {

                    $user->assignRole('head', 'dean');
                } elseif (!$user->hasExactRoles(['head'])) {

                    $user->assignRole('head');
                } elseif (!$user->hasExactRoles(['dean'])) {

                    $user->assignRole('dean');
                }

                //get head user id
                $userId = User::where('email', $heademail)
                    ->select('id as value')
                    ->get();

                $userId = json_decode($userId, true);
                $userId = $userId[0]["value"];

                //department update
                $dept = Department::where('id', '=', $request->department_id)->first();
                $dept->head_email = $heademail;
                $dept->head_user_id = $userId;
                $dept->save();

                //department head update
                $depth = DepartmentHead::where('id', '=', $data->id)->first();
                $depth->head_user_id = $userId;
                $depth->save();

                // faculty update
                $fac = Faculty::where('id', '=', $facultyID)->first();
                $fac->dean_email = $heademail;
                $fac->dean_user_id = $userId;
                $fac->save();

                // faculty dean update
                $facd = FacultyDean::where('id', '=', $data1->id)->first();
                $facd->dean_user_id = $userId;
                $facd->save();
            } else {

                //get last name
                $lastName = Employee::where('employee_no', $request->department_head)->select('last_name as value')->get();

                $lastName = json_decode($lastName, true);
                $lastName = $lastName[0]["value"];

                $user = new User();
                $code = rand(1000, 9999);
                $user->employee_no = $request->department_head;
                $user->main_branch_id = 51;
                $user->name = $lastName;
                $user->email = $heademail;
                $user->password = bcrypt($code);
                $user->status_id = 1;
                $user->created_user_id = Auth()->user()->employee_no;
                $user->save();

                $user->assignRole('user', 'head', 'dean');


                //department update
                $dept = Department::where('id', '=', $request->department_id)->first();
                $dept->head_email = $heademail;
                $dept->head_user_id = $user->id;
                $dept->save();

                //department head update
                $depth = DepartmentHead::where('id', '=', $data->id)->first();
                $depth->head_user_id = $user->id;
                $depth->save();

                // faculty update
                $fac = Faculty::where('id', '=', $facultyID)->first();
                $fac->dean_email = $heademail;
                $fac->dean_user_id = $user->id;
                $fac->save();

                // faculty dean update
                $facd = FacultyDean::where('id', '=', $data1->id)->first();
                $facd->dean_user_id = $user->id;
                $facd->save();

                // employee user account status update
                $emp = Employee::where('employee_no', '=', $request->department_head)->first();
                $emp->user_account_status = 1;
                $emp->save();
            }
        } else {

            //task 01
            $data = new DepartmentHead();
            $data->department_id = $request->department_id;
            $data->email = $heademail;
            $data->emp_no = $request->department_head;
            $data->appointmemt_type = $request->appointmemt_type;
            $data->head_position = $request->head_position;
            $data->start_date = date("Y-m-d", strtotime($request->start_date));
            $data->end_date = date("Y-m-d", strtotime($request->end_date));
            $data->added_user_id = $operator;
            $data->active_status = 1;
            $data->last_update_user = Auth()->user()->employee_no;
            $data->last_update_date = date('Y-m-d');
            $data->created_at = Carbon::now();
            $data->save();


            if (User::where('email', $heademail)->exists()) {

                // record exists
                $user = User::where('email', $heademail)->first();

                if (!$user->hasRole(['head'])) {

                    $user->assignRole('head');
                }

                //get head user id
                $userId = User::where('email', $heademail)
                    ->select('id as value')
                    ->get();

                $userId = json_decode($userId, true);
                $userId = $userId[0]["value"];

                //department update
                $dept = Department::where('id', '=', $request->department_id)->first();
                $dept->head_email = $heademail;
                $dept->head_user_id = $userId;
                $dept->save();

                //department head update
                $depth = DepartmentHead::where('id', '=', $data->id)->first();
                $depth->head_user_id = $userId;
                $depth->save();
            } else {

                //get last name
                $lastName = Employee::where('employee_no', $request->department_head)->select('last_name as value')->get();

                $lastName = json_decode($lastName, true);
                $lastName = $lastName[0]["value"];

                $user = new User();
                $code = rand(1000, 9999);
                $user->employee_no = $request->department_head;
                $user->main_branch_id = 51;
                $user->name = $lastName;
                $user->email = $heademail;
                $user->password = bcrypt($code);
                $user->status_id = 1;
                $user->created_user_id = Auth()->user()->employee_no;
                $user->save();

                $user->assignRole('user', 'head');

                //department update
                $dept = Department::where('id', '=', $request->department_id)->first();
                $dept->head_email = $heademail;
                $dept->head_user_id = $user->id;
                $dept->save();

                //department head update
                $depth = DepartmentHead::where('id', '=', $data->id)->first();
                $depth->head_user_id = $user->id;
                $depth->save();

                // employee user account status update
                $emp = Employee::where('employee_no', '=', $request->department_head)->first();
                $emp->user_account_status = 1;
                $emp->save();
            }
        }

        $notification = array(
            'message' => 'New Department Head Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('department.head.index')->with($notification);
    }

    public function departmentHeadEdit($id)
    {
        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;

        $depHeadId = decrypt($id);
        $editData = DepartmentHead::find($depHeadId);
        if ($mainBranch == 51) {

            $employees = Employee::where('main_branch_id', 52)
                ->where('employee_status_id', 110)
                ->where('email', '!=', NULL)
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head', 'cc'])) {

                $employees = Employee::where('main_branch_id', 52)
                    ->where('employee_status_id', 110)
                    ->where('employee_status_type_id',112)
                    ->where('email', '!=', NULL)
                    ->get();
            } elseif (Auth()->user()->hasRole(['sc'])) {

                $employees = Employee::where('main_branch_id', 52)
                    ->where('employee_status_id', 110)
                    ->where('employee_status_type_id',112)
                    ->where('email', '!=', NULL)
                    ->where('assign_ma_user_id', $empNo)
                    ->get();
            }
        }
        $departments =  Department::all();
        $categories = $this->getCategories([33, 45]);
        $appointmentTypes = $categories->where('category_type_id', '33');
        //$headPositions = $categories->where('category_type_id', '45');
        $headPositions = Category::where('category_type_id', 45)->orderBy('category_name', 'ASC')->get();

        return view('admin.setups.department_head.edit', compact('editData', 'employees', 'departments', 'appointmentTypes', 'headPositions'));
    }

    public function departmentHeadUpdate(Request $request, $id)
    {

        $validatedData = $request->validate([
            'department_head' => 'required',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            'appointmemt_type' => 'required',
            'head_position' => 'required',
        ], [
            'department_head.required' => 'select relevent department head',
            'start_date.required' => 'choose department head appointment date',
            'start_date.date' => 'department head appointment date invalid',
            'end_date.date' => 'department head appointment close date invalid',
            'appointmemt_type.required' => 'select the relavent appointment type',
            'head_position.required' => 'select the relavent head position'
        ]);

        //get head email
        $heademail = DB::table('employees')
            ->where('employee_no', $request->department_head)
            ->select('email as value')
            ->get();

        $heademail = json_decode($heademail, true);
        $heademail = $heademail[0]["value"];

        //get head belongs sc
        $operator = Employee::where('employee_no', $request->department_head)
            ->select('assign_ma_user_id as value')
            ->get();

        $operator = json_decode($operator, true);
        $operator = $operator[0]["value"];


        //get faculty dean office status
        $is_faculty_office = Department::where('id', $request->department_id)
            ->select('is_faculty_office as value')
            ->get();

        $is_faculty_office = json_decode($is_faculty_office, true);
        $is_faculty_office = $is_faculty_office[0]["value"];

        //get previous heads
        $previousHead = DB::table('department_heads')
            ->where('department_id', $request->department_id)
            ->select('emp_no as value')
            ->get();

        $previousHead = json_decode($previousHead, true);
        $previousHead = $previousHead[0]["value"];

        //get previous head email
        $headPervEmail = DB::table('users')
            ->where('employee_no', $previousHead)
            ->select('email as value')
            ->get();

        $headPervEmail = json_decode($headPervEmail, true);
        $headPervEmail = $headPervEmail[0]["value"];


        if ($is_faculty_office == 1) {

            //get previous head email
            $facultyDeanId = Faculty::where('faculty_dean_office', $request->department_id)
                ->join('faculty_deans', 'faculty_deans.faculty_id', '=', 'faculties.id')
                ->select('faculty_deans.id as value')
                ->get();

            $facultyDeanId = json_decode($facultyDeanId, true);
            $facultyDeanId = $facultyDeanId[0]["value"];

            if ($request->department_head != $previousHead) {

                //exist department head account disable
                if (DepartmentHead::where('emp_no', $previousHead)->count() > 1 && FacultyDean::where('emp_no', $previousHead)->count() > 1) {
                } elseif (DepartmentHead::where('emp_no', $previousHead)->count() > 1 && FacultyDean::where('emp_no', $previousHead)->count() == 1) {

                    $user = User::where('employee_no', $previousHead)->first();
                    $user->removeRole('dean');
                } else {

                    // record exists
                    $user = User::where('employee_no', $previousHead)->first();

                    if ($user->hasAnyRole(['head', 'dean'])) {

                        $user->removeRole('head');
                        $user->removeRole('dean');
                    }
                }

                //check new department head already have account

                if (User::where('email', $heademail)->exists()) {

                    // record exists
                    $user = User::where('email', $heademail)->first();

                    if (!$user->hasExactRoles(['head', 'dean'])) {

                        $user->assignRole('head', 'dean');
                    } elseif (!$user->hasExactRoles(['head'])) {

                        $user->assignRole('head');
                    } elseif (!$user->hasExactRoles(['dean'])) {

                        $user->assignRole('dean');
                    }

                    //task 01
                    $data = DepartmentHead::find($id);
                    $data->email = $heademail;
                    $data->emp_no = $request->department_head;
                    $data->appointmemt_type = $request->appointmemt_type;
                    $data->head_position = $request->head_position;
                    $data->start_date = date("Y-m-d", strtotime($request->start_date));
                    $data->end_date = date("Y-m-d", strtotime($request->end_date));
                    $data->added_user_id = $operator;
                    $data->active_status = 1;
                    $data->last_update_user = Auth()->user()->employee_no;
                    $data->last_update_date = date('Y-m-d');
                    $data->updated_at = Carbon::now();
                    $data->save();


                    //task 02
                    $data1 = FacultyDean::find($facultyDeanId);
                    $data1->email = $heademail;
                    $data1->emp_no = $request->department_head;
                    $data1->appointmemt_type = $request->appointmemt_type;
                    $data1->start_date = date("Y-m-d", strtotime($request->start_date));
                    $data1->end_date = date("Y-m-d", strtotime($request->end_date));
                    $data1->added_user_id = $operator;
                    $data1->active_status = 1;
                    $data1->updated_at = Carbon::now();
                    $data1->save();

                    //get head email
                    $userId = User::where('email', $heademail)
                        ->select('id as value')
                        ->get();

                    $userId = json_decode($userId, true);
                    $userId = $userId[0]["value"];

                    //department update
                    $dept = Department::where('id', '=', $request->department_id)->first();
                    $dept->head_email = $heademail;
                    $dept->head_user_id = $user->id;
                    $dept->save();

                    //department head update
                    $depth = DepartmentHead::where('id', '=', $data->id)->first();
                    $depth->head_user_id = $user->id;
                    $depth->save();

                    // faculty update
                    $fac = Faculty::where('id', '=', $data1->faculty_id)->first();
                    $fac->dean_email = $heademail;
                    $fac->dean_user_id = $user->id;
                    $fac->save();

                    // faculty dean update
                    $facd = FacultyDean::where('id', '=', $data1->id)->first();
                    $facd->dean_user_id = $user->id;
                    $facd->save();
                } else {

                    //get last name
                    $lastName = Employee::where('employee_no', $request->department_head)
                        ->select('last_name as value')
                        ->get();

                    $lastName = json_decode($lastName, true);
                    $lastName = $lastName[0]["value"];

                    $user = new User();
                    $code = rand(1000, 9999);
                    $user->employee_no = $request->department_head;
                    $user->main_branch_id = 51;
                    $user->name = $lastName;
                    $user->email = $heademail;
                    $user->password = bcrypt($code);
                    $user->status_id = 1;
                    $user->created_user_id = Auth()->user()->employee_no;
                    $user->save();

                    $user->assignRole('user', 'head', 'dean');


                    //task 01
                    $data = DepartmentHead::find($id);
                    $data->email = $heademail;
                    $data->emp_no = $request->department_head;
                    $data->appointmemt_type = $request->appointmemt_type;
                    $data->head_position = $request->head_position;
                    $data->start_date = date("Y-m-d", strtotime($request->start_date));
                    $data->end_date = date("Y-m-d", strtotime($request->end_date));
                    $data->added_user_id = $operator;
                    $data->active_status = 1;
                    $data->last_update_user = Auth()->user()->employee_no;
                    $data->last_update_date = date('Y-m-d');
                    $data->updated_at = Carbon::now();
                    $data->save();

                    //task 02
                    $data1 = FacultyDean::find($facultyDeanId);
                    $data1->email = $heademail;
                    $data1->emp_no = $request->department_head;
                    $data1->appointmemt_type = $request->appointmemt_type;
                    $data1->start_date = date("Y-m-d", strtotime($request->start_date));
                    $data1->end_date = date("Y-m-d", strtotime($request->end_date));
                    $data1->added_user_id = $operator;
                    $data1->active_status = 1;
                    $data1->updated_at = Carbon::now();
                    $data1->save();


                    //department update
                    $dept = Department::where('id', '=', $request->department_id)->first();
                    $dept->head_email = $heademail;
                    $dept->head_user_id = $user->id;
                    $dept->save();

                    //department head update
                    $depth = DepartmentHead::where('id', '=', $data->id)->first();
                    $depth->head_user_id = $user->id;
                    $depth->save();

                    // faculty update
                    $fac = Faculty::where('id', '=', $data1->faculty_id)->first();
                    $fac->dean_email = $heademail;
                    $fac->dean_user_id = $user->id;
                    $fac->save();

                    // faculty dean update
                    $facd = FacultyDean::where('id', '=', $data1->id)->first();
                    $facd->dean_user_id = $user->id;
                    $facd->save();

                    // employee update
                    $emp = Employee::where('employee_no', '=', $request->department_head)->first();
                    $emp->user_account_status = 1;
                    $emp->save();

                }
            } else {

                $data = DepartmentHead::find($id);
                $data->appointmemt_type = $request->appointmemt_type;
                $data->head_position = $request->head_position;
                $data->start_date = date("Y-m-d", strtotime($request->start_date));
                $data->end_date = date("Y-m-d", strtotime($request->end_date));
                $data->active_status = 1;
                $data->last_update_user = Auth()->user()->employee_no;
                $data->last_update_date = date('Y-m-d');
                $data->updated_at = Carbon::now();
                $data->save();

                $data1 = FacultyDean::find($facultyDeanId);
                $data1->appointmemt_type = $request->appointmemt_type;
                $data1->start_date = date("Y-m-d", strtotime($request->start_date));
                $data1->end_date = date("Y-m-d", strtotime($request->end_date));
                $data1->active_status = 1;
                $data1->updated_at = Carbon::now();
                $data1->save();
            }
        } else {

            if ($request->department_head != $previousHead) {

                //exist department head account disable
                if (DepartmentHead::where('emp_no', $previousHead)->count() > 1) {

                    $user = User::where('employee_no', '=', $previousHead)->first();
                    $user->status_id = 1;
                    $user->save();

                } else {

                    // record exists
                    $user = User::where('employee_no', $previousHead)->first();

                    if ($user->hasRole(['head'])) {

                        $user->removeRole('head');
                    }
                }

                //check new department head already have account

                if (User::where('email', $heademail)->exists()) {

                    // record exists
                    $user = User::where('email', $heademail)->first();

                    if (!$user->hasRole(['head'])) {

                        $user->assignRole('head');
                    }


                    $data = DepartmentHead::find($id);
                    $data->email = $heademail;
                    $data->emp_no = $request->department_head;
                    $data->appointmemt_type = $request->appointmemt_type;
                    $data->head_position = $request->head_position;
                    $data->start_date = date("Y-m-d", strtotime($request->start_date));
                    $data->end_date = date("Y-m-d", strtotime($request->end_date));
                    $data->added_user_id = $operator;
                    $data->active_status = 1;
                    $data->last_update_user = Auth()->user()->employee_no;
                    $data->last_update_date = date('Y-m-d');
                    $data->updated_at = Carbon::now();
                    $data->save();

                    //get head email
                    $userId = User::where('email', $heademail)
                        ->select('id as value')
                        ->get();

                    $userId = json_decode($userId, true);
                    $userId = $userId[0]["value"];

                    //department update
                    $dept = Department::where('id', '=', $request->department_id)->first();
                    $dept->head_email = $heademail;
                    $dept->head_user_id = $user->id;
                    $dept->save();

                    //department head update
                    $depth = DepartmentHead::where('id', '=', $data->id)->first();
                    $depth->head_user_id = $user->id;
                    $depth->save();

                } else {

                    //get last name
                    $lastName = Employee::where('employee_no', $request->department_head)->select('last_name as value')->get();

                    $lastName = json_decode($lastName, true);
                    $lastName = $lastName[0]["value"];

                    $user = new User();
                    $code = rand(1000, 9999);
                    $user->employee_no = $request->department_head;
                    $user->main_branch_id = 51;
                    $user->name = $lastName;
                    $user->email = $heademail;
                    $user->password = bcrypt($code);
                    $user->status_id = 1;
                    $user->created_user_id = Auth()->user()->employee_no;
                    $user->save();

                    $user->assignRole('user', 'head');


                    $data = DepartmentHead::find($id);
                    $data->email = $heademail;
                    $data->emp_no = $request->department_head;
                    $data->appointmemt_type = $request->appointmemt_type;
                    $data->head_position = $request->head_position;
                    $data->start_date = date("Y-m-d", strtotime($request->start_date));
                    $data->end_date = date("Y-m-d", strtotime($request->end_date));
                    $data->added_user_id = $operator;
                    $data->active_status = 1;
                    $data->last_update_user = Auth()->user()->employee_no;
                    $data->last_update_date = date('Y-m-d');
                    $data->updated_at = Carbon::now();
                    $data->save();


                    //department update
                    $dept = Department::where('id', '=', $request->department_id)->first();
                    $dept->head_email = $heademail;
                    $dept->head_user_id = $user->id;
                    $dept->save();

                    //department head update
                    $depth = DepartmentHead::where('id', '=', $data->id)->first();
                    $depth->head_user_id = $user->id;
                    $depth->save();

                    // employee update
                    $emp = Employee::where('employee_no', '=', $request->department_head)->first();
                    $emp->user_account_status = 1;
                    $emp->save();


                }
            } else {


                $data = DepartmentHead::find($id);
                $data->appointmemt_type = $request->appointmemt_type;
                $data->head_position = $request->head_position;
                $data->start_date = date("Y-m-d", strtotime($request->start_date));
                $data->end_date = date("Y-m-d", strtotime($request->end_date));
                $data->active_status = 1;
                $data->last_update_user = Auth()->user()->employee_no;
                $data->last_update_date = date('Y-m-d');
                $data->updated_at = Carbon::now();
                $data->save();
            }
        }


        $notification = array(
            'message' => 'Department Head data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('department.head.index')->with($notification);
    }

    public function departmentHeadDelete($id)
    {

        $depHeadId = decrypt($id);
        $head = DepartmentHead::find($depHeadId);
        $head->delete();
        //department data update
        $dept = Department::where('id', '=', $head->department_id)->first();
        $dept->head_email = '';
        $dept->head_user_id = 0;
        $dept->save();

        //get previous head email
        $facultyDeanId = Faculty::where('faculty_dean_office', $head->department_id)
            ->join('faculty_deans', 'faculty_deans.faculty_id', '=', 'faculties.id')
            ->select('faculty_deans.id as value')
            ->get();

        if (count($facultyDeanId) == 1) {

            $facultyDeanId = json_decode($facultyDeanId, true);
            $facultyDeanId = $facultyDeanId[0]["value"];

            $dean = FacultyDean::find($facultyDeanId);
            $dean->delete();

            //faculty data update
            $fac = Faculty::where('id', '=', $dean->faculty_id)->first();
            $fac->dean_email = '';
            $fac->dean_user_id = 0;
            $fac->save();
        }

        $notification = array(
            'message' => 'Department Head data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('department.head.index')->with($notification);
    }
}
