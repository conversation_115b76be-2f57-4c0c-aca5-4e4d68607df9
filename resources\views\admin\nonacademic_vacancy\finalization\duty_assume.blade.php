@extends('admin.admin_master')
@section('admin')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<section class="content">
    <div class="col-12">
      <form action="{{ route('nac.applicant.duty.assume.store') }}" method="POST" id="myForm">
        @csrf
        <div class="card card-primary">
             <div class="content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-12 text-center">
                    <h5 class="mb-1">
                        {{ strtoupper($vacancy->designations->designation_name) }} @if ($vacancy->display_name != '' && $vacancy->designation_category == 138)
                            ({{ strtoupper($vacancy->display_name) }})
                        @endif
                        -
                        <b>
                            <font style="font-size: 20px;">(
                                @if ($vacancy->designation_category == 138)
                                    Permanent
                                @elseif ($vacancy->designation_category == 140)
                                    Temporary
                                @elseif ($vacancy->designation_category == 141)
                                    Contract
                                @elseif ($vacancy->designation_category == 142)
                                    Assignment Basis
                                @endif
                                )
                            </font>
                        </b>
                    </h5>

                    <h6 align="center">
                        Application for @if ($vacancy->designation_category == 138)
                            Permanent
                        @elseif ($vacancy->designation_category == 140)
                            Temporary
                        @elseif ($vacancy->designation_category == 141)
                            Contract
                        @elseif ($vacancy->designation_category == 142)
                            Assignment Basis
                        @endif Non-Academic Positions
                    </h6>
                    <font style="font-size: 15px;">Application ID : <b class="text-danger"
                            style="font-size: 25px;">{{ str_pad($applicationForm->id, 3, '0', STR_PAD_LEFT) }}
                        </b> &nbsp;&nbsp;&nbsp;&nbsp;Submit Date : <b
                            class="text-dark">{{ date_format($applicationForm->created_at, 'd-M-Y') }}</b>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                    </font>
                </div><!-- /.col -->
            </div><!-- /.row -->
        </div><!-- /.container-fluid -->
    </div>
          <div class="card-body">
            <div class="row">
              {{-- <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label>Employee No. : <span style="color: #ff0000;">*</span></label>
                  <input type="number" name="employee_no" class="form-control" id="employee_no" value="{{ $editData->id }}" readonly/>
                   <span class="text-danger">@error('employee_no'){{$message}}@enderror</span>
                </div>
              </div> --}}
              <input type="hidden" name="employee_work_type" value="138" />
              <div class="col col-md-4 col-sm-12">
                <div class="form-group">
                  <label>NIC No. : <span style="color: #ff0000;">*</span></label>
                  <input type="text" name="nic" class="form-control" id="nic" value="{{ $applicationForm->nic }}"/>
                  <span class="text-danger">@error('nic'){{$message}}@enderror</span>
                </div>
              </div>
                  <div class="col col-md-4 col-sm-12">
                    <div class="form-group">
                      <label>Title: <span style="color: #ff0000;">*</span></label>
                      <select name="title" id="titel_id" class="select2bs4" style="width: 100%">
                        <option value="" selected disabled>Select Title</option>
                        @foreach($titles as $title)
                        <option value="{{ $title->id}}" {{ $title->id == $applicationForm->title ? 'selected' : '' }}>{{ ucfirst($title->category_name)}}</option>
                        @endforeach
                      </select>
                      <span class="text-danger">@error('title'){{$message}}@enderror</span>
                    </div>
                  </div>
                  <div class="col col-md-4 col-sm-12">
                    <div class="form-group">
                      <label>Initials: <span style="color: #ff0000;">*</span></label>
                      <input type="text" name="name_with_initials" class="form-control" id="initials" value="{{ $applicationForm->name_with_initials }}" placeholder="Ex:- R.M.L."/>
                      <span class="text-danger">@error('name_with_initials'){{$message}}@enderror</span>
                    </div>
                  </div>
            </div>
            <div class="row">
              <div class="col col-md-8 col-sm-12">
                <div class="form-group">
                  <label>Name Denoted By Initials : <span style="color: #ff0000;">*</span></label>
                  <input type="text" name="name_denoted_by_initials" class="form-control" id="name_denoted_by_initials" value="{{ $applicationForm->name_denoted_by_initials }}"/>
                  <span class="text-danger">@error('name_denoted_by_initials'){{$message}}@enderror</span>
                </div>
              </div>
              <div class="col col-md-4 col-sm-12">
                <div class="form-group">
                  <label>Last Name: <span style="color: #ff0000;">*</span></label>
                  <input type="text" name="last_name" class="form-control" id="last_name" value="{{ $applicationForm->last_name }}"/>
                  <span class="text-danger">@error('last_name'){{$message}}@enderror</span>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Mobile No.: <span style="color: #ff0000;">*</span></label>
                  <input type="text" name="telephone_mobile" class="form-control" id="mobile_no" value="{{ $applicationForm->telephone_mobile }}" placeholder="Ex:-0777777777"/>
                  <span class="text-danger">@error('telephone_mobile'){{$message}}@enderror</span>
                </div>
              </div>
              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Residence Phone No: </label>
                  <input type="text" name="telephone_residence" class="form-control" id="phone_no" value="{{ $applicationForm->telephone_residence }}" placeholder="Ex:-0112222222"/>
                  <span class="text-danger">@error('telephone_residence'){{$message}}@enderror</span>
                </div>

              </div>
            </div>
            <div class="row">
                  <div class="col col-md-6 col-sm-12">
                    <div class="form-group">
                      <label>Personal Email (Non SJP Mail): <span style="color: #ff0000;">*</span></label>
                      <input type="email" name="email_address" id="personal_email" class="form-control" value="{{ $applicationForm->email_address }}"/>
                      <span class="text-danger">@error('email_address'){{$message}}@enderror</span>
                    </div>
                  </div>

                  <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Date of Birth: <span style="color: #ff0000;">*</span></label>
                    <div class="input-group date" id="date_of_birth" data-target-input="nearest">

                        <input type="text" class="form-control datetimepicker-input" data-target="#date_of_birth" name="date_of_birth" id="date_of_birth_val" value="{{ date("d-M-Y", strtotime($applicationForm->date_of_birth)) }}" placeholder="Ex:- 01-Jan-2000" data-target="#date_of_birth" data-toggle="datetimepicker" readonly>

                        <div class="input-group-append" data-target="#date_of_birth" data-toggle="datetimepicker">
                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                        </div>
                    </div>
                    <span class="text-danger">@error('date_of_birth'){{$message}}@enderror</span>
                </div>
              </div>
            </div>
            <div class="row">

              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Gender: <span style="color: #ff0000;">*</span></label>
                  <select name="gender_id" id="gender_id" class="select2bs4" style="width: 100%">
                    <option value="" selected disabled>Select Gender</option>
                    @foreach($genders as $gender)
                    <option value="{{ $gender->id}}" {{ $gender->id == old('gender_id')  ? 'selected' : '' }}>{{ ucfirst($gender->category_name)}}</option>
                    @endforeach
                  </select>
                  <span class="text-danger">@error('gender_id'){{$message}}@enderror</span>
                </div>
              </div>

              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Civil Status: <span style="color: #ff0000;">*</span></label>
                  <select name="civil_status" id="civil_status_id" class="select2bs4" style="width: 100%">
                    <option value="" selected disabled>Select Civil Status</option>
                    @foreach($civilStatuses as $civilStatus)
                    <option value="{{ $civilStatus->id}}" {{ $civilStatus->id == $applicationForm->civil_status ? 'selected' : '' }}>{{ ucfirst($civilStatus->category_name)}}</option>
                    @endforeach
                  </select>
                  <span class="text-danger">@error('civil_status'){{$message}}@enderror</span>
                </div>
              </div>

            </div>


            <div class="row">
              <div class="col col-md-12">
                <label><strong>Permanent Address</strong></label>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label class="font-weight-normal">Address Line 1: <span style="color: #ff0000;">*</span></label>
                  <input type="text" name="permanent_address_line1" class="form-control" id="permanent_add1" value="{{ $applicationForm->permanent_address_line1 }}"/>
                  <span class="text-danger">@error('permanent_address_line1'){{$message}}@enderror</span>
                </div>
              </div>
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label class="font-weight-normal">Address Line 2:</label>
                  <input type="text" name="permanent_address_line2" class="form-control" id="permanent_add2" value="{{ $applicationForm->permanent_address_line2 }}"/>
                </div>
              </div>
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label class="font-weight-normal">Address Line 3:</label>
                  <input type="text" name="permanent_address_line3" class="form-control" id="permanent_add3" value="{{ $applicationForm->permanent_address_line3 }}"/>
                </div>
              </div>
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label class="font-weight-normal">City: <span style="color: #ff0000;">*</span></label>
                  <select name="permanent_address_city" id="permanent_city_id" class="select2bs5" style="width: 100%">
                    <option value="" selected disabled>Select City</option>
                    @foreach($cities as $city)
                    <option value="{{ $city->id }}" {{ $city->id == $applicationForm->permanent_address_city ? 'selected' : '' }}>{{ ucfirst($city->name_en)}}</option>
                    @endforeach
                  </select>
                  <span class="text-danger">@error('permanent_address_city'){{$message}}@enderror</span>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-12 col-sm-12">
                <label><strong>Postal Address</strong></label>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-12 col-sm-12">
                <label class="font-weight-normal">Same as Permanent Address:</label> <input type="checkbox" id="checkbox1" >
              </div>
            </div>
              <div class="row">
                <div class="col col-md-3 col-sm-12">
                  <div class="form-group">
                    <label class="font-weight-normal">Address Line 1: <span style="color: #ff0000;">*</span></label>
                    <input type="text" name="postal_address_line1" class="form-control" id="postal_add1" value="{{ $applicationForm->postal_address_line1 }}"/>
                    <span class="text-danger">@error('postal_add1'){{$message}}@enderror</span>
                  </div>
                </div>
                <div class="col col-md-3 col-sm-12">
                  <div class="form-group">
                    <label class="font-weight-normal">Address Line 2:</label>
                    <input type="text" name="postal_address_line2" class="form-control" id="postal_add2" value="{{ $applicationForm->postal_address_line2 }}"/>
                  </div>
                </div>
                <div class="col col-md-3 col-sm-12">
                  <div class="form-group">
                    <label class="font-weight-normal">Address Line 3:</label>
                    <input type="text" name="postal_address_line3" class="form-control" id="postal_add3" value="{{ $applicationForm->postal_address_line3 }}"/>
                  </div>
                </div>
                <div class="col col-md-3 col-sm-12">
                  <div class="form-group">
                    <label class="font-weight-normal">City: <span style="color: #ff0000;">*</span></label>
                    <select name="postal_address_city" id="postal_city_id" class="select2bs5" style="width: 100%">
                      <option value="" selected>Select City</option>
                      @foreach($cities as $city)
                      <option value="{{ $city->id}}" {{ $city->id == $applicationForm->postal_address_city ? 'selected' : '' }}>{{ ucfirst($city->name_en)}}</option>
                      @endforeach
                    </select>
                    <span class="text-danger">@error('postal_address_city'){{$message}}@enderror</span>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col col-md-4 col-sm-12">
                  <div class="form-group">
                    <label>Citizenship of Applicant: <span style="color: #ff0000;">*</span></label>
                    <input type="text" name="citizenship" class="form-control" id="citizenship" placeholder="Ex:-Sri Lankan" value="Sri Lankan"/>
                    <span class="text-danger">@error('citizenship'){{$message}}@enderror</span>
                  </div>
                </div>
                <div class="col col-md-4 col-sm-12">
                  <div class="form-group">
                    <label>If a Citizen of Sri Lanka, how obtained :<span style="color: #ff0000;">*</span></label>
                    <select name="citizen_sri_lanka_obtained" id="state_of_citizenship_id" class="select2bs4" style="width: 100%">
                      <option value="" selected disabled>Select Citizenship Type</option>
                      @foreach($citizenships as $citizenship)
                      <option value="{{ $citizenship->category_name }}" {{ $citizenship->category_name == $applicationForm->citizen_sri_lanka_obtained ? 'selected' : '' }}>{{ ucfirst($citizenship->category_name)}}</option>
                      @endforeach
                    </select>
                    <span class="text-danger">@error('citizen_sri_lanka_obtained'){{$message}}@enderror</span>
                  </div>
                </div>
                @if($applicationForm->citizen_sri_lanka_obtained == 'By Registration')
                <div class="col col-md-4 col-sm-12">
                  <div class="form-group"  id="reg_num" style="display:show;">
                    <label>Citizen Registration No.: <span style="color: #ff0000;">*</span></label>
                    <input type="text" name="citizen_registration_no" class="form-control" id="citizen_registration_no" value="{{ $applicationForm->citizen_registration_no }}"/>
                    <span class="text-danger">@error('citizen_registration_no'){{$message}}@enderror</span>
                  </div>
                </div>
                @endif
              </div>

        </div>
        <br>
        <div class="card card-outline card-primary" >
          <div class="card-body">

            <div class="row">

              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Designation: <span style="color: #ff0000;">*</span></label>
                  <select name="designation_id" id="designation_id" class="select2bs5" style="width: 100%">
                    @foreach($designations as $designation)
                    @if($designation->id ==  $vacancy->designation_id)
                    <option value="{{ $designation->id}}" selected>{{ ucfirst($designation->designation_name)}} - ( {{ $designation->salary_code }} )  {{$designation->category_name }}</option>
                    @endif
                    @endforeach
                  </select>
                  <span class="text-danger">@error('designation_id'){{$message}}@enderror</span>
                </div>
              </div>

              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Highest Educational Qualification: <span style="color: #ff0000;">*</span></label>
                  <select name="academic_qualification" id="emp_highest_edu_level" class="select2bs5" style="width: 100%">
                    <option value="" selected disabled>Select Highest Educational Qualification</option>
                    @foreach($educationLevels as $educationLevel)
                    <option value="{{ $educationLevel->category_name}}" {{ $educationLevel->category_name ==  $applicationForm->academic_qualification ? 'selected' : '' }}>{{ ucfirst($educationLevel->category_name )}}</option>
                    @endforeach
                  </select>
                  <span class="text-danger">@error('academic_qualification'){{$message}}@enderror</span>
                </div>
              </div>

            </div>

            <div class="row">
                <div class="col col-md-4 col-sm-12">
                <h3 class="card-title"><b>Working Location</b></h3>
                </div>
              </div>
            <div class="row">
              <div class="col col-md-4 col-sm-12">
                <div class="form-group">
                  <label class="font-weight-normal">Faculty: <span class="text-danger"> *</span></label>
                  <select name="faculty_id" id="faculty_id" class="select2bs5" style="width: 100%" >
                    <option value="" selected disabled>Select Faculty</option>
                    @foreach($faculties as $faculty)
                    <option value="{{ $faculty->id}}" {{ $faculty->id ==  old('faculty_id') ? 'selected' : '' }}>{{ ucfirst($faculty->faculty_name)}}</option>
                    @endforeach
                  </select>
                  <span class="text-danger">@error('faculty_id'){{$message}}@enderror</span>
                </div>
                </div><!-- col-md-6 -->
                <div class="col col-md-4 col-sm-12">
                  <div class="form-group">
                    <label class="font-weight-normal">Department: <span class="text-danger"> *</span></label>
                    <div class="controls">
                      <select name="department_id" id="department_id" class="select2bs5" style="width: 100%" >
                        <option value="" selected="" disabled="">Select Department</option>
                        @if (old('department_id') != 0)
                        @foreach($departments as $department)
                              @if (old('department_id') ==  $department->id )
                              <option value="{{ $department->id }}" selected>{{ ucfirst($department->department_name)}}</option>
                              @else
                              <option value="{{ $department->id }}">{{ ucfirst($department->department_name)}}</option>
                              @endif
                        @endforeach

                        @endif
                      </select>

                     <span class="text-danger">@error('department_id'){{$message}}@enderror</span>

                     </div>
                       </div>
                  </div><!-- col-md-6 -->
                  <div class="col col-md-4 col-sm-12">
                    <div class="form-group">
                        <label class="font-weight-normal">Sub Department: </label>
                      <div class="controls">
                        <select name="sub_department_id" id="sub_department_id" class="select2bs5" style="width: 100%">
                          <option value="" selected="" disabled="">Select Sub Department</option>
                          @if (old('sub_department_id') != 0)
                        @foreach($subDepartments as $subDepartment)
                              @if (old('sub_department_id') == $subDepartment->sub_department_code )
                              <option value="{{ $subDepartment->sub_department_code }}" selected>{{ ucfirst( $subDepartment->sub_departmet_name )}}</option>
                              @else
                              <option value="{{ $subDepartment->sub_department_code }}">{{ ucfirst($subDepartment->sub_departmet_name)}}</option>
                              @endif
                        @endforeach

                        @endif
                        </select>

                       <span class="text-danger">@error('sub_department_id'){{$message}}@enderror</span>

                       </div>
                         </div>
                    </div><!-- col-md-6 -->

                  </div><!-- row -->
                  <div class="row">
                    <div class="col col-md-4 col-sm-12">
                    <h3 class="card-title"><b>Carder Location</b></h3>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col col-md-4 col-sm-12">
                      <div class="form-group">
                        <label class="font-weight-normal">Faculty: <span class="text-danger"> *</span></label>
                        <select name="carder_faculty_id" id="carder_faculty_id" class="select2bs5" style="width: 100%" >
                          <option value="" selected disabled>Select Faculty</option>
                          @foreach($faculties as $faculty)
                          <option value="{{ $faculty->id}}" {{ $faculty->id ==  old('carder_faculty_id') ? 'selected' : '' }}>{{ ucfirst($faculty->faculty_name)}}</option>
                          @endforeach
                        </select>
                        <span class="text-danger">@error('carder_faculty_id'){{$message}}@enderror</span>
                      </div>
                      </div><!-- col-md-6 -->
                      <div class="col col-md-4 col-sm-12">
                        <div class="form-group">
                            <label class="font-weight-normal">Department: <span class="text-danger"> *</span></label>
                          <div class="controls">
                            <select name="carder_department_id" id="carder_department_id" class="select2bs5" style="width: 100%" >
                              <option value="" selected="" disabled="">Select Department</option>
                              @if (old('carder_department_id') != 0)
                              @foreach($CarderDepartment as $department)
                                    @if (old('carder_department_id') ==  $department->id )
                                    <option value="{{ $department->id }}" selected>{{ ucfirst($department->department_name)}}</option>
                                    @else
                                    <option value="{{ $department->id }}">{{ ucfirst($department->department_name)}}</option>
                                    @endif
                              @endforeach

                              @endif
                            </select>

                           <span class="text-danger">@error('carder_department_id'){{$message}}@enderror</span>

                           </div>
                             </div>
                        </div><!-- col-md-6 -->
                        <div class="col col-md-4 col-sm-12">
                          <div class="form-group">
                            <label class="font-weight-normal">Sub Department: </label>
                            <div class="controls">
                              <select name="carder_sub_department_id" id="carder_sub_department_id" class="select2bs5" style="width: 100%">
                                <option value="" selected="" disabled="">Select Sub Department</option>
                                @if (old('carder_sub_department_id') != 0)
                              @foreach($CarderSubDepartment as $subDepartment)
                                    @if (old('carder_sub_department_id') == $subDepartment->sub_department_code )
                                    <option value="{{ $subDepartment->sub_department_code }}" selected>{{ ucfirst( $subDepartment->sub_departmet_name )}}</option>
                                    @else
                                    <option value="{{ $subDepartment->sub_department_code }}">{{ ucfirst($subDepartment->sub_departmet_name)}}</option>
                                    @endif
                              @endforeach

                              @endif
                              </select>

                             <span class="text-danger">@error('carder_sub_department_id'){{$message}}@enderror</span>

                             </div>
                               </div>
                          </div><!-- col-md-6 -->

                        </div><!-- row -->



        </div>
        </div>

        <br>
                <div style="background-color: #fffccd;">

                        @csrf
                        <div class="card-header">
                            <h3><b>Duty Assumption of Applicant</b></h3>
                        </div>
                        <div class="card-body">
                            <input type="hidden" name="app_id" value="{{ $applicationForm->id }}">
                            <input type="hidden" name="designation_category" id ="designation_category" value="{{ $vacancy->designation_category }}">

                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>Did the applicant assume duty?</label>
                                        <div class="row">
                                            <div class="col-1"></div>
                                            <div class="col-2">
                                                <input class="form-check-input" type="radio" name="recom"
                                                    id="rYes" value="2" onclick='not_recom();'
                                                    {{ $applicationForm->duty_assume_status == 2 ? 'checked' : '' }}>
                                                <label class="form-check-label" for="flexRadioDefault1">Yes</label>
                                            </div>
                                            <div class="col-2">
                                                <input class="form-check-input" type="radio" name="recom"
                                                    id="rNo" value="1" onclick='not_recom();'
                                                    {{ $applicationForm->duty_assume_status == 1 ? 'checked' : '' }}>
                                                <label class="form-check-label" for="flexRadioDefault1">No</label>
                                            </div>
                                        </div>
                                        <span class="text-danger">
                                            @error('recom')
                                                {{ $message }}
                                            @enderror
                                        </span>
                                        <br>
                                    </div>
                                </div>
                            </div>

                            <div class="row" id="notRecom"
                                style="{{ $applicationForm->duty_assume_status == 1 ? 'display:block;' : 'display:none;' }}">

                                <div class="col col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <label>File Reference No:</label>
                                        <input type="text" name="file_reference_number" class="form-control"
                                            id="file_reference_number" value="{{ old('file_reference_number') }}" />
                                    </div>
                                </div>

                                <div class="col col-md-6 col-sm-6">
                                    <div class="form-group">
                                        <label>Initial Basic Salary: <span style="color: #ff0000;">*</span></label>
                                        <input type="number" name="current_basic_salary" class="form-control" id="bsal"
                                            value="{{ old('current_basic_salary') }}" />
                                        @php
                                            $salaryScale = App\Models\SalaryScale::Join(
                                                'designations',
                                                'salary_scales.id',
                                                'designations.salary_scale',
                                            )->join('salary_scale_versions', 'salary_scale_versions.id', '=', 'salary_scales.salary_scale_version_id')
                                                ->select('salary_scales.salary_scale_txt as value')
                                                ->where('designations.id', $vacancy->designation_id)
                                                ->where('salary_scale_versions.status', 1)
                                                ->first();

                                            if ($salaryScale) {
                                                $salaryScale = $salaryScale->value;
                                            } else {
                                                $salaryScale = 'N/A';
                                            }

                                        @endphp
                                        <span class="text-dark"> Salary Scale - {{ $salaryScale }}</span>
                                    </div>
                                </div>

                                <div class="col col-md-6 col-sm-6">
                                    <div class="form-group">
                                        <label>Duty Assumed Date: <span class="text-danger"> *</span></label>
                                        <div class="input-group date" id="date_opened" data-target-input="nearest">
                                            <input type="text" class="form-control datetimepicker-input"
                                                data-target="#date_opened" name="duty_assume_date"
                                                value="{{ old('duty_assume_date') }}" id="duty_assume_date"
                                                placeholder="Ex:- 01-Jan-2000" data-target="#date_opened"
                                                data-toggle="datetimepicker">
                                            <div class="input-group-append" data-target="#date_opened"
                                                data-toggle="datetimepicker">
                                                <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                            </div>
                                        </div>
                                        <span class="text-danger">
                                            @error('duty_assume_date')
                                                {{ $message }}
                                            @enderror
                                        </span>
                                    </div>
                                </div>

                                @if($vacancy->designation_category != 138)
                                <div class="col col-md-6 col-sm-6">
                                    <div class="form-group">
                                        <label>Service Termination Date: <span class="text-danger"> *</span></label>
                                        <div class="input-group date" id="date_closed" data-target-input="nearest">
                                            <input type="text" class="form-control datetimepicker-input"
                                                data-target="#date_closed" name="salary_termination_date"
                                                value="{{ old('salary_termination_date') }}" id="salary_termination_date"
                                                placeholder="Ex:- 01-Jan-2000" data-target="#date_closed"
                                                data-toggle="datetimepicker">
                                            <div class="input-group-append" data-target="#date_closed"
                                                data-toggle="datetimepicker">
                                                <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                            </div>
                                        </div>
                                        <span class="text-danger">
                                            @error('salary_termination_date')
                                                {{ $message }}
                                            @enderror
                                        </span>
                                    </div>
                                </div>
                               <div class="col col-md-6 col-sm-6">
                                    <div class="form-group">
                                        <label>Salaray Payment Type: <span style="color: #ff0000;">*</span></label>
                                        <select name="salary_payment_type" id="salary_payment_type" class="select2bs5" style="width: 100%">
                                            <option value="" selected disabled>Select Salary Payment Type</option>
                                            @foreach($salaryPaymentTypes as $salaryPaymentType)
                                            <option value="{{ $salaryPaymentType->id}}" {{ $salaryPaymentType->id == old('salary_payment_type') ?
                                                'selected' : '' }}>{{ ucfirst($salaryPaymentType->category_name )}}</option>
                                            @endforeach
                                        </select>
                                        <span class="text-danger">@error('salary_payment_type'){{$message}}@enderror</span>
                                    </div>
                                </div>
                                @endif

                            </div>


                        </div>

                        <!-- /.card-body -->
                        <div class="card-footer">

                            <input type="submit" class="btn btn-danger btn-lg float-right ml-2" value="Confirm"
                                name="submit" style="background-color:#990000;">
                            <a href="{{ route('nac.vacancy.interview.completed.application.list',encrypt($vacancy->id)) }}"
                                class="btn btn-secondary btn-lg float-right">Back</a>
                        </div>
                    </form>
                </div>

      </form>
    </div>
  </section>

  <script type="text/javascript">

    $(document).ready(function () {

      $("#checkbox1").on("change",function(){

       if (this.checked ) {
            $("#postal_add1").val($("#permanent_add1").val());
            $("#postal_add2").val($("#permanent_add2").val());
            $("#postal_add3").val($("#permanent_add3").val());

           var skillsSelect = document.getElementById("permanent_city_id");
           var selectedText = skillsSelect.options[skillsSelect.selectedIndex].text;
           var value=$("#permanent_city_id option:selected").val();

           $("#postal_city_id").append("<option value='"+value+"' selected>"+selectedText+"</option>");

           $('#postal_add1').attr('readonly', true);
           $('#postal_add2').attr('readonly', true);
           $('#postal_add3').attr('readonly', true);
           //$('#postal_city_id').attr('selected', true);

        } else {

        $('#postal_add1').val("");
        $('#postal_add2').val("");
        $('#postal_add3').val("");
        document.getElementById("postal_city_id").removeChild(document.getElementById("postal_city_id").lastElementChild); //remove the last append option

           $('#postal_add1').attr('readonly', false);
           $('#postal_add2').attr('readonly', false);
           $('#postal_add3').attr('readonly', false);
           //$('#postal_city_id').attr('selected', false);

      }

      });



      function insert(str, index, value) {
         return str.substr(0, index) + value + str.substr(index);
      }/*add to sub string existing string*/

      function zeroPad(num, places) {
           var zero = places - num.toString().length + 1;
           return Array(+(zero > 0 && zero)).join("0") + num;
      }/*Add leading zeros*/

      // Helper function to get the abbreviation of the month
       function getMonthAbbreviation(month) {
         var months = [
           'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
           'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return months[month - 1];
       }

        $('#applicant_form select[name="state_of_citizenship_id"]').change(function () {
            if ($('#applicant_form select[name="state_of_citizenship_id"] option:selected').val() == 26) {
                $('#reg_num').show();
            } else {
                $('#reg_num').hide();
            }
        });

        $('#faculty_id').on('change', function(){
                var faculty_id = $(this).val();
                  if(faculty_id) {
                      $.ajax({
                          url: "{{  url('/department/load/ajax') }}/"+faculty_id,
                          type:"GET",
                          dataType:"json",
                            success:function(data) {
                                var d =$('select[name="department_id"]').empty();
                                $.each(data, function(key, value){
                                $('select[name="department_id"]').append('<option value="'+ value.id +'">' + value.department_name + '</option>');
                             });

                              },
                         });
                      }else {
                alert('danger');
            }
          });

          $('select[name="department_id"]').on('change', function(){
            var department_id = $(this).val();
            if(department_id) {
                $.ajax({
                    url: "{{  url('/sub/department/load/ajax') }}/"+department_id,
                    type:"GET",
                    dataType:"json",
                    success:function(data) {
                       var d =$('select[name="sub_department_id"]').empty();

                          $.each(data, function(key, value){
                              $('select[name="sub_department_id"]').append('<option value="'+ value.sub_department_code +'">' + value.sub_departmet_name + '</option>');
                          });

                          $('select[name="sub_department_id"]').append('<option value="">None of Above Sub Department</option>');
                    },
                });
            } else {
                alert('danger');
            }
        });

        $('#carder_faculty_id').on('change', function(){
                var faculty_id = $(this).val();
                  if(faculty_id) {
                      $.ajax({
                          url: "{{  url('/department/load/ajax') }}/"+faculty_id,
                          type:"GET",
                          dataType:"json",
                            success:function(data) {
                                var d =$('select[name="carder_department_id"]').empty();
                                $.each(data, function(key, value){
                                $('select[name="carder_department_id"]').append('<option value="'+ value.id +'">' + value.department_name + '</option>');
                             });

                              },
                         });
                      }else {
                alert('danger');
            }
          });

          $('select[name="carder_department_id"]').on('change', function(){
            var department_id = $(this).val();
            if(department_id) {
                $.ajax({
                    url: "{{  url('/sub/department/load/ajax') }}/"+department_id,
                    type:"GET",
                    dataType:"json",
                    success:function(data) {
                       var d =$('select[name="carder_sub_department_id"]').empty();

                          $.each(data, function(key, value){
                              $('select[name="carder_sub_department_id"]').append('<option value="'+ value.sub_department_code +'">' + value.sub_departmet_name + '</option>');
                          });

                          $('select[name="carder_sub_department_id"]').append('<option value="">None of Above Sub Department</option>');
                    },
                });
            } else {
                alert('danger');
            }
        });

          $('select[name="employee_status_id"]').on('change', function(){
                var employee_status_id = $(this).val();
                  if(employee_status_id) {
                      $.ajax({
                          url: "{{  url('/employee/status/type/load/ajax') }}/"+employee_status_id,
                          type:"GET",
                          dataType:"json",
                            success:function(data) {
                                var d =$('select[name="employee_status_type_id"]').empty();
                                $.each(data, function(key, value){
                                $('select[name="employee_status_type_id"]').append('<option value="'+ value.id +'">' + value.category_name + '</option>');
                             });
                              },
                         });
                      }
          });


          $('select[name="designation_id"]').on('change', function(){
                var designation_id = $(this).val();
                if(designation_id) {
                      $.ajax({
                          url: "{{  url('/designation/main/group/load/ajax') }}/"+designation_id,
                          type:"GET",
                          dataType:"json",
                            success:function(data) {
                              $.each(data, function(key, value){
                                if (value.main_group == 97) {

                                 var dob = document.getElementById("date_of_birth_val").value;
                                 if (typeof dob !== 'undefined' && dob) {

                                 var year = new Date(dob).getFullYear() + 65;
                                 var month = new Date(dob).toLocaleString('en-US', {month: 'short'});
                                 var date = new Date(dob).getDate();

                                 var rdate = zeroPad(date, 2)+'-'+month+'-'+year;
                                 $("#retirement_date_val").val(rdate);
                                }


                                } else if (value.main_group == 98) {

                                 var dob = document.getElementById("date_of_birth_val").value;
                                 if (typeof dob !== 'undefined' && dob) {

                                   var year = new Date(dob).getFullYear() + 60;
                                   var month = new Date(dob).toLocaleString('en-US', {month: 'short'});
                                  var date = new Date(dob).getDate();

                                  var rdate = zeroPad(date, 2)+'-'+month+'-'+year;
                                  $("#retirement_date_val").val(rdate);
                                  }
                                }
                              });

                              },
                         });
                      }
          });
    });

    </script>
    <script type="text/javascript">
        $(document).ready(function() {

            $("#checkbox3").on("change",function(){

        if (this.checked ) {
          $("#gratuity_cal_date_val").val($("#initial_appointment_date_val").val());
        } else {

          $('#gratuity_cal_date_val').val("");
        }

      });


        });

        function not_recom() {

            if (document.getElementById("rNo").checked) {
                document.getElementById("notRecom").style.display = "none";

            } else if (document.getElementById("rYes").checked) {
                document.getElementById("notRecom").style.display = "block";

            }

        }

        document.addEventListener("DOMContentLoaded", function() {
            var form = document.getElementById("myForm"); // Replace "myForm" with the actual ID of your form

            if (form) {
                form.addEventListener("submit", function(e) {
                    var radioNotSelect = document.getElementById("rNo");
                    var radioSelect = document.getElementById("rYes");
                    var initial_appointment_date = document.getElementById("initial_appointment_date_val");
                    var gratuity_cal_date = document.getElementById("gratuity_cal_date_val");
                    var duty_assume_date = document.getElementById("duty_assume_date");
                    var bsal = document.getElementById("bsal");
                    var designation_category = document.getElementById("designation_category");
                    var salary_termination_date = document.getElementById("salary_termination_date");
                    var salary_payment_type = document.getElementById("salary_payment_type");
                    //alert(designation_category);

                    if (!radioNotSelect.checked && !radioSelect.checked) {
                        e.preventDefault(); // Prevent form submission
                        Swal.fire({
                            icon: 'error',
                            title: 'Error! Incomplete information',
                            text: 'You must select applicant duty assume status',
                        });
                    }else if (radioSelect.checked && bsal.value.trim() === "") {
                        e.preventDefault(); // Prevent form submission
                        Swal.fire({
                            icon: 'error',
                            title: 'Error! Incomplete information',
                            text: 'Applicant initial basic salary is required',
                        });
                    }else if (radioSelect.checked && duty_assume_date.value.trim() === "") {
                        e.preventDefault(); // Prevent form submission
                        Swal.fire({
                            icon: 'error',
                            title: 'Error! Incomplete information',
                            text: 'Duty Assume date of the appointment is required',
                        });
                    }else if (radioSelect.checked && salary_termination_date.value.trim() === "" && designation_category != 138) {
                        e.preventDefault(); // Prevent form submission
                        Swal.fire({
                            icon: 'error',
                            title: 'Error! Incomplete information',
                            text: 'Service termination date of the appointment is required',
                        });
                    }else if (radioSelect.checked && salary_payment_type.value.trim() === "" && designation_category != 138) {
                        e.preventDefault(); // Prevent form submission
                        Swal.fire({
                            icon: 'error',
                            title: 'Error! Incomplete information',
                            text: 'salary payment method of the appointment is required',
                        });
                    }

                });
            }
        });
    </script>
@endsection
