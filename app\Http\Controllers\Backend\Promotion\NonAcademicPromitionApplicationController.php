<?php

namespace App\Http\Controllers\Backend\Promotion;

use App\Http\Controllers\Controller;
use App\Mail\NcaPromoApplicationSubmit;
use App\Models\Employee;
use App\Models\PromotionApplicationsNonacademic;
use App\Models\promotionDuration;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;


class NonAcademicPromitionApplicationController extends Controller
{
    //page1 controllers
    public function openPage1(Request $request)
    {

        if (isset($request->empNo)) {

            $empNo = $request->empNo;

            return view('admin.promotion.nonAcademic_application.page1', compact('empNo'));
        } else {

            $message_text_E = "There was a technical error with your application. Please try again. Contact the Non-academic Establishments Division if necessary";
            $message_text_S = "ඔබගේ අයදුම්පත හා සම්බන්ධ තාක්ෂණික දෝෂයක් පැවති. කරුණාකර නැවත උත්සාහ කරන්න. අවශ්‍යනම් අනධ්‍යන ආයතන අංශය සම්බන්ධ කරගන්න";
            return view('admin.promotion.nonAcademic_application.error_page1', compact('message_text_E', 'message_text_S'));
        }
       /*
        $empNo = 11301;
        return view('admin.promotion.nonAcademic_application.page1', compact('empNo'));
        */
    }

    public function index(Request $request)
    {
        $request->validate(
            ['empNo' => 'required'],
            ['empNo.required' => 'Please enter service period ']
        );

        $closingDate = Carbon::parse('2025-02-01');//update closing date 2024
        $today = Carbon::today();

        if ($closingDate < $today) {
            $message_text_E = "Acceptance of applications closed on 31.01.2025. If necessary, contact the non-academic Establishments Division. If you have submitted an application, you can check its progress on the first page.";
            $message_text_S = "අයඳුම්පත් භාරගැනීම 2025.01.31 දින අවසන් කරන ලදී. අවශ්‍ය වන්නේ නම්  අනධ්‍යයන ආයතන අංශය සම්බන්ධ කරගන්න. ඔබ අයඳුම්පත් ඉදිරිපත් කර ඇත්නම් එහි ප්‍රගතිය පරීක්ෂා කිරීමේ හැකියාව පළමු පිටුව තුළින් සලසා ඇත.";

            return view('admin.promotion.nonAcademic_application.error_page1', compact('message_text_E', 'message_text_S'));
        } else {

            $year = date("Y") - 1;

            $promotionText = PromotionApplicationsNonacademic::where('emp_no', '=', $request->empNo)
                            ->where('year', '=', $year)->get();

            if ($promotionText->count() > 0) {

                $empNo = $request->empNo;

                return redirect()->route('nonAcademic.promotion.application.status', ['empNo' => $empNo]);
            } else {

                $empDetails = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->where('employee_no', $request->empNo)
                    ->get();

                $emp_row_count = $empDetails->count();

                if ($emp_row_count > 0) {

                    foreach ($empDetails as $empDetail) {

                        $empname = $empDetail->name_denoted_by_initials . " " . $empDetail->last_name;
                        $empNIC = $empDetail->nic;
                        $empNo = $empDetail->employee_no;
                        $empDesig = $empDetail->designation_name . " " . $empDetail->category_name;
                        $empAppDate = $empDetail->current_appointment_date;
                        $desiID = $empDetail->designation_id;
                        $AppDate = Carbon::parse($empDetail->current_appointment_date);
                        $promoEligibility = $empDetail->promo_eligibility;
                        //53 for non academic
                        if ($empDetail->main_branch_id == 53) {

                            $promoD = promotionDuration::join('designations', 'designations.id', '=', 'promotion_durations.promo_desig_id')
                                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                                ->where('designation_id', $empDetail->designation_id)
                                ->get();
                            $promoD_rowCount = $promoD->count();
                            if ($promoD_rowCount > 0) {
                                foreach ($promoD as $promoDs) {

                                    $promoDesig = $promoDs->designation_name . " " . $promoDs->category_name;
                                    $prmoDesigID = $promoDs->promo_desig_id;
                                    $promoYears = $promoDs->duration;
                                }


                                $currentDate = Carbon::parse('2024-01-01');

                                $serviceP = $AppDate->diffInYears($currentDate);

                                if ($promoEligibility == 1) {
                                    return view('admin.promotion.nonAcademic_application.non_academic_promotion_application', compact('promoDesig', 'empname', 'empNIC', 'empNo', 'empDesig', 'empAppDate', 'desiID', 'prmoDesigID'));
                                } else {

                                    if ($serviceP >= $promoYears) {

                                        return view('admin.promotion.nonAcademic_application.non_academic_promotion_application', compact('promoDesig', 'empname', 'empNIC', 'empNo', 'empDesig', 'empAppDate', 'desiID', 'prmoDesigID'));
                                    } else {
                                        $message_text_E = "Something wrong. You are not eligible to apply yet. Please contact the Non-academic Establishments Division, if any quaries.";
                                        $message_text_S = "උසස්වීම් ඉල්ලුම් කිරීම සදහා ප්‍රමාණවත් සේවාකාලයක් සම්පූර්ණ කර නොමැත. කරුණාකර අනධ්‍යයන ආයතන අංශය සම්බන්ධ කරගන්න ";

                                        return view('admin.promotion.nonAcademic_application.error_page1', compact('message_text_E', 'message_text_S'));
                                    }
                                }
                            } else {
                                $message_text_E = "Something wrong. Not available the promotion details in the system related to your designation. Please contact the Non-academic Establishments Division, if any quaries.";
                                $message_text_S = "ඔබගේ තනතුරට අදාල උසස්වීම් පිළිබද තොරතුරු පද්ධතිය මත නොමැත. කරුණාකර අනධ්‍යයන ආයතන අංශය සම්බන්ධ කරගන්න ";

                                return view('admin.promotion.nonAcademic_application.error_page1', compact('message_text_E', 'message_text_S'));
                            }
                        } else {
                            $message_text_E = "Something wrong. This application form is for non-academic staff only. Please contact the Academic Establishments Division, if any quaries.";
                            $message_text_S = "මෙම මාර්ගගත ඉල්ලුම් පත්‍රය අනධ්‍යයන කාර්යමණ්ඩලය සදහා පමණි. කරුණාකර අධ්‍යයන ආයතන අංශය සම්බන්ධ කරගන්න ";

                            return view('admin.promotion.nonAcademic_application.error_page1', compact('message_text_E', 'message_text_S'));
                        }
                    }
                } else {
                    $message_text_E = "Something wrong. Your details are not available in the system. Please contact the Non-academic Establishments Division, if any quaries.";
                    $message_text_S = "ඔබගේ තොරතුරු පද්ධතිය මත නොමැති බැවින් කරුණාකර අනධ්‍යන ආයතන අංශය සම්බන්ධ කරගන්න ";

                    return view('admin.promotion.nonAcademic_application.error_page1', compact('message_text_E', 'message_text_S'));
                }
            }
        }
    }

    public function store(Request $request)
    {
        Log::info('NonAcademicPromitionApplicationController -> non academic promotion application submit started');
        $request->validate(
            [
                'empNo' => 'required',
                'desigID' => 'required',
                'proDesigID' => 'required'
            ],
            [
                'empNo.required' => 'Something wrong. Please contact the development team.1',
                'desigID.required' => 'Something wrong. Please contact the development team.2',
                'proDesigID.required' => 'Something wrong. Please contact the development team.3'
            ]
        );

        if ($request->empNo != null) {
            if ($request->desigID != null) {

                if ($request->proDesigID != null) {

                    if ($request->language != null) {

                        $year = date("Y") - 1;
                        $refText = PromotionApplicationsNonacademic::where('year', '=', $year)
                                   ->orderBy('id')
                                   ->get();

                        $refText_rowcount = $refText->count() + 1;

                        $refNo = $year . "/" . substr("000{$refText_rowcount}", -3);

                        $promoAppli = new PromotionApplicationsNonacademic();
                        $promoAppli->year = $year;
                        $promoAppli->emp_no = $request->empNo;
                        $promoAppli->current_desigantion_id = $request->desigID;
                        $promoAppli->promotion_designation_id = $request->proDesigID;
                        $promoAppli->ref_no = $refNo;
                        $promoAppli->test_language = $request->language;
                        $promoAppli->apply_date = now();
                        $promoAppli->save();

                        $empdata = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                            ->where('employees.employee_no', '=', $request->empNo)
                            ->get();

                        if ($empdata->count() > 0) {
                            foreach ($empdata as $empdatas) {
                                $name = $empdatas->category_name . " " . $empdatas->initials . " " . $empdatas->last_name;
                                $empEmail = $empdatas->email;
                            }
                        } else {
                            $name = "";
                            $empEmail = "";
                        }

                        $testRecord = PromotionApplicationsNonacademic::where('emp_no', '=', $request->empNo)
                            ->where('year', '=', $year)
                            ->get();

                        $testRecord_count = $testRecord->count();

                        if ($testRecord_count > 0) {

                            foreach ($testRecord as $testRecords) {
                                $refNum = $testRecords->ref_no;
                                $date = $testRecords->created_at;
                                $empNo = $testRecords->emp_no;
                            }

                            //set referance number session
                            session()->get('ref_num');
                            session()->forget('ref_num');
                            Session::put('ref_num', $refNum);

                            //set promotion apply date session
                            session()->get('date');
                            session()->forget('date');
                            Session::put('date', $date);

                            //set promotion apply date session
                            session()->get('empNo');
                            session()->forget('empNo');
                            Session::put('empNo', $empNo);

                            //send email
                            $emailData = [
                                'name' =>  $name,
                                'email' => $empEmail,
                                'refNo' => $refNo,
                            ];

                            $mail = new NcaPromoApplicationSubmit($emailData);
                            //sending email
                            Mail::to($empEmail)->send($mail);

                            Log::alert('NonAcademicPromitionApplicationController -> non academic promotion application submit by employee number id - ' . $promoAppli->emp_no . ' with referance number - ' . $refNo . ' email by - ' . $empEmail);
                            Log::info('NonAcademicPromitionApplicationController -> non academic promotion application submit ended');


                            return redirect()->route('nonAcademic.promotion.application.final');
                        }
                    } else {
                        $message_text_E = "Unsuccessful submission. Please try again.";
                        $message_text_S = "අසාර්ථක ඉදිරිපත් කිරීම. කරුණාකර නැවත උත්සාහ කරන්න.";

                        return view('admin.promotion.nonAcademic_application.error_page1', compact('message_text_E', 'message_text_S'));
                    }
                } else {
                    $message_text_E = "Unsuccessful submission. Please try again.";
                    $message_text_S = "අසාර්ථක ඉදිරිපත් කිරීම. කරුණාකර නැවත උත්සාහ කරන්න.";

                    return view('admin.promotion.nonAcademic_application.error_page1', compact('message_text_E', 'message_text_S'));
                }
            } else {
                $message_text_E = "Unsuccessful submission. Please try again.";
                $message_text_S = "අසාර්ථක ඉදිරිපත් කිරීම. කරුණාකර නැවත උත්සාහ කරන්න.";

                return view('admin.promotion.nonAcademic_application.error_page1', compact('message_text_E', 'message_text_S'));
            }
        } else {
            $message_text_E = "Unsuccessful submission. Please try again.";
            $message_text_S = "අසාර්ථක ඉදිරිපත් කිරීම. කරුණාකර නැවත උත්සාහ කරන්න.";

            return view('admin.promotion.nonAcademic_application.error_page1', compact('message_text_E', 'message_text_S'));
        }
    }

    public function final()
    {

        $ref_num = session()->get('ref_num');
        $date = session()->get('date');
        $empNo = session()->get('empNo');
        return view('admin.promotion.nonAcademic_application.page2', compact('ref_num', 'date', 'empNo'));
    }


    public function applicationStatus(Request $request)
    {
        $year = date("Y") - 1;
        $empNo = $request->empNo;

        $promoText = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no',)
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as proDes', 'proDes.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proDesG', 'proDesG.id', '=', 'proDes.staff_grade')
            ->where('promotion_applications_nonacademics.emp_no', '=', $request->empNo)
            ->where('promotion_applications_nonacademics.year', '=', $year)
            ->select('promotion_applications_nonacademics.*', 'employees.*', 'designations.designation_name', 'categories.category_name', 'proDes.designation_name as promoDesName', 'proDesG.category_name as promoGread')
            ->get();

        if ($promoText->count() > 0) {

            foreach ($promoText as $promoTexts) {
                $name = $promoTexts->initials . " " . $promoTexts->last_name;
                $empNo = $promoTexts->emp_no;
                $nic = $promoTexts->nic;
                $desig = $promoTexts->designation_name . " " . $promoTexts->category_name;
                $promoDes = $promoTexts->promoDesName . " " . $promoTexts->promoGread;
                $appliedDate = Carbon::parse($promoTexts->apply_date)->format('d F, Y');
                $mediumID = $promoTexts->test_language;

                if ($mediumID == 0) {
                    $medium = "Sinhala";
                } else if ($mediumID == 1) {
                    $medium = "English";
                } else {
                    $medium = "No";
                }

                $referanceNo = $promoTexts->ref_no;
                $check = $promoTexts->check_status;
                $ar = $promoTexts->ar_accept_status;
                $head = $promoTexts->head_status;
                $exam = $promoTexts->exam_status;
                $interview = $promoTexts->interview_status;
                $confirm = $promoTexts->confirm_status;

                $checkComment = $promoTexts->check_remark;
                $arComment = $promoTexts->ar_accept_remark;

                if ($promoTexts->check_date == NULL) {

                    $checkingDate = '';
                } else {

                    $checkingDate = Carbon::parse($promoTexts->check_date)->format('d F, Y');
                }

                if ($promoTexts->ar_accept_date == NULL) {

                    $arCheckingDate = '';
                } else {

                    $arCheckingDate = Carbon::parse($promoTexts->ar_accept_date)->format('d F, Y');
                }

                if ($promoTexts->head_date == NULL) {

                    $headCheckingDate = '';
                } else {

                    $headCheckingDate = Carbon::parse($promoTexts->head_date)->format('d F, Y');
                }

                if ($promoTexts->exam_result_date == NULL) {

                    $examDate = '';
                } else {

                    $examDate = Carbon::parse($promoTexts->exam_result_date)->format('d F, Y');
                }

                if ($promoTexts->interview_date == NULL) {

                    $interviewDate = '';
                } else {

                    $interviewDate = Carbon::parse($promoTexts->interview_date)->format('d F, Y');
                }

                if ($promoTexts->confirm_date == NULL) {

                    $confirmDate = '';
                } else {

                    $confirmDate = Carbon::parse($promoTexts->confirm_date)->format('d F, Y');
                }


                //$applicationDate =
            }
            return view('admin.promotion.nonAcademic_application.status', compact('name', 'empNo', 'nic', 'desig', 'promoDes', 'appliedDate', 'medium', 'referanceNo', 'check', 'ar', 'head', 'exam', 'interview', 'confirm', 'checkComment', 'arComment', 'checkingDate', 'arCheckingDate', 'headCheckingDate', 'examDate', 'interviewDate', 'confirmDate'));
        } else {
            $message_text_E = "Haven't submitted application";
            $message_text_S = "අයදුම්පත ඉදිරිපත් කර නැත";

            return view('admin.promotion.nonAcademic_application.error_page1', compact('message_text_E', 'message_text_S'));
        }
    }

    public function openPdf()
    {

        $path = public_path('/pdf/NacPromotionNotice2024.pdf');

        return response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);
    }
}
