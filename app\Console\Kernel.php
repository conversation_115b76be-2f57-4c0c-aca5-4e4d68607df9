<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;


class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('auto:closedate')->daily();
        $schedule->command('dhead:cron')->daily();
        $schedule->command('queue:work --stop-when-empty')->everyMinute()->withoutOverlapping();
        $schedule->command('backup:run --only-db --disable-notifications')->everyTwoHours();
        $schedule->command('remindar1:cron')->daily();
        $schedule->command('remindar2:cron')->daily();
        $schedule->command('tempcheck:cron')->daily();
        $schedule->command('concheck:cron')->daily();
        $schedule->command('asbcheck:cron')->daily();
        //$schedule->command('servicetermination:cron')->daily();
        $schedule->command('retirement:cron')->daily();
        $schedule->command('headexp:cron')->daily();
        $schedule->command('temporyexp:cron')->daily();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
