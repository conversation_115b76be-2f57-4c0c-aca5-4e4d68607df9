
<?php $__env->startSection('frontend'); ?>
<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card" style="background-color:#990000">
                        <div class="card-header">
                            <h3 class="card-title text-white"><a href="<?php echo e(route('home')); ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true" style="font-size: 23px;"></i></a>&nbsp; &nbsp;Current Vacancies at USJ</h3>
                          </div>
                    </div>
                    <!-- /.box-header -->
                    <div class="card-body">
                        
                          
                    
                        <div class="card">
                            <div class="card-header text-center">
                                <form action="<?php echo e(route('vacancy.list')); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                    <div class="row">
                                        <div class="col-md-1">
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                              <div class="controls">
                                                <select name="faculty_id" id="faculty_id" class="select2bs4" style="width: 100%">
                                                  <option value="" selected="" disabled="">Select Faculty</option>
                                                  <?php $__currentLoopData = $facultysWithInfo; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $faculty): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                  <option value="<?php echo e($faculty->id); ?>" <?php echo e($faculty->id == $currentFac ? 'selected' : ''); ?>> <?php echo e(ucfirst($faculty->faculty_name)); ?></option>
                                                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                  <?php if($NullFacultyCount != 0): ?>
                                                  <option value="0" <?php echo e(0 == $currentFac ? 'selected' : ''); ?>>None</option>
                                                  <?php endif; ?>

                                                </select>
                                               </div>
                                                 </div>
                                            </div><!-- col-md-6 -->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                  <div class="controls">
                                                    <select name="department_id" id="department_id" class="select2bs4" style="width: 100%">
                                                      <option value="" selected="" disabled="">Select Department</option>
                                                      <?php $__currentLoopData = $departmentsWithInfo; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $department): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                      <?php if($department->id == $currentDep): ?>
                                                      <option value="<?php echo e($department->id); ?>" selected> <?php echo e(ucfirst($department->department_name)); ?></option>
                                                      <?php else: ?>
                                                      <option value="<?php echo e($department->id); ?>"> <?php echo e(ucfirst($department->department_name)); ?></option>
                                                      <?php endif; ?>
                                                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                      <?php if($NullDepartmentCount != 0): ?>
                                                      <option value="0" <?php echo e(0 == $currentDep ? 'selected' : ''); ?>>None</option>
                                                      <?php endif; ?>
                                                    </select>
                                                   </div>
                                                     </div>
                                                </div><!-- col-md-6 -->
                                                <div class="col-md-2">

                                        <input type="submit" value="Filter" class="btn btn-primary" style="background-color:#228B22;">

                                        <a href="<?php echo e(route('vacancy.list')); ?>" class="btn btn-danger ml-2" style="background-color:#990000;">Reset</a>
                                                </div>
                                                <div class="col-md-1">
                                                </div>
                                    </div>

                                </form>
                            </div>


                        </div>
                        <div class="table-responsive">
                            <table id="example2" class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th width="5%">SN</th>
                                        <th>Vacancy</th>
                                        <th width="15%">Type</th>
                                        <th width="15%">Closing Date</th>
                                        
                                        <th data-priority="1">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $vacancies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $vacancy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($key+1); ?></td>
                                        <?php if($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id == ''): ?>
                                        <td> <?php echo e(strtoupper($vacancy->designations->designation_name)); ?> <?php if($vacancy->display_name != ''): ?>
                                            (<?php echo e(strtoupper($vacancy->display_name)); ?>)
                                        <?php endif; ?>
                                        </td>

                                        <?php elseif($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id == ''): ?>
                                        <td><?php echo e(strtoupper($vacancy->designations->designation_name)); ?> <?php if($vacancy->display_name != ''): ?>
                                            (<?php echo e(strtoupper($vacancy->display_name)); ?>)
                                        <?php endif; ?> <br><font style="font-size: 13px;"><?php echo e(strtoupper($vacancy->faculties->faculty_name)); ?></font>
                                       </td>

                                        <?php elseif($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id != ''): ?>
                                        <td><?php echo e(strtoupper($vacancy->designations->designation_name)); ?> <?php if($vacancy->display_name != ''): ?>
                                            (<?php echo e(strtoupper($vacancy->display_name)); ?>)
                                        <?php endif; ?> <br> <font style="font-size: 13px;"><?php if($vacancy->departments->name_status == 1): ?> DEPARTMENT OF <?php endif; ?>  <?php echo e(strtoupper($vacancy->departments->department_name)); ?> </font> / <font style="font-size: 13px;"><?php echo e(strtoupper($vacancy->faculties->faculty_name)); ?></font>
                                       </td>

                                       <?php elseif($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id != ''): ?>
                                       <td><?php echo e(strtoupper($vacancy->designations->designation_name)); ?> <?php if($vacancy->display_name != ''): ?>
                                        (<?php echo e(strtoupper($vacancy->display_name)); ?>)
                                    <?php endif; ?> <br><font style="font-size: 13px;"><?php if($vacancy->departments->name_status == 1): ?> DEPARTMENT OF <?php endif; ?> <?php echo e(strtoupper($vacancy->departments->department_name)); ?></font>
                                       </td>

                                       <?php else: ?>
                                       <td><?php echo e(strtoupper($vacancy->designations->designation_name)); ?> <?php if($vacancy->display_name != ''): ?>
                                        (<?php echo e(strtoupper($vacancy->display_name)); ?>)
                                    <?php endif; ?>  - <?php echo e(strtoupper($vacancy->subject)); ?>  <br><font style="font-size: 13px;"><?php if($vacancy->departments->name_status == 1): ?> DEPARTMENT OF <?php endif; ?> <?php echo e(strtoupper($vacancy->departments->department_name)); ?></font> / <font style="font-size: 13px;"><?php echo e(strtoupper($vacancy->faculties->faculty_name)); ?></font>
                                      </td>

                                       <?php endif; ?>

                                        <td><?php echo e($vacancy->category_name); ?></td>
                                        <td><?php echo e(date("d-M-Y", strtotime($vacancy->date_closed))); ?></td>
                                        
                                        <td>
                                        <form method="post" action="<?php echo e(route('job.select')); ?>">
                                            <?php echo csrf_field(); ?>
                                            <input type="hidden" name="vacancy_id" value="<?php echo e($vacancy->id); ?>" >
                                            <input type="submit" class="btn btn-sm text-white" style="background-color:#990000" value="Apply Now">
                                        </form>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
<script type="text/javascript">

    $(document).ready(function () {

    $('#faculty_id').on('change', function(){
     var faculty_id = $(this).val();
        if(faculty_id) {
          $.ajax({
            url: "<?php echo e(url('/vacancy/department/load/ajax')); ?>/"+faculty_id,
            type:"GET",
            dataType:"json",
        success:function(data) {
            var d =$('select[name="department_id"]').empty();
            $.each(data, function(key, value){
            $('select[name="department_id"]').append('<option value="'+ value.id +'">' + value.department_name + '</option>');
         });
          },
     });
  }
});
    });

</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\University\HRSystem\resources\views/frontend/vacancy_list.blade.php ENDPATH**/ ?>