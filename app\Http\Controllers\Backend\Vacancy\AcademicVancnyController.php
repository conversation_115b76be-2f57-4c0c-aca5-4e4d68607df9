<?php

namespace App\Http\Controllers\Backend\Vacancy;

use App\Http\Controllers\Controller;
use App\Mail\ApplicationHeadForwardMail;
use App\Mail\HeadConfirmationResetMail;
use App\Mail\InterviewCallingCancelMail;
use App\Mail\InterviewCallMail;
use App\Mail\UJSWelcomeMail;
use App\Models\Application;
use App\Models\Bond;
use App\Models\City;
use App\Models\Degree;
use App\Models\DegreeCertificate;
use App\Models\DegreeSubject;
use App\Models\Diploma;
use App\Models\DiplomaCertificate;
use App\Models\Employee;
use App\Models\EmployeeSalaryStatus;
use App\Models\EmploymentRecord;
use App\Models\EmploymentRecordCertificate;
use App\Models\interviewPanelMember;
use App\Models\Membership;
use App\Models\PermanentEmployee;
use App\Models\ProfessionalQualification;
use App\Models\Referee;
use App\Models\ReleaseLetter;
use App\Models\Research;
use App\Models\SpecialQulification;
use App\Models\User;
use App\Models\Vacancy;
use App\Notifications\DutyAssumeEmployee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Session;

class AcademicVancnyController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc|head');
    }

    public function VacancyApplicationView()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;
        $currentDate = date('Y-m-d');


        if ($mainBranch == 51) {

            $totalComplete = Application::join('vacancies', 'applications.vacancy_id', '=', 'vacancies.id')
                ->where('application_decision_id', 34)
                ->whereIn('vacancy_status_type_id', array(27, 259))
                ->count();
            $totalIncomplete = Application::join('vacancies', 'applications.vacancy_id', '=', 'vacancies.id')
                ->where('application_decision_id', 33)
                ->whereIn('vacancy_status_type_id', array(27, 259))
                ->count();
            $totalCompleteFinal = Application::join('vacancies', 'applications.vacancy_id', '=', 'vacancies.id')
                ->where('application_decision_id', 34)
                ->whereIn('vacancy_status_type_id', array(28))
                ->count();
            $totalIncompleteFinal = Application::join('vacancies', 'applications.vacancy_id', '=', 'vacancies.id')
                ->where('application_decision_id', 33)
                ->whereIn('vacancy_status_type_id', array(28))
                ->count();

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancy_status_type_id',
                    'head_decision_reset',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 33 THEN 1 ELSE 0 END), 0) as available'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id IN (34, 35, 36, 37, 38, 39) THEN 1 ELSE 0 END), 0) as booked')
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_status_type_id','head_decision_reset')
                ->orderByDesc('vacancies.id')
                //->whereIn('vacancy_status_type_id', array(27,28,259))
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $totalComplete = '';
                $totalIncomplete = '';
                $totalCompleteFinal = '';
                $totalIncompleteFinal = '';

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancy_status_type_id',
                        'head_decision_reset',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 33 THEN 1 ELSE 0 END), 0) as available'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id IN (34, 35, 36, 37, 38, 39) THEN 1 ELSE 0 END), 0) as booked')
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_status_type_id','head_decision_reset')
                    ->orderByDesc('vacancies.id')
                    //->whereIn('vacancy_status_type_id', array(27,28,259))
                    ->whereIn('main_category_id', array(44, 45))
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $totalComplete = '';
                $totalIncomplete = '';
                $totalCompleteFinal = '';
                $totalIncompleteFinal = '';

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('vacancy_operators', 'vacancies.id', '=', 'vacancy_operators.vacancy_id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancy_operators.employee_no',
                        'vacancy_status_type_id',
                        'head_decision_reset',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 33 THEN 1 ELSE 0 END), 0) as available'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id IN (34, 35, 36, 37, 38, 39) THEN 1 ELSE 0 END), 0) as booked')
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_operators.employee_no', 'vacancy_status_type_id','head_decision_reset')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancy_operators.employee_no', $empNo)
                    //->whereIn('vacancy_status_type_id', array(27,28,259))
                    ->whereIn('main_category_id', array(44, 45))
                    ->get();
            }
        }
        return view('admin.vacancy.applicant.index', compact('vacancies', 'totalComplete', 'totalIncomplete', 'totalCompleteFinal', 'totalIncompleteFinal'));
    }

    public function VacancyApplicationCompleteList($id)
    {

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($vacancyId);
        $completeApplications = Application::where('vacancy_id', $vacancyId)->where('application_decision_id', '!=', 33)->get();

        return view('admin.vacancy.applicant.complete_list', compact('completeApplications', 'vacancy'));
    }

    public function VacancyApplicationIncompleteList($id)
    {

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($vacancyId);
        $incompleteApplications = Application::where('vacancy_id', $vacancyId)->where('application_decision_id', 33)->get();

        return view('admin.vacancy.applicant.incomplete_list', compact('incompleteApplications', 'vacancy'));
    }

    public function VacancyApplicationShow(Request $request)
    {
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($request->vacancy_id);

        $appData = Application::find($request->reference_no);
        $firstDegrees = Degree::where('reference_no', '=', $request->reference_no)->where('degree_type', 214)->get();
        $degreeSubjects = DegreeSubject::where('reference_no', '=', $request->reference_no)->get();
        $postDegrees = Degree::where('reference_no', '=', $request->reference_no)->where('degree_type', 215)->get();
        $diplomas = Diploma::where('reference_no', '=', $request->reference_no)->get();
        $research = Research::where('reference_no', '=', $request->reference_no)->get();
        $specialQulificationList = SpecialQulification::where('reference_no', '=', $request->reference_no)->get();
        $memberships = Membership::where('reference_no', '=', $request->reference_no)->get();
        $professionalQulificationList = ProfessionalQualification::where('reference_no', '=', $request->reference_no)->get();
        $employmentRecords = EmploymentRecord::where('reference_no', '=', $request->reference_no)->orderBy('start_date')->get();
        $employmentBonds = Bond::where('reference_no', '=', $request->reference_no)->get();
        $refereeList = Referee::where('reference_no', '=', $request->reference_no)->get();

        $degreeCertificatesCount = DegreeCertificate::where('reference_no', '=', $request->reference_no)->count();
        $diplomaCertificatesCount = DiplomaCertificate::where('reference_no', '=', $request->reference_no)->count();
        $employmentRecordCertificatesCount = EmploymentRecordCertificate::where('reference_no', '=', $request->reference_no)->count();
        $releaseLettersCount = ReleaseLetter::where('reference_no', '=', $request->reference_no)->count();

        $degreeCertificates = DegreeCertificate::where('reference_no', '=', $request->reference_no)->get();
        $diplomaCertificates = DiplomaCertificate::where('reference_no', '=', $request->reference_no)->get();
        $employmentRecordCertificates = EmploymentRecordCertificate::where('reference_no', '=', $request->reference_no)->get();
        $releaseLetters = ReleaseLetter::where('reference_no', '=', $request->reference_no)->get();

        $data = [
            'vacancy' => $vacancy,
            'appData' => $appData,
            'firstDegrees' => $firstDegrees,
            'degreeSubjects' => $degreeSubjects,
            'postDegrees' => $postDegrees,
            'diplomas' => $diplomas,
            'research' => $research,
            'specialQulificationList' => $specialQulificationList,
            'memberships' => $memberships,
            'professionalQulificationList' => $professionalQulificationList,
            'employmentRecords' => $employmentRecords,
            'employmentBonds' => $employmentBonds,
            'refereeList' => $refereeList,
            'degreeCertificatesCount' => $degreeCertificatesCount,
            'diplomaCertificatesCount' => $diplomaCertificatesCount,
            'employmentRecordCertificatesCount' => $employmentRecordCertificatesCount,
            'releaseLettersCount' => $releaseLettersCount
        ];


        return view('admin.vacancy.applicant.application', compact('vacancy', 'appData', 'firstDegrees', 'degreeSubjects', 'postDegrees', 'diplomas', 'research', 'specialQulificationList', 'memberships', 'professionalQulificationList', 'memberships', 'professionalQulificationList', 'employmentRecords', 'employmentBonds', 'refereeList', 'degreeCertificatesCount', 'diplomaCertificatesCount', 'employmentRecordCertificatesCount', 'releaseLettersCount', 'degreeCertificates', 'diplomaCertificates', 'employmentRecordCertificates', 'releaseLetters'));
    }

    public function applicationReportDownload(Request $request)
    {
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($request->vacancy_id);

        $appData = Application::find($request->reference_no);
        $firstDegrees = Degree::where('reference_no', '=', $request->reference_no)->where('degree_type', 214)->get();
        $degreeSubjects = DegreeSubject::where('reference_no', '=', $request->reference_no)->get();
        $postDegrees = Degree::where('reference_no', '=', $request->reference_no)->where('degree_type', 215)->get();
        $diplomas = Diploma::where('reference_no', '=', $request->reference_no)->get();
        $research = Research::where('reference_no', '=', $request->reference_no)->get();
        $specialQulificationList = SpecialQulification::where('reference_no', '=', $request->reference_no)->get();
        $memberships = Membership::where('reference_no', '=', $request->reference_no)->get();
        $professionalQulificationList = ProfessionalQualification::where('reference_no', '=', $request->reference_no)->get();
        $employmentRecords = EmploymentRecord::where('reference_no', '=', $request->reference_no)->orderBy('start_date')->get();
        $employmentBonds = Bond::where('reference_no', '=', $request->reference_no)->get();
        $refereeList = Referee::where('reference_no', '=', $request->reference_no)->get();

        $degreeCertificatesCount = DegreeCertificate::where('reference_no', '=', $request->reference_no)->count();
        $diplomaCertificatesCount = DiplomaCertificate::where('reference_no', '=', $request->reference_no)->count();
        $employmentRecordCertificatesCount = EmploymentRecordCertificate::where('reference_no', '=', $request->reference_no)->count();
        $releaseLettersCount = ReleaseLetter::where('reference_no', '=', $request->reference_no)->count();


        $data = [
            'vacancy' => $vacancy,
            'appData' => $appData,
            'firstDegrees' => $firstDegrees,
            'degreeSubjects' => $degreeSubjects,
            'postDegrees' => $postDegrees,
            'diplomas' => $diplomas,
            'research' => $research,
            'specialQulificationList' => $specialQulificationList,
            'memberships' => $memberships,
            'professionalQulificationList' => $professionalQulificationList,
            'employmentRecords' => $employmentRecords,
            'employmentBonds' => $employmentBonds,
            'refereeList' => $refereeList,
            'degreeCertificatesCount' => $degreeCertificatesCount,
            'diplomaCertificatesCount' => $diplomaCertificatesCount,
            'employmentRecordCertificatesCount' => $employmentRecordCertificatesCount,
            'releaseLettersCount' => $releaseLettersCount
        ];


        $pdf = FacadePdf::loadView('frontend.application.report.app_report', $data)->setPaper('a4');

        return $pdf->download($appData->reference_no . ' Final Application.pdf');
    }

    public function DegreePdf($id)
    {

        $vacancyId = decrypt($id);
        $degreepath = DegreeCertificate::find($vacancyId);
        $path = storage_path() . '/app/' . $degreepath->save_path;

        // Generate the PDF response
        $response = response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);

        // Send the response
        return $response;
    }

    public function DiplomaPdf($id)
    {

        $vacancyId = decrypt($id);
        $diplomapath = DiplomaCertificate::find($vacancyId);
        $path = storage_path() . '/app/' . $diplomapath->save_path;

        // Generate the PDF response
        $response = response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);

        // Send the response
        return $response;
    }

    public function EmpRecordPdf($id)
    {

        $vacancyId = decrypt($id);
        $emprecordpath = EmploymentRecordCertificate::find($vacancyId);
        $path = storage_path() . '/app/' . $emprecordpath->save_path;

        // Generate the PDF response
        $response = response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);

        // Send the response
        return $response;
    }

    public function ReleasePdf($id)
    {

        $vacancyId = decrypt($id);
        $releasepath = ReleaseLetter::find($vacancyId);
        $path = storage_path() . '/app/' . $releasepath->save_path;

        // Generate the PDF response
        $response = response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);

        // Send the response
        return $response;
    }

    public function VacancyApplicationFilter()
    {

        return view('admin.vacancy.applicant.filter');
    }

    /*********************************************************************/

    public function VacancyApplicationResearchSummaryView()
    {

        $data = Research::all();
        return view('admin.vacancy.applicant.temp', compact('data'));
    }

    public function VacancyResearchSummaryEdit($id)
    {
        $editData = Research::find($id);
        return view('admin.vacancy.applicant.temp_edit', compact('editData'));
    }

    public function VacancyResearchSummaryUpdate(Request $request, $id)
    {
        $data = Research::find($id);
        $data->research_summary = $request->research_summary;
        $data->save();

        $notification = array(
            'message' => 'Reserch data Name Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('vacancy.application.research.summary.view')->with($notification);
    }

    public function VacancyApplicationFirstCheckView()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;
        $currentDate = date('Y-m-d');


        if ($mainBranch == 51) {

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancy_status_type_id',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 33 THEN 1 ELSE 0 END), 0) as available'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 34 THEN 1 ELSE 0 END), 0) as booked')
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_status_type_id')
                ->orderByDesc('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(28))
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['cc', 'sc'])) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('vacancy_operators', 'vacancies.id', '=', 'vacancy_operators.vacancy_id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancy_operators.employee_no',
                        'vacancy_status_type_id',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 33 THEN 1 ELSE 0 END), 0) as available'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 34 THEN 1 ELSE 0 END), 0) as booked')
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_operators.employee_no', 'vacancy_status_type_id')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancy_operators.employee_no', $empNo)
                    ->whereIn('vacancy_status_type_id', array(28))
                    ->whereIn('main_category_id', array(44, 45))
                    ->get();
            }
        }
        return view('admin.vacancy.first_check.index', compact('vacancies'));
    }

    public function VacancyApplicationFirstCheckList($id)
    {

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($vacancyId);
        $completeApplications = Application::where('vacancy_id', $vacancyId)->where('application_decision_id', '!=', 33)->get();

        return view('admin.vacancy.first_check.list', compact('completeApplications', 'vacancy'));
    }

    public function VacancyApplicationCheckView(Request $request)
    {

        session()->get('vacancy_id');
        session()->forget('vacancy_id');
        $session = Session::put('vacancy_id', $request->vacancy_id);

        session()->get('reference_no');
        session()->forget('reference_no');
        $session = Session::put('reference_no', $request->reference_no);

        return redirect()->route('vacancy.application.check.view.show');
    }

    public function VacancyApplicationCheckViewShow()
    {

        if (!session()->get('vacancy_id')) {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.application.first.check.view')->with($notification);
        }

        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find(session()->get('vacancy_id'));

        $appData = Application::find(session()->get('reference_no'));
        $firstDegrees = Degree::where('reference_no', '=', session()->get('reference_no'))->where('degree_type', 214)->get();
        $degreeSubjects = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->get();
        $postDegrees = Degree::where('reference_no', '=', session()->get('reference_no'))->where('degree_type', 215)->get();
        $diplomas = Diploma::where('reference_no', '=', session()->get('reference_no'))->get();
        $research = Research::where('reference_no', '=', session()->get('reference_no'))->get();
        $specialQulificationList = SpecialQulification::where('reference_no', '=', session()->get('reference_no'))->get();
        $memberships = Membership::where('reference_no', '=', session()->get('reference_no'))->get();
        $professionalQulificationList = ProfessionalQualification::where('reference_no', '=', session()->get('reference_no'))->get();
        $employmentRecords = EmploymentRecord::where('reference_no', '=', session()->get('reference_no'))->orderBy('start_date')->get();
        $employmentBonds = Bond::where('reference_no', '=', session()->get('reference_no'))->get();
        $refereeList = Referee::where('reference_no', '=', session()->get('reference_no'))->get();

        $degreeCertificatesCount = DegreeCertificate::where('reference_no', '=', session()->get('reference_no'))->count();
        $diplomaCertificatesCount = DiplomaCertificate::where('reference_no', '=', session()->get('reference_no'))->count();
        $employmentRecordCertificatesCount = EmploymentRecordCertificate::where('reference_no', '=', session()->get('reference_no'))->count();
        $releaseLettersCount = ReleaseLetter::where('reference_no', '=', session()->get('reference_no'))->count();

        $degreeCertificates = DegreeCertificate::where('reference_no', '=', session()->get('reference_no'))->get();
        $diplomaCertificates = DiplomaCertificate::where('reference_no', '=', session()->get('reference_no'))->get();
        $employmentRecordCertificates = EmploymentRecordCertificate::where('reference_no', '=', session()->get('reference_no'))->get();
        $releaseLetters = ReleaseLetter::where('reference_no', '=', session()->get('reference_no'))->get();

        $categories = $this->getCategories([47]);
        $academicSORCategory = $categories->where('category_type_id', '47');

        $data = [
            'vacancy' => $vacancy,
            'appData' => $appData,
            'firstDegrees' => $firstDegrees,
            'degreeSubjects' => $degreeSubjects,
            'postDegrees' => $postDegrees,
            'diplomas' => $diplomas,
            'research' => $research,
            'specialQulificationList' => $specialQulificationList,
            'memberships' => $memberships,
            'professionalQulificationList' => $professionalQulificationList,
            'employmentRecords' => $employmentRecords,
            'employmentBonds' => $employmentBonds,
            'refereeList' => $refereeList,
            'degreeCertificatesCount' => $degreeCertificatesCount,
            'diplomaCertificatesCount' => $diplomaCertificatesCount,
            'employmentRecordCertificatesCount' => $employmentRecordCertificatesCount,
            'releaseLettersCount' => $releaseLettersCount,
            'academicSORCategory' => $academicSORCategory
        ];


        return view('admin.vacancy.first_check.application', compact('vacancy', 'appData', 'firstDegrees', 'degreeSubjects', 'postDegrees', 'diplomas', 'research', 'specialQulificationList', 'memberships', 'professionalQulificationList', 'memberships', 'professionalQulificationList', 'employmentRecords', 'employmentBonds', 'refereeList', 'degreeCertificatesCount', 'diplomaCertificatesCount', 'employmentRecordCertificatesCount', 'releaseLettersCount', 'degreeCertificates', 'diplomaCertificates', 'employmentRecordCertificates', 'releaseLetters', 'academicSORCategory'));
    }

    public function VacancyApplicationSubmitMA(Request $request)
    {

        $validatedData = $request->validate([
            'category_id' => 'required',
            'experience_year' => 'nullable|numeric'
        ], [
            'category_id.required' => 'please select relatent category according to the SOR',
        ]);

        if ($request->reference_no != '' && $request->vacancy_id != '') {

            $data = Application::find($request->reference_no);
            $data->sor_category =  $request->category_id;
            $data->experience_year = $request->experience_year;
            $data->experience_month = $request->experience_month;
            $data->ma_check_status = 1;
            $data->ma_check_remark = $request->ma_remark;
            $data->ma_check_emp = Auth()->user()->employee_no;
            $data->ma_check_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'Application Ckeck Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.application.first.check.list', encrypt($request->vacancy_id))->with($notification);
        } else {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.application.first.check.view')->with($notification);
        }
    }

    public function VacancyApplicationSubmitMAComplete(Request $request)
    {

        if ($request->vacancy_id != '') {

            $data = Vacancy::find($request->vacancy_id);
            $data->vacancy_status_type_id =  257;
            $data->ma_check_emp = Auth()->user()->employee_no;
            $data->ma_check_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'Vacancy Data Forward Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.application.first.check.view')->with($notification);
        } else {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.application.first.check.view')->with($notification);
        }
    }

    public function VacancyApplicationAdminOfficerView()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;
        $currentDate = date('Y-m-d');


        if ($mainBranch == 51) {

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancy_status_type_id',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 33 THEN 1 ELSE 0 END), 0) as available'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 34 THEN 1 ELSE 0 END), 0) as booked')
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_status_type_id')
                ->orderByDesc('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(28))
                ->get();

        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    //->join('vacancy_operators', 'vacancies.id', '=', 'vacancy_operators.vacancy_id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancy_status_type_id',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 33 THEN 1 ELSE 0 END), 0) as available'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 34 THEN 1 ELSE 0 END), 0) as booked')
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_status_type_id')
                    ->orderByDesc('vacancies.id')
                    //->where('vacancy_operators.employee_no',$empNo)
                    ->whereIn('vacancy_status_type_id', array(28))
                    ->whereIn('main_category_id', array(44, 45))
                    ->get();
            }
        }
        return view('admin.vacancy.officer_check.index', compact('vacancies'));
    }

    public function VacancyApplicationAdminOfficerComplete(Request $request)
    {

        if ($request->vacancy_id != '') {

            $data = Vacancy::find($request->vacancy_id);
            $data->vacancy_status_type_id =  258;
            $data->admin_officer_check_emp = Auth()->user()->employee_no;
            $data->admin_officer_check_date = Carbon::today();
            $data->save();

            $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('departments', 'vacancies.department_id', '=', 'departments.id')
                ->join('department_heads', 'department_heads.department_id', '=', 'departments.id')
                ->join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
                ->join('categories as title', 'title.id', '=', 'employees.title_id')
                ->join('categories as head_pos', 'department_heads.head_position', '=', 'head_pos.id')
                ->select('vacancies.*', 'categories.display_name as des_grade', 'employees.initials', 'employees.last_name', 'employees.email', 'title.category_name as title_name', 'head_pos.category_name as head_position')
                ->where('department_heads.active_status', '=', 1)
                ->find($request->vacancy_id);

            if ($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id == '') {

                //mail data
                $emailData = [
                    'name' => $vacancy->title_name . " " . $vacancy->initials . " " . $vacancy->last_name,
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->des_grade,
                    'faculty' => '',
                    'department' => '',
                    'name_status' => '',
                    'subject' => '',
                ];
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id == '') {

                //mail data
                $emailData = [
                    'name' => $vacancy->title_name . " " . $vacancy->initials . " " . $vacancy->last_name,
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->des_grade,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => '',
                    'name_status' => '',
                    'subject' => '',
                ];
                # code...
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id != '') {

                //mail data
                $emailData = [
                    'name' => $vacancy->title_name . " " . $vacancy->initials . " " . $vacancy->last_name,
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->des_grade,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => '',
                ];
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id != '') {

                $emailData = [
                    'name' => $vacancy->title_name . " " . $vacancy->initials . " " . $vacancy->last_name,
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->des_grade,
                    'faculty' => '',
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => '',
                ];
            } else {

                $emailData = [
                    'name' => $vacancy->title_name . " " . $vacancy->initials . " " . $vacancy->last_name,
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->des_grade,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => $vacancy->subject,
                ];
            }


            $mail = new ApplicationHeadForwardMail($emailData);
            //sending email
            Mail::to($vacancy->email)->send($mail);
            //Mail::to('<EMAIL>')->send($mail);

            $notification = array(
                'message' => 'Vacancy Data Forward Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.application.admin.officer.view')->with($notification);
        } else {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.application.admin.officer.view')->with($notification);
        }
    }

    public function VacancyAdminOfficerView(){

        $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
        ->select('vacancies.*','categories.display_name')
        ->whereIn('vacancy_status_type_id', array(29,30,31,32,257,258,259,282,283,284,285,286,287,288))
        ->orderByDesc('vacancies.id')
        ->get();

        return view('admin.vacancy.officer_check.list', compact('vacancies'));
    }

    public function VacancyAdminOfficerEditView($id){

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($vacancyId);

        $users = User::join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
            ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->select('employees.employee_no','employees.initials','employees.last_name')
            ->where('users.main_branch_id',52)
            ->whereIn('roles.id', [4])
            ->distinct()
            ->get();

        return view('admin.vacancy.officer_check.edit', compact('vacancy','users'));
    }

    public function VacancyAdminOfficerUpdate(Request $request, $id){

        $validatedData = $request->validate([
            'admin_officer_check_emp' => 'required',
        ], [
            'admin_officer_check_emp.required' => 'please select admin officer',
        ]);

        $data = Vacancy::find($id);
        $data->previous_admin_officer_check_emp = $request->previous_admin_officer_check_emp;
        $data->admin_officer_check_emp = $request->admin_officer_check_emp;
        $data->save();

        $notification = array(
            'message' => 'Vacancy Assign Admin Officer Updated Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('vacancy.admin.officer.view')->with($notification);

    }

    public function VacancyApplicationDeptHeadList($id)
    {

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($vacancyId);
        $completeApplications = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $vacancyId)
            ->where('application_decision_id', '!=', 33)
            ->get();

        return view('admin.vacancy.head_check.list', compact('completeApplications', 'vacancy'));
    }

    public function VacancyApplicationDeptHeadCheckView(Request $request)
    {

        session()->get('vacancy_id');
        session()->forget('vacancy_id');
        $session = Session::put('vacancy_id', $request->vacancy_id);

        session()->get('reference_no');
        session()->forget('reference_no');
        $session = Session::put('reference_no', $request->reference_no);

        return redirect()->route('vacancy.application.dept.head.check.view.show');
    }

    public function VacancyApplicationDeptHeadCheckViewShow()
    {

        if (!session()->get('vacancy_id')) {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.application.dept.head.view')->with($notification);
        }

        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find(session()->get('vacancy_id'));
        $appData = Application::find(session()->get('reference_no'));
        $firstDegrees = Degree::where('reference_no', '=', session()->get('reference_no'))->where('degree_type', 214)->get();
        $degreeSubjects = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->get();
        $postDegrees = Degree::where('reference_no', '=', session()->get('reference_no'))->where('degree_type', 215)->get();
        $diplomas = Diploma::where('reference_no', '=', session()->get('reference_no'))->get();
        $research = Research::where('reference_no', '=', session()->get('reference_no'))->get();
        $specialQulificationList = SpecialQulification::where('reference_no', '=', session()->get('reference_no'))->get();
        $memberships = Membership::where('reference_no', '=', session()->get('reference_no'))->get();
        $professionalQulificationList = ProfessionalQualification::where('reference_no', '=', session()->get('reference_no'))->get();
        $employmentRecords = EmploymentRecord::where('reference_no', '=', session()->get('reference_no'))->orderBy('start_date')->get();
        $employmentBonds = Bond::where('reference_no', '=', session()->get('reference_no'))->get();
        $refereeList = Referee::where('reference_no', '=', session()->get('reference_no'))->get();

        $degreeCertificatesCount = DegreeCertificate::where('reference_no', '=', session()->get('reference_no'))->count();
        $diplomaCertificatesCount = DiplomaCertificate::where('reference_no', '=', session()->get('reference_no'))->count();
        $employmentRecordCertificatesCount = EmploymentRecordCertificate::where('reference_no', '=', session()->get('reference_no'))->count();
        $releaseLettersCount = ReleaseLetter::where('reference_no', '=', session()->get('reference_no'))->count();

        $degreeCertificates = DegreeCertificate::where('reference_no', '=', session()->get('reference_no'))->get();
        $diplomaCertificates = DiplomaCertificate::where('reference_no', '=', session()->get('reference_no'))->get();
        $employmentRecordCertificates = EmploymentRecordCertificate::where('reference_no', '=', session()->get('reference_no'))->get();
        $releaseLetters = ReleaseLetter::where('reference_no', '=', session()->get('reference_no'))->get();

        $categories = $this->getCategories([47]);
        $academicSORCategory = $categories->where('category_type_id', '47');

        $data = [
            'vacancy' => $vacancy,
            'appData' => $appData,
            'firstDegrees' => $firstDegrees,
            'degreeSubjects' => $degreeSubjects,
            'postDegrees' => $postDegrees,
            'diplomas' => $diplomas,
            'research' => $research,
            'specialQulificationList' => $specialQulificationList,
            'memberships' => $memberships,
            'professionalQulificationList' => $professionalQulificationList,
            'employmentRecords' => $employmentRecords,
            'employmentBonds' => $employmentBonds,
            'refereeList' => $refereeList,
            'degreeCertificatesCount' => $degreeCertificatesCount,
            'diplomaCertificatesCount' => $diplomaCertificatesCount,
            'employmentRecordCertificatesCount' => $employmentRecordCertificatesCount,
            'releaseLettersCount' => $releaseLettersCount,
            'academicSORCategory' => $academicSORCategory
        ];


        return view('admin.vacancy.head_check.application', compact('vacancy', 'appData', 'firstDegrees', 'degreeSubjects', 'postDegrees', 'diplomas', 'research', 'specialQulificationList', 'memberships', 'professionalQulificationList', 'memberships', 'professionalQulificationList', 'employmentRecords', 'employmentBonds', 'refereeList', 'degreeCertificatesCount', 'diplomaCertificatesCount', 'employmentRecordCertificatesCount', 'releaseLettersCount', 'degreeCertificates', 'diplomaCertificates', 'employmentRecordCertificates', 'releaseLetters', 'academicSORCategory'));
    }

    public function VacancyApplicationSubmitDeptHead(Request $request)
    {

        $validatedData = $request->validate([
            'recom' => 'required',
            'head_check_reson' => 'required_if:recom,2', // Requires notRecomCmm if recom is "2"
        ], [
            'recom.required' => 'you must choose applicant selection status',
            'head_check_reson.required_if' => 'you must provide valid reason for not selected applicant'
        ]);

        if ($request->reference_no != '' && $request->vacancy_id != '') {

            $data = Application::find($request->reference_no);
            $data->application_decision_id = 35;
            $data->head_check_status = $request->recom;
            $data->head_check_remark = $request->head_check_remark;
            if ($request->recom == 1) {
                $data->head_check_reson = '';
            } else if ($request->recom == 2) {
                $data->head_check_reson = $request->head_check_reson;
            }

            $data->head_check_emp = Auth()->user()->employee_no;
            $data->head_check_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'Application Ckeck Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.application.dept.head.list', encrypt($request->vacancy_id))->with($notification);
        } else {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.application.dept.head.view')->with($notification);
        }
    }

    public function VacancyApplicationSubmitDeptHeadComplete(Request $request)
    {

        if ($request->vacancy_id != '') {

            $data = Vacancy::find($request->vacancy_id);
            $data->vacancy_status_type_id =  29;
            $data->dept_head_check_emp = Auth()->user()->employee_no;
            $data->dept_head_check_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'You have successfully confirmed the selections',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.application.dept.head.list', encrypt($request->vacancy_id))->with($notification);
        } else {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.application.dept.head.view')->with($notification);
        }
    }

    public function VacancyApplicationSubmitFinalReport(Request $request)
    {

        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('departments', 'vacancies.department_id', '=', 'departments.id')
            ->join('department_heads', 'department_heads.department_id', '=', 'departments.id')
            ->join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('faculties', 'vacancies.faculty_id', '=', 'faculties.id')
            ->join('faculty_deans', 'faculty_deans.faculty_id', '=', 'faculties.id')
            ->join('employees as dean', 'dean.employee_no', '=', 'faculty_deans.emp_no')
            ->join('categories as dean_titel', 'dean_titel.id', '=', 'dean.title_id')
            ->join('categories as head_pos', 'department_heads.head_position', '=', 'head_pos.id')
            ->join('categories as staff_grade', 'designations.staff_grade', '=', 'staff_grade.id')
            ->select('vacancies.*', 'employees.last_name', 'employees.initials', 'categories.category_name as category_name', 'head_pos.category_name as head_position', 'departments.department_name', 'staff_grade.display_name', 'dean.initials as dinitials', 'dean.last_name as dlast_name', 'dean_titel.category_name as dtitle', 'faculties.faculty_name')
            ->find($request->vacancy_id);

        $headDeanCount = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('departments', 'vacancies.department_id', '=', 'departments.id')
            ->join('department_heads', 'department_heads.department_id', '=', 'departments.id')
            ->join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
            ->join('faculties', 'vacancies.faculty_id', '=', 'faculties.id')
            ->join('faculty_deans', 'faculty_deans.faculty_id', '=', 'faculties.id')
            ->join('employees as dean', 'dean.employee_no', '=', 'faculty_deans.emp_no')
            ->whereColumn('department_heads.emp_no', '=', 'faculty_deans.emp_no')
            ->where('vacancies.id', $request->vacancy_id)
            ->count();

        $appDataSelected = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $request->vacancy_id)
            ->where('application_decision_id', 35)
            ->where('applications.head_check_status', 1)
            ->orderBy('applications.reference_no')
            ->get();

        $appDataNotSelected = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $request->vacancy_id)
            ->where('application_decision_id', 35)
            ->where('applications.head_check_status', 2)
            ->orderBy('applications.reference_no')
            ->get();

        $data = [
            'vacancy' => $vacancy,
            'appDataSelected' => $appDataSelected,
            'appDataNotSelected' => $appDataNotSelected,
            'headDeanCount' => $headDeanCount
        ];


        $pdf = FacadePdf::loadView('admin.vacancy.head_check.report', $data)->setPaper('a4');

        return $pdf->download($vacancy->id . ' Summary Report.pdf');
    }

    public function VacancyApplicationDeptHeadFinalPrint($id)
    {

        $vacancyId = decrypt($id);

        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('departments', 'vacancies.department_id', '=', 'departments.id')
            ->join('department_heads', 'department_heads.department_id', '=', 'departments.id')
            ->join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('faculties', 'vacancies.faculty_id', '=', 'faculties.id')
            ->join('faculty_deans', 'faculty_deans.faculty_id', '=', 'faculties.id')
            ->join('employees as dean', 'dean.employee_no', '=', 'faculty_deans.emp_no')
            ->join('categories as dean_titel', 'dean_titel.id', '=', 'dean.title_id')
            ->join('categories as head_pos', 'department_heads.head_position', '=', 'head_pos.id')
            ->join('categories as staff_grade', 'designations.staff_grade', '=', 'staff_grade.id')
            ->select('vacancies.*', 'employees.last_name', 'employees.initials', 'categories.category_name as category_name', 'head_pos.category_name as head_position', 'departments.department_name', 'staff_grade.display_name', 'dean.initials as dinitials', 'dean.last_name as dlast_name', 'dean_titel.category_name as dtitle', 'faculties.faculty_name')
            ->find($vacancyId);

        $headDeanCount = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('departments', 'vacancies.department_id', '=', 'departments.id')
            ->join('department_heads', 'department_heads.department_id', '=', 'departments.id')
            ->join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
            ->join('faculties', 'vacancies.faculty_id', '=', 'faculties.id')
            ->join('faculty_deans', 'faculty_deans.faculty_id', '=', 'faculties.id')
            ->join('employees as dean', 'dean.employee_no', '=', 'faculty_deans.emp_no')
            ->whereColumn('department_heads.emp_no', '=', 'faculty_deans.emp_no')
            ->where('vacancies.id', $vacancyId)
            ->count();

        $appDataSelected = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $vacancyId)
            ->where('application_decision_id', 35)
            ->where('applications.head_check_status', 1)
            ->orderBy('applications.reference_no')
            ->get();

        $appDataNotSelected = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $vacancyId)
            ->where('application_decision_id', 35)
            ->where('applications.head_check_status', 2)
            ->orderBy('applications.reference_no')
            ->get();

        $data = [
            'vacancy' => $vacancy,
            'appDataSelected' => $appDataSelected,
            'appDataNotSelected' => $appDataNotSelected,
            'headDeanCount' => $headDeanCount
        ];


        $pdf = FacadePdf::loadView('admin.vacancy.head_check.report', $data);

        //return $pdf->stream($vacancy->id.' Summary Report.pdf');

        return $pdf->setPaper('a4')->setOption('print-media-type', true)->stream('Summary Report.pdf', ['Attachment' => false]);
    }

    public function VacancyApplicationDeptHeadFinalReset($id)
    {

        $vacancyId = decrypt($id);
        $data = Vacancy::find($vacancyId);
        $data->vacancy_status_type_id = 258;
        $data->head_decision_reset = 1;
        $data->head_decision_reset_date = Carbon::today();
        $data->save();

        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('departments', 'vacancies.department_id', '=', 'departments.id')
            ->join('department_heads', 'department_heads.department_id', '=', 'departments.id')
            ->join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
            ->join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('categories as head_pos', 'department_heads.head_position', '=', 'head_pos.id')
            ->select('vacancies.*', 'categories.display_name as des_grade', 'employees.initials', 'employees.last_name', 'employees.email', 'title.category_name as title_name', 'head_pos.category_name as head_position')
            ->where('department_heads.active_status', '=', 1)
            ->find($vacancyId);

        if ($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id == '') {

            //mail data
            $emailData = [
                'name' => $vacancy->title_name . " " . $vacancy->initials . " " . $vacancy->last_name,
                'designation' => $vacancy->designations->designation_name,
                'grade' => $vacancy->des_grade,
                'faculty' => '',
                'department' => '',
                'name_status' => '',
                'subject' => '',
            ];
        } elseif ($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id == '') {

            //mail data
            $emailData = [
                'name' => $vacancy->title_name . " " . $vacancy->initials . " " . $vacancy->last_name,
                'designation' => $vacancy->designations->designation_name,
                'grade' => $vacancy->des_grade,
                'faculty' => $vacancy->faculties->faculty_name,
                'department' => '',
                'name_status' => '',
                'subject' => '',
            ];
            # code...
        } elseif ($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id != '') {

            //mail data
            $emailData = [
                'name' => $vacancy->title_name . " " . $vacancy->initials . " " . $vacancy->last_name,
                'designation' => $vacancy->designations->designation_name,
                'grade' => $vacancy->des_grade,
                'faculty' => $vacancy->faculties->faculty_name,
                'department' => $vacancy->departments->department_name,
                'name_status' => $vacancy->departments->name_status,
                'subject' => '',
            ];
        } elseif ($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id != '') {

            $emailData = [
                'name' => $vacancy->title_name . " " . $vacancy->initials . " " . $vacancy->last_name,
                'designation' => $vacancy->designations->designation_name,
                'grade' => $vacancy->des_grade,
                'faculty' => '',
                'department' => $vacancy->departments->department_name,
                'name_status' => $vacancy->departments->name_status,
                'subject' => '',
            ];
        } else {

            $emailData = [
                'name' => $vacancy->title_name . " " . $vacancy->initials . " " . $vacancy->last_name,
                'designation' => $vacancy->designations->designation_name,
                'grade' => $vacancy->des_grade,
                'faculty' => $vacancy->faculties->faculty_name,
                'department' => $vacancy->departments->department_name,
                'name_status' => $vacancy->departments->name_status,
                'subject' => $vacancy->subject,
            ];
        }


        $mail = new HeadConfirmationResetMail($emailData);
        //sending email
        Mail::to($vacancy->email)->send($mail);
        //Mail::to('<EMAIL>')->send($mail);

        $notification = array(
            'message' => 'Head Application Confirmation Reset',
            'alert-type' => 'success'
        );

        return redirect()->back()->with($notification);
    }

    public function VacancyInterviewPendingList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $userID = Auth()->user()->id;
        $empNo = Auth()->user()->employee_no;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {


            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    'vacancies.dept_head_check_date',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 35 THEN 1 ELSE 0 END), 0) as booked'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.dept_head_check_date')
                ->orderByDesc('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(29))
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancies.vacancy_status_type_id',
                        'vacancies.dept_head_check_date',
                        'vacancies.admin_officer_check_emp',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 35 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.dept_head_check_date', 'vacancies.admin_officer_check_emp')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancies.admin_officer_check_emp', $empNo)
                    ->whereIn('vacancy_status_type_id', array(29))
                    ->whereIn('main_category_id', array(44, 45))
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('vacancy_operators', 'vacancies.id', '=', 'vacancy_operators.vacancy_id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancy_operators.employee_no',
                        'vacancy_status_type_id',
                        'vacancies.dept_head_check_date',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 35 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_operators.employee_no', 'vacancy_status_type_id','vacancies.dept_head_check_date')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancy_operators.employee_no', $empNo)
                    ->whereIn('vacancy_status_type_id', array(29))
                    ->whereIn('main_category_id', array(44, 45))
                    ->get();
            }
        }

        $pendingVacancyCountAll = Vacancy::join('departments', 'vacancies.department_id', '=', 'departments.id')
            ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
            ->select(
                'vacancies.id',
                'departments.head_email',
                DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 33 THEN 1 ELSE 0 END), 0) as available'),
                DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 34 THEN 1 ELSE 0 END), 0) as booked')
            )
            ->groupBy('vacancies.id', 'departments.head_email')
            ->where('vacancies.vacancy_status_type_id', 258)
            ->havingRaw('booked != 0')
            ->count();

        return view('admin.vacancy.interview_pending_list.index', compact('vacancies', 'pendingVacancyCountAll'));
    }

    public function VacancyInterviewPendingApplicationList($id)
    {

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($vacancyId);

        $completeApplications = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $vacancyId)->where('application_decision_id', 35)->get();

        return view('admin.vacancy.interview_pending_list.list', compact('completeApplications', 'vacancy'));
    }

    public function VacancyInterviewAssignList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $userID = Auth()->user()->id;
        $empNo = Auth()->user()->employee_no;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {


            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('interview_panels', 'vacancies.interview_board_no', '=', 'interview_panels.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    'vacancies.dept_head_check_date',
                    'vacancies.interview_email',
                    'interview_panels.panel_id',
                    'interview_panels.interview_date',
                    'interview_panels.interview_time',
                    'interview_panels.venue',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.interview_status = 1 THEN 1 ELSE 0 END), 0) as interview'),
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.dept_head_check_date', 'vacancies.interview_email', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
                ->orderByDesc('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(31))
                ->where('interview_panels.interview_date', '>=', date("Y-m-d"))
                ->get();

        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('interview_panels', 'vacancies.interview_board_no', '=', 'interview_panels.id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancies.vacancy_status_type_id',
                        'vacancies.dept_head_check_date',
                        'vacancies.admin_officer_check_emp',
                        'vacancies.interview_email',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue',
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancies.vacancy_status_type_id',
                        'vacancies.dept_head_check_date',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.interview_status = 1 THEN 1 ELSE 0 END), 0) as interview'),
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.dept_head_check_date', 'vacancies.admin_officer_check_emp', 'vacancies.interview_email', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancies.admin_officer_check_emp', $empNo)
                    ->whereIn('vacancy_status_type_id', array(31))
                    ->whereIn('main_category_id', array(44, 45))
                    ->where('interview_panels.interview_date', '>=', date("Y-m-d"))
                    ->get();

            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('interview_panels', 'vacancies.interview_board_no', '=', 'interview_panels.id')
                    ->join('vacancy_operators', 'vacancies.id', '=', 'vacancy_operators.vacancy_id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancy_operators.employee_no',
                        'vacancy_status_type_id',
                        'vacancies.interview_email',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.interview_status = 1 THEN 1 ELSE 0 END), 0) as interview'),
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_operators.employee_no', 'vacancy_status_type_id', 'vacancies.interview_email', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancy_operators.employee_no', $empNo)
                    ->whereIn('vacancy_status_type_id', array(31))
                    ->whereIn('main_category_id', array(44, 45))
                    ->where('interview_panels.interview_date', '>=', date("Y-m-d"))
                    ->get();
            }
        }

        $pendingVacancyCountAll = Vacancy::join('departments', 'vacancies.department_id', '=', 'departments.id')
            ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
            ->select(
                'vacancies.id',
                'departments.head_email',
                DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 33 THEN 1 ELSE 0 END), 0) as available'),
                DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 35 AND applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as booked')
            )
            ->groupBy('vacancies.id', 'departments.head_email')
            ->where('vacancies.vacancy_status_type_id', 29)
            ->havingRaw('booked != 0')
            ->count();


        return view('admin.vacancy.interview_assign_list.index', compact('vacancies', 'pendingVacancyCountAll'));
    }

    public function VacancyInterviewAssignApplicationList($id)
    {

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($vacancyId);

        $completeApplications = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $vacancyId)
            ->where('application_decision_id', 35)
            ->where('head_check_status', 1)
            ->where('interview_status', 1)
            ->get();

        return view('admin.vacancy.interview_assign_list.list', compact('completeApplications', 'vacancy'));
    }

    public function VacancyInterviewScheduleDownloadIndividual(Request $request)
    {

        $sn = $request->sn;
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('interview_panels', 'vacancies.interview_board_no', '=', 'interview_panels.id')
            ->select('vacancies.*', 'categories.display_name', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
            ->find($request->vacancy_id);
        $appData = Application::find($request->reference_no);
        $firstDegrees = Degree::where('reference_no', '=', $request->reference_no)->where('degree_type', 214)->get();
        $degreeSubjects = DegreeSubject::where('reference_no', '=', $request->reference_no)->get();
        $postDegrees = Degree::where('reference_no', '=', $request->reference_no)->where('degree_type', 215)->get();
        $diplomas = Diploma::where('reference_no', '=', $request->reference_no)->get();
        $research = Research::where('reference_no', '=', $request->reference_no)->get();
        $specialQulificationList = SpecialQulification::where('reference_no', '=', $request->reference_no)->get();
        $memberships = Membership::where('reference_no', '=', $request->reference_no)->get();
        $professionalQulificationList = ProfessionalQualification::where('reference_no', '=', $request->reference_no)->get();
        $employmentRecords = EmploymentRecord::where('reference_no', '=', $request->reference_no)->orderBy('start_date')->get();
        $employmentBonds = Bond::where('reference_no', '=', $request->reference_no)->get();
        $refereeList = Referee::where('reference_no', '=', $request->reference_no)->get();

        $degreeCertificatesCount = DegreeCertificate::where('reference_no', '=', $request->reference_no)->count();
        $diplomaCertificatesCount = DiplomaCertificate::where('reference_no', '=', $request->reference_no)->count();
        $employmentRecordCertificatesCount = EmploymentRecordCertificate::where('reference_no', '=', $request->reference_no)->count();
        $releaseLettersCount = ReleaseLetter::where('reference_no', '=', $request->reference_no)->count();


        $data = [
            'sn' => $sn,
            'vacancy' => $vacancy,
            'appData' => $appData,
            'firstDegrees' => $firstDegrees,
            'degreeSubjects' => $degreeSubjects,
            'postDegrees' => $postDegrees,
            'diplomas' => $diplomas,
            'research' => $research,
            'specialQulificationList' => $specialQulificationList,
            'memberships' => $memberships,
            'professionalQulificationList' => $professionalQulificationList,
            'employmentRecords' => $employmentRecords,
            'employmentBonds' => $employmentBonds,
            'refereeList' => $refereeList,
            'degreeCertificatesCount' => $degreeCertificatesCount,
            'diplomaCertificatesCount' => $diplomaCertificatesCount,
            'employmentRecordCertificatesCount' => $employmentRecordCertificatesCount,
            'releaseLettersCount' => $releaseLettersCount
        ];


        $pdf = FacadePdf::loadView('admin.vacancy.interview_assign_list.induvidule_report', $data)->setPaper('a3', 'landscape');

        return $pdf->download($appData->reference_no . ' Final Application.pdf');
    }

    public function VacancyInterviewScheduleDownloadIndividualWebview(Request $request)
    {

        $sn = $request->sn;
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('interview_panels', 'vacancies.interview_board_no', '=', 'interview_panels.id')
            ->select('vacancies.*', 'categories.display_name', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
            ->find($request->vacancy_id);
        $appData = Application::find($request->reference_no);
        $firstDegrees = Degree::where('reference_no', '=', $request->reference_no)->where('degree_type', 214)->get();
        $degreeSubjects = DegreeSubject::where('reference_no', '=', $request->reference_no)->get();
        $postDegrees = Degree::where('reference_no', '=', $request->reference_no)->where('degree_type', 215)->get();
        $diplomas = Diploma::where('reference_no', '=', $request->reference_no)->orderBy('type', 'DESC')->get();
        $research = Research::where('reference_no', '=', $request->reference_no)->get();
        $specialQulificationList = SpecialQulification::where('reference_no', '=', $request->reference_no)->get();
        $memberships = Membership::where('reference_no', '=', $request->reference_no)->get();
        $professionalQulificationList = ProfessionalQualification::where('reference_no', '=', $request->reference_no)->get();
        $employmentRecords = EmploymentRecord::where('reference_no', '=', $request->reference_no)->orderBy('start_date')->get();
        $employmentBonds = Bond::where('reference_no', '=', $request->reference_no)->get();
        $refereeList = Referee::where('reference_no', '=', $request->reference_no)->get();

        $degreeCertificatesCount = DegreeCertificate::where('reference_no', '=', $request->reference_no)->count();
        $diplomaCertificatesCount = DiplomaCertificate::where('reference_no', '=', $request->reference_no)->count();
        $employmentRecordCertificatesCount = EmploymentRecordCertificate::where('reference_no', '=', $request->reference_no)->count();
        $releaseLettersCount = ReleaseLetter::where('reference_no', '=', $request->reference_no)->count();


        $data = [
            'sn' => $sn,
            'vacancy' => $vacancy,
            'appData' => $appData,
            'firstDegrees' => $firstDegrees,
            'degreeSubjects' => $degreeSubjects,
            'postDegrees' => $postDegrees,
            'diplomas' => $diplomas,
            'research' => $research,
            'specialQulificationList' => $specialQulificationList,
            'memberships' => $memberships,
            'professionalQulificationList' => $professionalQulificationList,
            'employmentRecords' => $employmentRecords,
            'employmentBonds' => $employmentBonds,
            'refereeList' => $refereeList,
            'degreeCertificatesCount' => $degreeCertificatesCount,
            'diplomaCertificatesCount' => $diplomaCertificatesCount,
            'employmentRecordCertificatesCount' => $employmentRecordCertificatesCount,
            'releaseLettersCount' => $releaseLettersCount
        ];


        return view('admin.vacancy.interview_assign_list.induvidule_web', compact('sn', 'vacancy', 'appData', 'firstDegrees', 'degreeSubjects', 'postDegrees', 'research', 'specialQulificationList', 'memberships', 'professionalQulificationList', 'employmentRecords', 'releaseLettersCount', 'diplomas', 'employmentBonds'));
    }

    public function VacancyInterviewScheduleIndividualWebview(Request $request)
    {

        $sn = $request->sn;
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('interview_panels', 'vacancies.interview_board_no', '=', 'interview_panels.id')
            ->select('vacancies.*', 'categories.display_name', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
            ->find($request->vacancy_id);

        $appData = Application::find($request->reference_no);
        $firstDegrees = Degree::where('reference_no', '=', $request->reference_no)->where('degree_type', 214)->get();
        $degreeSubjects = DegreeSubject::where('reference_no', '=', $request->reference_no)->get();
        $postDegrees = Degree::where('reference_no', '=', $request->reference_no)->where('degree_type', 215)->get();
        $diplomas = Diploma::where('reference_no', '=', $request->reference_no)->orderBy('type', 'DESC')->get();
        $research = Research::where('reference_no', '=', $request->reference_no)->get();
        $specialQulificationList = SpecialQulification::where('reference_no', '=', $request->reference_no)->get();
        $memberships = Membership::where('reference_no', '=', $request->reference_no)->get();
        $professionalQulificationList = ProfessionalQualification::where('reference_no', '=', $request->reference_no)->get();
        $employmentRecords = EmploymentRecord::where('reference_no', '=', $request->reference_no)->orderBy('start_date')->get();
        $employmentBonds = Bond::where('reference_no', '=', $request->reference_no)->get();
        $refereeList = Referee::where('reference_no', '=', $request->reference_no)->get();

        $degreeCertificatesCount = DegreeCertificate::where('reference_no', '=', $request->reference_no)->count();
        $diplomaCertificatesCount = DiplomaCertificate::where('reference_no', '=', $request->reference_no)->count();
        $employmentRecordCertificatesCount = EmploymentRecordCertificate::where('reference_no', '=', $request->reference_no)->count();
        $releaseLettersCount = ReleaseLetter::where('reference_no', '=', $request->reference_no)->count();


        $data = [
            'sn' => $sn,
            'vacancy' => $vacancy,
            'appData' => $appData,
            'firstDegrees' => $firstDegrees,
            'degreeSubjects' => $degreeSubjects,
            'postDegrees' => $postDegrees,
            'diplomas' => $diplomas,
            'research' => $research,
            'specialQulificationList' => $specialQulificationList,
            'memberships' => $memberships,
            'professionalQulificationList' => $professionalQulificationList,
            'employmentRecords' => $employmentRecords,
            'employmentBonds' => $employmentBonds,
            'refereeList' => $refereeList,
            'degreeCertificatesCount' => $degreeCertificatesCount,
            'diplomaCertificatesCount' => $diplomaCertificatesCount,
            'employmentRecordCertificatesCount' => $employmentRecordCertificatesCount,
            'releaseLettersCount' => $releaseLettersCount
        ];


        return view('admin.vacancy.interview_assign_list.induvidule_web_view', compact('sn', 'vacancy', 'appData', 'firstDegrees', 'degreeSubjects', 'postDegrees', 'research', 'specialQulificationList', 'memberships', 'professionalQulificationList', 'employmentRecords', 'releaseLettersCount', 'diplomas', 'employmentBonds'));
    }

    public function VacancyInterviewPanelMemberLetter(Request $request)
    {

        $pID = $request->pID;
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('interview_panels', 'vacancies.interview_board_no', '=', 'interview_panels.id')
            ->select('vacancies.*', 'categories.display_name', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
            ->find($request->vacancy_id);

        $pDate = date("d F,Y", strtotime($request->pDate));
        $pTime = $request->pTime;
        $pVenue = $request->pVenue;


        $pMember = interviewPanelMember::where('panel_id', $request->pID)->get();

        if ($pMember->count() > 0) {

            $data = [
                'vacancy' => $vacancy,
                'pdate' => $pDate,
                'ptime' => $pTime,
                'pvenue' => $pVenue,
                'pMembers' => $pMember,

            ];

            $pdf = FacadePdf::loadView('admin.vacancy.interview_assign_list.pm_letter', $data)->setPaper('a4');

            return $pdf->download('Interview Panle Member Letter.pdf');
        }
    }

    public function ApplicantInterviewCallingEmail(Request $request)
    {


        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($request->vacancy_id);

        $applications = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $request->vacancy_id)
            ->where('application_decision_id', 35)
            ->where('head_check_status', 1)
            ->where('interview_status', 1)
            ->where('interview_email', 0)
            ->get();

        $vacancyofficer = Vacancy::join('employees', 'vacancies.admin_officer_check_emp', '=', 'employees.employee_no')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->find($request->vacancy_id);

        foreach ($applications as $application) {

            if ($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id == '') {

                //mail data
                $emailData = [
                    'name' => $application->category_name . ' ' . strtoupper($application->initials) . ' ' . ucwords($application->last_name),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => '',
                    'department' => '',
                    'name_status' => '',
                    'subject' => '',
                    'date' =>  date("Y-m-d", strtotime($request->pDate)),
                    'time' => $request->pTime,
                    'venue' => $request->pVenue,
                    'designation_id' => $vacancy->designation_id,
                    'officer_name' => strtoupper($vacancyofficer->initials) . ' ' . ucwords($vacancyofficer->last_name),
                    'officer_designation' => $vacancyofficer->designation_name
                ];
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id == '') {

                //mail data
                $emailData = [
                    'name' => $application->category_name . ' ' . strtoupper($application->initials) . ' ' . ucwords($application->last_name),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => '',
                    'name_status' => '',
                    'subject' => '',
                    'date' =>  date("Y-m-d", strtotime($request->pDate)),
                    'time' => $request->pTime,
                    'venue' => $request->pVenue,
                    'designation_id' => $vacancy->designation_id,
                    'officer_name' => strtoupper($vacancyofficer->initials) . ' ' . ucwords($vacancyofficer->last_name),
                    'officer_designation' => $vacancyofficer->designation_name
                ];
                # code...
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id != '') {

                //mail data
                $emailData = [
                    'name' => $application->category_name . ' ' . strtoupper($application->initials) . ' ' . ucwords($application->last_name),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => '',
                    'date' =>  date("Y-m-d", strtotime($request->pDate)),
                    'time' => $request->pTime,
                    'venue' => $request->pVenue,
                    'designation_id' => $vacancy->designation_id,
                    'officer_name' => strtoupper($vacancyofficer->initials) . ' ' . ucwords($vacancyofficer->last_name),
                    'officer_designation' => $vacancyofficer->designation_name
                ];
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id != '') {

                $emailData = [
                    'name' => $application->category_name . ' ' . strtoupper($application->initials) . ' ' . ucwords($application->last_name),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => '',
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => '',
                    'date' =>  date("Y-m-d", strtotime($request->pDate)),
                    'time' => $request->pTime,
                    'venue' => $request->pVenue,
                    'designation_id' => $vacancy->designation_id,
                    'officer_name' => strtoupper($vacancyofficer->initials) . ' ' . ucwords($vacancyofficer->last_name),
                    'officer_designation' => $vacancyofficer->designation_name
                ];
            } else {

                $emailData = [
                    'name' => $application->category_name . ' ' . strtoupper($application->initials) . ' ' . ucwords($application->last_name),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => $vacancy->subject,
                    'date' =>  date("Y-m-d", strtotime($request->pDate)),
                    'time' => $request->pTime,
                    'venue' => $request->pVenue,
                    'designation_id' => $vacancy->designation_id,
                    'officer_name' => strtoupper($vacancyofficer->initials) . ' ' . ucwords($vacancyofficer->last_name),
                    'officer_designation' => $vacancyofficer->designation_name
                ];
            }


            $mail = new InterviewCallMail($emailData);
            //sending email
            Mail::to($application->email, 'USJ HRM SYSTEM')->bcc(Auth()->user()->email)->send($mail);
            //Mail::to('<EMAIL>','USJ HRM SYSTEM')->send($mail);

            $application->interview_email = 1;
            $application->save(); // update application interview email status

        }


        $vacancy->interview_email = 1;
        $vacancy->save(); // Save changes and trigger model events for vacancy


        $notification = array(
            'message' => 'Applicant Email Send Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('vacancy.interview.assign.list')->with($notification);
    }

    public function ApplicantInterviewCallingLetter(Request $request)
    {


        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($request->vacancy_id);

        $applications = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $request->vacancy_id)
            ->where('application_decision_id', 35)
            ->where('head_check_status', 1)
            ->where('interview_status', 1)
            ->get();

        $vacancyofficer = Vacancy::join('employees', 'vacancies.admin_officer_check_emp', '=', 'employees.employee_no')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->find($request->vacancy_id);


        if ($applications->count() > 0) {

            $data = [
                'vacancy' => $vacancy,
                'vacancyofficer' => $vacancyofficer,
                'date' =>  date("Y-m-d", strtotime($request->pDate)),
                'time' => $request->pTime,
                'venue' => $request->pVenue,
                'applications' => $applications
            ];

            $pdf = FacadePdf::loadView('admin.vacancy.interview_assign_list.int_letter', $data)->setPaper('a4');

            return $pdf->download('Interviewer Letter.pdf');
        }
    }

    public function VacancyInterviewCompleteList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $userID = Auth()->user()->id;
        $empNo = Auth()->user()->employee_no;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {


            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('interview_panels', 'vacancies.interview_board_no', '=', 'interview_panels.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    'vacancies.dept_head_check_date',
                    'vacancies.interview_email',
                    'interview_panels.panel_id',
                    'interview_panels.interview_date',
                    'interview_panels.interview_time',
                    'interview_panels.venue',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.interview_status IN (1, 2, 3, 4) THEN 1 ELSE 0 END), 0) as interview'),

                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.dept_head_check_date', 'vacancies.interview_email', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
                ->orderByDesc('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(31))
                ->where('interview_panels.interview_date', '<=', date("Y-m-d"))
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('interview_panels', 'vacancies.interview_board_no', '=', 'interview_panels.id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancies.vacancy_status_type_id',
                        'vacancies.dept_head_check_date',
                        'vacancies.admin_officer_check_emp',
                        'vacancies.interview_email',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue',
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancies.vacancy_status_type_id',
                        'vacancies.dept_head_check_date',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.interview_status IN (1, 2, 3, 4) THEN 1 ELSE 0 END), 0) as interview'),
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.dept_head_check_date', 'vacancies.admin_officer_check_emp', 'vacancies.interview_email', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancies.admin_officer_check_emp', $empNo)
                    ->whereIn('vacancy_status_type_id', array(31))
                    ->whereIn('main_category_id', array(44, 45))
                    ->where('interview_panels.interview_date', '<=', date("Y-m-d"))
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('interview_panels', 'vacancies.interview_board_no', '=', 'interview_panels.id')
                    ->join('vacancy_operators', 'vacancies.id', '=', 'vacancy_operators.vacancy_id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancy_operators.employee_no',
                        'vacancy_status_type_id',
                        'vacancies.interview_email',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.interview_status IN (1, 2, 3, 4) THEN 1 ELSE 0 END), 0) as interview'),
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_operators.employee_no', 'vacancy_status_type_id', 'vacancies.interview_email', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancy_operators.employee_no', $empNo)
                    ->whereIn('vacancy_status_type_id', array(31))
                    ->whereIn('main_category_id', array(44, 45))
                    ->where('interview_panels.interview_date', '<=', date("Y-m-d"))
                    ->get();
            }
        }


        return view('admin.vacancy.interview_result.index', compact('vacancies'));
    }

    public function VacancyInterviewCompleteApplicationList($id)
    {

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($vacancyId);

        $completeApplications = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $vacancyId)
            ->whereIn('application_decision_id', array(35, 37))
            ->where('head_check_status', 1)
            ->whereIn('interview_status', array(1, 2, 3, 4))
            ->get();

        return view('admin.vacancy.interview_result.list', compact('completeApplications', 'vacancy'));
    }

    public function VacancyApplicationCompleteCheckView(Request $request)
    {

        session()->get('vacancy_id');
        session()->forget('vacancy_id');
        $session = Session::put('vacancy_id', $request->vacancy_id);

        session()->get('reference_no');
        session()->forget('reference_no');
        $session = Session::put('reference_no', $request->reference_no);

        return redirect()->route('vacancy.application.complete.check.view.show');
    }

    public function VacancyApplicationCompleteCheckViewShow()
    {

        if (!session()->get('vacancy_id')) {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.interview.complete.list')->with($notification);
        }

        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find(session()->get('vacancy_id'));
        $appData = Application::find(session()->get('reference_no'));
        $firstDegrees = Degree::where('reference_no', '=', session()->get('reference_no'))->where('degree_type', 214)->get();
        $degreeSubjects = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->get();
        $postDegrees = Degree::where('reference_no', '=', session()->get('reference_no'))->where('degree_type', 215)->get();
        $diplomas = Diploma::where('reference_no', '=', session()->get('reference_no'))->get();
        $research = Research::where('reference_no', '=', session()->get('reference_no'))->get();
        $specialQulificationList = SpecialQulification::where('reference_no', '=', session()->get('reference_no'))->get();
        $memberships = Membership::where('reference_no', '=', session()->get('reference_no'))->get();
        $professionalQulificationList = ProfessionalQualification::where('reference_no', '=', session()->get('reference_no'))->get();
        $employmentRecords = EmploymentRecord::where('reference_no', '=', session()->get('reference_no'))->orderBy('start_date')->get();
        $employmentBonds = Bond::where('reference_no', '=', session()->get('reference_no'))->get();
        $refereeList = Referee::where('reference_no', '=', session()->get('reference_no'))->get();

        $degreeCertificatesCount = DegreeCertificate::where('reference_no', '=', session()->get('reference_no'))->count();
        $diplomaCertificatesCount = DiplomaCertificate::where('reference_no', '=', session()->get('reference_no'))->count();
        $employmentRecordCertificatesCount = EmploymentRecordCertificate::where('reference_no', '=', session()->get('reference_no'))->count();
        $releaseLettersCount = ReleaseLetter::where('reference_no', '=', session()->get('reference_no'))->count();

        $degreeCertificates = DegreeCertificate::where('reference_no', '=', session()->get('reference_no'))->get();
        $diplomaCertificates = DiplomaCertificate::where('reference_no', '=', session()->get('reference_no'))->get();
        $employmentRecordCertificates = EmploymentRecordCertificate::where('reference_no', '=', session()->get('reference_no'))->get();
        $releaseLetters = ReleaseLetter::where('reference_no', '=', session()->get('reference_no'))->get();

        $categories = $this->getCategories([47]);
        $academicSORCategory = $categories->where('category_type_id', '47');
        $cities = City::all();

        $data = [
            'vacancy' => $vacancy,
            'appData' => $appData,
            'firstDegrees' => $firstDegrees,
            'degreeSubjects' => $degreeSubjects,
            'postDegrees' => $postDegrees,
            'diplomas' => $diplomas,
            'research' => $research,
            'specialQulificationList' => $specialQulificationList,
            'memberships' => $memberships,
            'professionalQulificationList' => $professionalQulificationList,
            'employmentRecords' => $employmentRecords,
            'employmentBonds' => $employmentBonds,
            'refereeList' => $refereeList,
            'degreeCertificatesCount' => $degreeCertificatesCount,
            'diplomaCertificatesCount' => $diplomaCertificatesCount,
            'employmentRecordCertificatesCount' => $employmentRecordCertificatesCount,
            'releaseLettersCount' => $releaseLettersCount,
            'academicSORCategory' => $academicSORCategory,
            'cities' => $cities
        ];


        return view('admin.vacancy.interview_result.application', compact('vacancy', 'appData', 'firstDegrees', 'degreeSubjects', 'postDegrees', 'diplomas', 'research', 'specialQulificationList', 'memberships', 'professionalQulificationList', 'memberships', 'professionalQulificationList', 'employmentRecords', 'employmentBonds', 'refereeList', 'degreeCertificatesCount', 'diplomaCertificatesCount', 'employmentRecordCertificatesCount', 'releaseLettersCount', 'degreeCertificates', 'diplomaCertificates', 'employmentRecordCertificates', 'releaseLetters', 'academicSORCategory', 'cities'));
    }

    public function VacancyApplicationSubmitResult(Request $request)
    {

        $validatedData = $request->validate([
            'recom' => 'required',
            'effective_date' => 'required_if:recom,2', // Requires notRecomCmm if recom is "2"
            'interview_result' => 'required_if:recom,2',
            // 'postal_add1' => 'required',
            // 'postal_city_id' => 'required'
        ], [
            'recom.required' => 'you must choose applicant selection status',
            'effective_date.required_if' => 'you must enter valid effective date',
            'interview_result.required_if' => 'you must enter applicant interview mark',
            // 'postal_add1.required' => 'postal address required',
            // 'postal_city_id.required' => 'postal address city required'
        ]);

        if ($request->reference_no != '' && $request->vacancy_id != '') {

            $data = Application::find($request->reference_no);
            // $data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            // $data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            // $data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            // $data->postal_city_id = $request->postal_city_id;
            $data->application_decision_id = 37;
            $data->interview_status = $request->recom;
            if ($request->recom == 3 || $request->recom == 4) {
                //$data->effective_date = '';
                //$data->	interview_result = '';
            } else if ($request->recom == 2) {
                $data->effective_date = $request->effective_date;
                $data->interview_result = $request->interview_result;
            }

            $data->interview_result_enter_emp = Auth()->user()->employee_no;
            $data->interview_result_enter_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'Application Result Entered Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.interview.complete.application.list', encrypt($request->vacancy_id))->with($notification);
        } else {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.interview.complete.list')->with($notification);
        }
    }

    public function VacancyApplicationSubmitResultFinalize(Request $request)
    {

        if ($request->vacancy_id != '') {

            $data = Vacancy::find($request->vacancy_id);
            $data->vacancy_status_type_id =  32;
            $data->interview_finalized_emp = Auth()->user()->employee_no;
            $data->interview_finalized_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'You have successfully confirmed the final list',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.interview.complete.list')->with($notification);
        } else {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.interview.complete.list')->with($notification);
        }
    }

    public function VacancyInterviewCompletedList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $userID = Auth()->user()->id;
        $empNo = Auth()->user()->employee_no;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {


            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    'vacancies.dept_head_check_date',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.interview_status = 2 THEN 1 ELSE 0 END), 0) as booked'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.duty_assume_status = 1 THEN 1 ELSE 0 END), 0) as noduty'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.duty_assume_status = 2 THEN 1 ELSE 0 END), 0) as complete'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.duty_assume_status = 3 THEN 1 ELSE 0 END), 0) as empConvert'),
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.dept_head_check_date')
                ->orderByDesc('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(32))
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancies.vacancy_status_type_id',
                        'vacancies.dept_head_check_date',
                        'vacancies.admin_officer_check_emp',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.interview_status = 2 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.duty_assume_status = 1 THEN 1 ELSE 0 END), 0) as noduty'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.duty_assume_status = 2 THEN 1 ELSE 0 END), 0) as complete'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.duty_assume_status = 3 THEN 1 ELSE 0 END), 0) as empConvert'),
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.dept_head_check_date', 'vacancies.admin_officer_check_emp')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancies.admin_officer_check_emp', $empNo)
                    ->whereIn('vacancy_status_type_id', array(32))
                    ->whereIn('main_category_id', array(44, 45))
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('vacancy_operators', 'vacancies.id', '=', 'vacancy_operators.vacancy_id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancy_operators.employee_no',
                        'vacancy_status_type_id',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.interview_status = 2 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.duty_assume_status = 1 THEN 1 ELSE 0 END), 0) as noduty'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.duty_assume_status = 2 THEN 1 ELSE 0 END), 0) as complete'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.duty_assume_status = 3 THEN 1 ELSE 0 END), 0) as empConvert'),
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_operators.employee_no', 'vacancy_status_type_id')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancy_operators.employee_no', $empNo)
                    ->whereIn('vacancy_status_type_id', array(32))
                    ->whereIn('main_category_id', array(44, 45))
                    ->get();
            }
        }


        return view('admin.vacancy.finalization.index', compact('vacancies'));
    }

    public function VacancyInterviewCompletedApplicationList(Request $request)
    {
        auth()->user()->notifications()->where('id', $request->notification_id)->update(['read_at' => now()]);

        $vacancyId = $request->vacancy_id;
        //dd($vacancyId);
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($vacancyId);

        $completeApplications = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $vacancyId)
            ->where('application_decision_id', 37)
            ->where('interview_status', 2)
            ->get();

        return view('admin.vacancy.finalization.list', compact('completeApplications', 'vacancy'));
    }

    public function ApplicantInterviewCallingEmailReset($id)
    {


        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($id);

        $applications = Application::join('categories', 'categories.id', '=', 'applications.titel_id')
            ->where('vacancy_id', $id)
            ->where('application_decision_id', 35)
            ->where('head_check_status', 1)
            ->where('interview_status', 1)
            ->where('interview_email', 1)
            ->get();

        $vacancyofficer = Vacancy::join('employees', 'vacancies.admin_officer_check_emp', '=', 'employees.employee_no')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->find($id);

        foreach ($applications as $application) {

            if ($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id == '') {

                //mail data
                $emailData = [
                    'name' => $application->category_name . ' ' . strtoupper($application->initials) . ' ' . ucwords($application->last_name),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => '',
                    'department' => '',
                    'name_status' => '',
                    'subject' => '',
                    'officer_name' => strtoupper($vacancyofficer->initials) . ' ' . ucwords($vacancyofficer->last_name),
                    'officer_designation' => $vacancyofficer->designation_name
                ];
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id == '') {

                //mail data
                $emailData = [
                    'name' => $application->category_name . ' ' . strtoupper($application->initials) . ' ' . ucwords($application->last_name),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => '',
                    'name_status' => '',
                    'subject' => '',
                    'officer_name' => strtoupper($vacancyofficer->initials) . ' ' . ucwords($vacancyofficer->last_name),
                    'officer_designation' => $vacancyofficer->designation_name
                ];
                # code...
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id != '') {

                //mail data
                $emailData = [
                    'name' => $application->category_name . ' ' . strtoupper($application->initials) . ' ' . ucwords($application->last_name),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => '',
                    'officer_name' => strtoupper($vacancyofficer->initials) . ' ' . ucwords($vacancyofficer->last_name),
                    'officer_designation' => $vacancyofficer->designation_name
                ];
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id != '') {

                $emailData = [
                    'name' => $application->category_name . ' ' . strtoupper($application->initials) . ' ' . ucwords($application->last_name),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => '',
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => '',
                    'officer_name' => strtoupper($vacancyofficer->initials) . ' ' . ucwords($vacancyofficer->last_name),
                    'officer_designation' => $vacancyofficer->designation_name
                ];
            } else {

                $emailData = [
                    'name' => $application->category_name . ' ' . strtoupper($application->initials) . ' ' . ucwords($application->last_name),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => $vacancy->subject,
                    'officer_name' => strtoupper($vacancyofficer->initials) . ' ' . ucwords($vacancyofficer->last_name),
                    'officer_designation' => $vacancyofficer->designation_name
                ];
            }


            $mail = new InterviewCallingCancelMail($emailData);
            //sending email
            Mail::to($application->email, 'USJ HRM SYSTEM')->send($mail);
            //Mail::to('<EMAIL>','USJ HRM SYSTEM')->send($mail);

            $application->interview_email = 0;
            $application->save(); // update application interview email status

        }


        $vacancy->interview_email = 0;
        $vacancy->save(); // update vacancy interview email status


        $notification = array(
            'message' => 'Interview Pospond Email Send Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('vacancy.interview.assign.list')->with($notification);
    }

    public function ApplicantDutyAssume(Request $request)
    {

        session()->get('vacancy_id');
        session()->forget('vacancy_id');
        $session = Session::put('vacancy_id', $request->vacancy_id);

        session()->get('reference_no');
        session()->forget('reference_no');
        $session = Session::put('reference_no', $request->reference_no);

        return redirect()->route('applicant.duty.assume.show');
    }

    public function ApplicantDutyAssumeShow()
    {

        if (!session()->get('vacancy_id')) {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.interview.completed.list')->with($notification);
        }

        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find(session()->get('vacancy_id'));
        $appData = Application::find(session()->get('reference_no'));

        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $titles = $categories->where('category_type_id', '5');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $cities = City::all();

        return view('admin.vacancy.finalization.duty_assume', compact('vacancy', 'appData', 'titles', 'religions', 'cities'));
    }

    public function VacancyDutyAssumeMaSubmit(Request $request)
    {

        $validatedData = $request->validate([
            'recom' => 'required',
            'titel_id' => 'required',
            'initials' => ['required', 'regex:/^([A-Z]\.)+$/'],
            'name_denoted_by_initials' => 'required',
            'last_name' => 'required',
            'mobile_no' => 'required|digits_between:10,12',
            'permanent_add1' => 'required',
            'permanent_city_id' => 'required',
            'postal_add1' => 'required',
            'postal_city_id' => 'required',
        ]);

        if ($request->reference_no != '' && $request->vacancy_id != '') {

            $data = Application::find($request->reference_no);
            $data->titel_id = $request->titel_id;
            $data->mobile_no = $request->mobile_no;
            $data->initials = strtoupper($request->initials);
            $data->name_denoted_by_initials = ucwords(strtolower($request->name_denoted_by_initials));
            $data->last_name = ucwords(strtolower($request->last_name));
            $data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $data->permanent_city_id = $request->permanent_city_id;
            $data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $data->postal_city_id = $request->postal_city_id;
            $data->duty_assume_status =  $request->recom;
            $data->file_referance_no =  $request->file_reference_number;
            $data->initial_appointment_date =  date("Y-m-d", strtotime($request->duty_assume_date));
            $data->gratuity_cal_date =   date("Y-m-d", strtotime($request->duty_assume_date));
            $data->duty_assume_date = $request->duty_assume_date;
            $data->bsal = $request->bsal;
            $data->duty_assume_ma_emp = Auth()->user()->employee_no;
            $data->duty_assume_ma_date = Carbon::today();
            $data->save();

            $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($request->vacancy_id);

            $emailData = [
                'reference_no' => $request->reference_no,
                'vacancy_id' => $vacancy->id,
            ];

            $users = User::join('employees', 'users.employee_no', '=', 'employees.employee_no')
                    ->join('vacancies', 'vacancies.admin_officer_check_emp', '=', 'employees.employee_no')
                    ->select('users.*')
                    ->where('vacancies.id',$request->vacancy_id)
                    ->get();

            if ($request->recom == 2) {

                Notification::send($users, new DutyAssumeEmployee($emailData));
            }

            $notification = array(
                'message' => 'Applicant Duty Assume Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.interview.completed.list')->with($notification);

        } else {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.interview.completed.list')->with($notification);
        }
    }

    public function ApplicantConvertEmployee(Request $request)
    {

        session()->get('vacancy_id');
        session()->forget('vacancy_id');
        $session = Session::put('vacancy_id', $request->vacancy_id);

        session()->get('reference_no');
        session()->forget('reference_no');
        $session = Session::put('reference_no', $request->reference_no);

        return redirect()->route('applicant.convert.employee.show');
    }

    public function ApplicantConvertEmployeeShow()
    {

        if (!session()->get('vacancy_id')) {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.interview.completed.list')->with($notification);
        }

        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find(session()->get('vacancy_id'));
        $appData = Application::find(session()->get('reference_no'));

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                //->where('users.main_branch_id',53)
                ->whereIn('roles.id', [5, 6])
                ->distinct()
                ->get();
        } else if ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->where('users.main_branch_id', 52)
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            }
        } else if ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->where('users.main_branch_id', 53)
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            }
        }
        //dd($appData->nic);

        $empDataCount = Employee::where(function ($query) use ($appData) {
            $query->where('nic_old', $appData->nic_old)
                ->orWhere('nic_new', $appData->new_nic);
        })
            ->where('employee_status_id', 110)
            ->count();

        if ($empDataCount > 0) {

            $empSimilarData = Employee::where(function ($query) use ($appData) {
                $query->where('nic_old', $appData->nic_old)
                    ->orWhere('nic_new', $appData->new_nic);
            })
                ->where('employee_status_id', 110)
                ->get();


            //dd($empSimilarData);

            return view('admin.vacancy.finalization.similar_nic_find', compact('vacancy', 'appData', 'empSimilarData', 'operators'));
        } else {

            return view('admin.vacancy.finalization.applicant_to_employee', compact('vacancy', 'appData', 'operators'));
        }
    }

    public function ApplicantConvertEmployeeSubmit(Request $request)
    {

        if ($request->reference_no != '' && $request->vacancy_id != '') {

            //Application table update
            $data = Application::find($request->reference_no);
            $data->duty_assume_status =  3;
            $data->file_assign_ma_user = $request->operator_user;
            $data->duty_assume_confirm_emp = Auth()->user()->employee_no;
            $data->duty_assume_confirm_date = Carbon::today();
            $data->save();

            /************************************************************* */

            $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('vacancies.*', 'categories.display_name')
                ->find($request->vacancy_id);

            $appData = Application::find($request->reference_no);


            // create employee number
            $data = new PermanentEmployee();
            if ($appData->active_nic == 1) {
                $data->nic = strtoupper($appData->nic);
            } else {
                $data->nic = strtoupper($appData->new_nic);
            }
            $data->main_branch = Auth()->user()->main_branch_id;
            $data->assign_ma_user_id = $request->operator_user;
            $data->assign_ma_date = date("Y-m-d");
            $data->employee_status_id = 110;
            $data->employee_status_type_id = 112;
            $data->employee_work_type = 138;
            $data->added_user_id = auth()->user()->employee_no;
            $data->added_date = date("Y-m-d");
            $data->save();


            // create employee data in employee data
            //get next table id
            $maxnumber = Employee::select(DB::raw('MAX(id) as value'))->get();
            $maxValue = json_decode($maxnumber, true);
            $nextId = $maxValue[0]["value"] + 1;

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($data->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $employee_data = new Employee();
            $employee_data->id = $nextId;
            $employee_data->employee_no = $data->id;
            $employee_data->file_reference_number = strtoupper($appData->file_referance_no);
            $employee_data->vacancy_id = $vacancy->id;
            $employee_data->application_referance_no = $appData->reference_no;
            $employee_data->main_branch_id = 52;
            $employee_data->designation_id = $vacancy->designation_id;
            $employee_data->faculty_id = $vacancy->faculty_id;
            $employee_data->department_id = $vacancy->department_id;

            $employee_data->carder_faculty_id = $vacancy->faculty_id;
            $employee_data->carder_department_id = $vacancy->department_id;

            //$employee_data->sub_department_id = $request->sub_department_id;
            $employee_data->initials = strtoupper($appData->initials);
            $employee_data->name_denoted_by_initials = ucwords(strtolower($appData->name_denoted_by_initials));
            $employee_data->last_name = ucwords(strtolower($appData->last_name));
            $employee_data->civil_status_id =  $appData->civil_status_id;
            $employee_data->gender_id =  $appData->gender_id;
            //$employee_data->race_id =  $appData->race_id;
            //$employee_data->religion_id =  $appData->religion_id;
            $employee_data->permanent_add1 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $appData->permanent_add1)));
            $employee_data->permanent_add2 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $appData->permanent_add2)));
            $employee_data->permanent_add3 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $appData->permanent_add3)));
            $employee_data->permanent_city_id = $appData->permanent_city_id;
            $employee_data->postal_add1 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $appData->postal_add1)));
            $employee_data->postal_add2 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $appData->postal_add2)));
            $employee_data->postal_add3 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $appData->postal_add3)));
            $employee_data->postal_city_id = $appData->postal_city_id;
            $employee_data->personal_email = $appData->email;
            //$employee_data->email = $request->email;
            $employee_data->state_of_citizenship_id = $appData->state_of_citizenship_id;
            $employee_data->citizen_registration_no = $appData->citizen_registration_no;
            $employee_data->initial_appointment_date = date("Y-m-d", strtotime($appData->initial_appointment_date));
            $employee_data->current_appointment_date = date("Y-m-d", strtotime($appData->duty_assume_date));
            $employee_data->gratuity_cal_date = date("Y-m-d", strtotime($appData->gratuity_cal_date));
            //$employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
            //$employee_data->salary_termination_date_2 = date("Y-m-d", strtotime($request->salary_termination_date_2));
            $employee_data->retirement_date = date("Y-m-d", strtotime($appData->date_of_birth . " +65 years"));
            $employee_data->current_basic_salary = $appData->bsal;
            $employee_data->emp_highest_edu_level = $appData->emp_highest_edu_level;
            $employee_data->added_ma_user_id = Auth()->user()->employee_no;
            $employee_data->added_ma_date = date('Y-m-d');
            //        $employee_data->approved_ar_user_id=$request->approved_ar_user_id;
            //        $employee_data->approved_ar_date=$request->approved_ar_date;
            $employee_data->assign_ma_user_id = $request->operator_user;
            $employee_data->assign_ma_date = date('Y-m-d');
            $employee_data->status_id = 1;
            $employee_data->employee_status_id = 110;
            $employee_data->employee_status_type_id = 112;
            $employee_data->employee_work_type = 138;
            $employee_data->increment_date = date("m-d", strtotime($appData->duty_assume_date));
            $employee_data->emp_decision_id = 41;
            $employee_data->mobile_no = $appData->mobile_no;
            $employee_data->telephone_no = $appData->phone_no;
            $employee_data->nic = strtoupper($data->nic);

            $employee_data->nic_old = $nicData['oldnic'] ?? null;
            $employee_data->nic_new = $nicData['newnic'] ?? null;
            $employee_data->active_nic = $nicData['activenic'] ?? null;
            $employee_data->dob_gen = $nicData['dob'] ?? null;

            $employee_data->date_of_birth = date("Y-m-d", strtotime($appData->date_of_birth));
            $employee_data->title_id = $appData->titel_id;
            $employee_data->salary_payment_type = 266;
            $employee_data->save();

            //completion status data update
            $permanent_data = PermanentEmployee::find($data->id);
            $permanent_data->completion_status = 1;
            $permanent_data->updated_at = Carbon::now();
            $permanent_data->save();


            // create new salary record
            $statusRecord = new EmployeeSalaryStatus();
            $statusRecord->employee_no = $employee_data->id;
            $statusRecord->status = 1;
            $statusRecord->created_date = Carbon::now();
            $statusRecord->save();

            // employee number update in application table
            $Empdata = Application::find($request->reference_no);
            $Empdata->employee_no = $data->id;
            $Empdata->save();

            /************************************* */

            $sourcePath = $appData->profile_photo;

            $destinationDirectory = public_path('backend/dist/img/profile');

            $absoluteSourcePath = storage_path('app/' . $sourcePath);

            if (File::exists($absoluteSourcePath)) {

                //$extension = pathinfo($sourcePath, PATHINFO_EXTENSION);
                $newFileName = $data->id . '.jpg';
                $destinationDirectory = public_path('backend/dist/img/profile');
                $destinationPath = $destinationDirectory . '/' . $newFileName;


                if (copy($absoluteSourcePath, $destinationPath)) {
                    File::chmod($destinationPath, 0777); // Adjust permissions as required
                    Log::notice('File copied and renamed to: ' . $destinationPath);
                } else {

                    Log::error('Failed to copy file to: ' . $destinationPath);
                }
            } else {

                Log::alert('Source file not found: ' . $absoluteSourcePath);
            }

            /****************************************** */

            $mailData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('last_name', 'initials', 'category_name', 'employee_no', 'personal_email')
                ->where('employee_no', $data->id)
                ->first();

            $data = [
                'name' => $mailData->category_name . ' ' . $mailData->initials . ' ' . $mailData->last_name,
                'empNo' => $mailData->employee_no
            ];

            $mail = new UJSWelcomeMail($data);

            Mail::to($mailData->personal_email)->send($mail);

            $employee = Employee::find($mailData->employee_no);
            $employee->welcome_mail = 1;
            $employee->save();

            $notification = array(
                'message' => 'Applicant Transfer To Employee Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.interview.completed.application.list', ['vacancy_id' => $vacancy->id])->with($notification);
        } else {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.interview.completed.list')->with($notification);
        }
    }

    public function CreatedEmployeeUpdate(Request $request){

        if ($request->reference_no != '' && $request->vacancy_id != '') {

            //Application table update
            $appData = Application::find($request->reference_no);
            $appData->duty_assume_status =  3;
            $appData->file_assign_ma_user = $request->operator_user;
            $appData->duty_assume_confirm_emp = Auth()->user()->employee_no;
            $appData->duty_assume_confirm_date = Carbon::today();
            $appData->save();

            //completion status data update
            $data = Employee::find($request->empNo);
            $data->vacancy_id = $request->vacancy_id;
            $data->application_referance_no = $request->reference_no;
            $data->updated_at = Carbon::now();
            $data->save();

            // employee number update in application table
            $Empdata = Application::find($request->reference_no);
            $Empdata->employee_no = $request->empNo;
            $Empdata->save();

            $sourcePath = $appData->profile_photo;

            $destinationDirectory = public_path('backend/dist/img/profile');

            $absoluteSourcePath = storage_path('app/' . $sourcePath);

            if (File::exists($absoluteSourcePath)) {

                //$extension = pathinfo($sourcePath, PATHINFO_EXTENSION);
                $newFileName = $request->empNo . '.jpg';
                $destinationDirectory = public_path('backend/dist/img/profile');
                $destinationPath = $destinationDirectory . '/' . $newFileName;


                if (copy($absoluteSourcePath, $destinationPath)) {
                    File::chmod($destinationPath, 0777); // Adjust permissions as required
                    Log::notice('File copied and renamed to: ' . $destinationPath);
                } else {

                    Log::error('Failed to copy file to: ' . $destinationPath);
                }
            } else {

                Log::alert('Source file not found: ' . $absoluteSourcePath);
            }

            $notification = array(
                'message' => 'Applicant Transfer To Employee Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.interview.completed.application.list', ['vacancy_id' => $request->vacancy_id])->with($notification);
        } else {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('vacancy.interview.completed.list')->with($notification);
        }

    }

    public function VacancyFinalization(Request $request)
    {

        $type = $request->type;

        if ($type == 1) {

            $data_text = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    'vacancies.date_opened',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 34 THEN 1 ELSE 0 END), 0) as booked'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.date_opened')
                ->orderBy('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(258))
                ->havingRaw('booked = 0')
                ->havingRaw('sortlist = 0')
                ->get();

        } else if ($type == 2) {

            $data_text = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    'vacancies.date_opened',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 34 THEN 1 ELSE 0 END), 0) as booked'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.date_opened')
                ->orderBy('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(258))
                ->havingRaw('booked != 0 OR sortlist != 0')
                ->get();

        } else if ($type == 3) {

            $data_text = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    'vacancies.date_opened',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.date_opened')
                ->orderBy('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(29))
                ->havingRaw('sortlist = 0')
                ->get();

        } else if ($type == 4) {

            $data_text = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    'vacancies.date_opened',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.date_opened')
                ->orderBy('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(29))
                ->havingRaw('sortlist != 0')
                ->get();

        }else if ($type == 5) {

            $data_text = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    'vacancies.date_opened',
                    'vacancy_status_type_id',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.interview_status = 2 THEN 1 ELSE 0 END), 0) as booked')
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.date_opened')
                ->orderBy('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(32))
                ->havingRaw('booked = 0')
                ->get();

        } else {

            $data_text = array();
        }


        return view('admin.vacancy.pending.index', compact('data_text', 'type'));
    }

    public function VacancyFinalizationType1(Request $request){

        //dd($request->vacancy_id);

        if ($request->vacancy_id != '') {

            $data = Vacancy::find($request->vacancy_id);
            $data->vacancy_status_type_id =  282;
            $data->finalized_user = Auth()->user()->employee_no;
            $data->finalized_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'vacancy finalized successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.finalization',['type' => 1])->with($notification);
        }

    }

    public function VacancyFinalizationType2(Request $request){

        if ($request->vacancy_id != '') {

            $data = Vacancy::find($request->vacancy_id);
            $data->vacancy_status_type_id =  283;
            $data->finalized_user = Auth()->user()->employee_no;
            $data->finalized_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'vacancy finalized successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.finalization',['type' => 2])->with($notification);
        }

    }

    public function VacancyFinalizationType3(Request $request){

        if ($request->vacancy_id != '') {

            $data = Vacancy::find($request->vacancy_id);
            $data->vacancy_status_type_id =  284;
            $data->finalized_user = Auth()->user()->employee_no;
            $data->finalized_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'vacancy finalized successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.finalization',['type' => 3])->with($notification);
        }

    }

    public function VacancyFinalizationType4(Request $request){

        if ($request->vacancy_id != '') {

            $data = Vacancy::find($request->vacancy_id);
            $data->vacancy_status_type_id =  285;
            $data->finalized_user = Auth()->user()->employee_no;
            $data->finalized_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'vacancy finalized successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.finalization',['type' => 4])->with($notification);
        }

    }

    public function VacancyFinalizationType5(Request $request){

        if ($request->vacancy_id != '') {

            $data = Vacancy::find($request->vacancy_id);
            $data->vacancy_status_type_id =  286;
            $data->finalized_user = Auth()->user()->employee_no;
            $data->finalized_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'vacancy finalized successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.finalization',['type' => 5])->with($notification);
        }

    }

    public function VacancyFinalizationType6(Request $request){

        if ($request->vacancy_id != '') {

            $applicantToEmployee = Application::where('vacancy_id',$request->vacancy_id)
                         ->where('application_decision_id', 37)
                         ->where('duty_assume_status', 3)
                         ->count();

            $data = Vacancy::find($request->vacancy_id);
            if ($applicantToEmployee == 0) {
                $data->vacancy_status_type_id =  287;
            }else{
                $data->vacancy_status_type_id =  288;
            }

            $data->finalized_user = Auth()->user()->employee_no;
            $data->finalized_date = Carbon::today();
            $data->save();

            $notification = array(
                'message' => 'vacancy finalized successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('vacancy.interview.completed.list')->with($notification);
        }

    }
}
