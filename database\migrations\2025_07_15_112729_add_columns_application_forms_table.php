<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            $table->integer('written_exam_index_no')->nullable()->after('written_exam_date');
            $table->integer('written_exam_notification_status')->default(0)->after('written_exam_index_no');
            $table->integer('written_exam_email_notification_status')->default(0)->after('written_exam_notification_status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            $table->dropColumn('written_exam_index_no');
            $table->dropColumn('written_exam_notification_status');
            $table->dropColumn('written_exam_email_notification_status');
        });
    }
};
