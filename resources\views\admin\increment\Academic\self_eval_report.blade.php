@extends('frontend.frontend_admin_master')
@section('admin1')

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- <h1 class="text-center">INCREMENT CERTIFICATION FORM FOR ACADEMIC STAFF</h1> -->
                <h1 class="text-center">Increment Certification Form For Academic Staff</h1>
            </div><!-- /.col -->

        </div>
        <div class="row">
            <div class="col-12">
                <h3 class="text-center">Self-Evaluation Report</h3>
            </div><!-- /.col -->

        </div>
    </div><!-- /.container-fluid -->
</div>

<form id="myForm" action="{{ route('ac.incre.selfEval.store')}}" method="POST" enctype="multipart/form-data">
    @csrf
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-1">
                                    <p><b>No</b></p>
                                </div>
                                <div class="col-3">
                                    <p><b>Description</b></p>
                                </div>
                                <div class="col-4">
                                    <p><b>Undergraduate Courses (Number of Hours)</b></p>
                                </div>
                                <div class="col-4">
                                    <p><b>Postgraduate Courses (Number of Hours)</b></p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-4"></div>
                                <div class="col-2">
                                    <label>Per week</label>
                                </div>
                                <div class="col-2">
                                    <label>Per Year</label>
                                </div>
                                <div class="col-2">
                                    <label>Per week</label>
                                </div>
                                <div class="col-2">
                                    <label>Per Year</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-1">
                                    <p><b>1.</b></p>
                                </div>
                                <div class="col-3">
                                    <p><b>Lectures conducted</b></p>
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="lec_under_week" value="{{$under_lect_conduct_week}}">
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="lect_under_year" value="{{$under_lect_conduct_year}}">
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="lect_post_week" value="{{$post_lect_conduct_week}}">
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="lect_post_year" value="{{$post_lect_conduct_year}}">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-1">
                                    <p><b>2.</b></p>
                                </div>
                                <div class="col-3">
                                    <p><b>Tutorials conducted</b></p>
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="tuto_under_week" value="{{$under_tutor_conduct_week}}">
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="tuto_under_year" value="{{$under_tutor_conduct_year}}">
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="tuto_post_week" value="{{$post_tutor_conduct_week}}">
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="tuto_post_year" value="{{$post_tutor_conduct_year}}">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-1">
                                    <p><b>3.</b></p>
                                </div>
                                <div class="col-3">
                                    <p><b>Practicals conducted</b></p>
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="pract_under_week" value="{{$under_practical_conduct_week}}">
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="pract_under_year" value="{{$under_practical_conduct_year}}">
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="pract_post_week" value="{{$post_practical_conduct_week}}">
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="pract_post_year" value="{{$post_practical_conduct_year}}">
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-12">
                                    <p><b>4.Student projects supervised (Number of Projects)</b></p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-1"></div>
                                <div class="col-2">
                                    <p><b>Undergraduate</b></p>
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="under_proj_sup" value="{{$under_project_supervised}}">
                                </div>
                                <div class="col-1"></div>
                                <div class="col-2">
                                    <p><b>Postgraduate</b></p>
                                </div>
                                <div class="col-2">
                                    <input class="form-control" type="number" name="post_proj_sup" value="{{$post_project_supervised}}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <label>5.Research carried out</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-9">
                                    <textarea class="form-control" id="description5" name="description5" rows="2"></textarea>
                                </div>
                                <div class="col-1"></div>
                                <div class="col-1">
                                    <input type="button" onclick="append_row5();" id="addItem5" name="addItem5" value="Add" class="btn btn-primary">
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-12">
                                    <table class="table table-bordered" id="serTable5" name="serTable5">
                                        <thead>
                                            <tr>

                                                <th class="col-11">Description</th>
                                                <th class="col-1">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if (count($part2_text)>0)
                                            @foreach ($part2_text as $part2_texts)
                                            @if ($part2_texts->type_no == 304)
                                            <tr>
                                                <td>{{$part2_texts->description}}<input type="hidden" name="section5[]" class="form-control" value="{{$part2_texts->description}}" readonly></td>
                                                <td><input type="button" onclick="deleteRow(this);" id="removeItem" name="removeItem" value="Remove" class="btn btn-danger"></td>
                                            </tr>
                                            @endif
                                            @endforeach
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <label>6.Research Publications and scholarly work</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-9">
                                    <textarea class="form-control" id="description6" name="description6" rows="2"></textarea>
                                </div>
                                <div class="col-1"></div>
                                <div class="col-1">
                                    <input type="button" onclick="append_row6();" id="addItem6" name="addItem6" value="Add" class="btn btn-primary">
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-12">
                                    <table class="table table-bordered" id="serTable6" name="serTable6">
                                        <thead>
                                            <tr>

                                                <th class="col-11">Description</th>
                                                <th class="col-1">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if (count($part2_text)>0)
                                            @foreach ($part2_text as $part2_texts)
                                            @if ($part2_texts->type_no == 305)
                                            <tr>
                                                <td>{{$part2_texts->description}}<input type="hidden" name="section6[]" class="form-control" value="{{$part2_texts->description}}" readonly></td>
                                                <td><input type="button" onclick="deleteRow(this);" id="removeItem" name="removeItem" value="Remove" class="btn btn-danger"></td>
                                            </tr>
                                            @endif
                                            @endforeach
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <label>7.Participation at Seminars,Conferencesetc. and presentations mad</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-9">
                                    <textarea class="form-control" id="description7" name="description7" rows="2"></textarea>
                                </div>
                                <div class="col-1"></div>
                                <div class="col-1">
                                    <input type="button" onclick="append_row7();" id="addItem7" name="addItem7" value="Add" class="btn btn-primary">
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-12">
                                    <table class="table table-bordered" id="serTable7" name="serTable7">
                                        <thead>
                                            <tr>

                                                <th class="col-11">Description</th>
                                                <th class="col-1">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if (count($part2_text)>0)
                                            @foreach ($part2_text as $part2_texts)
                                            @if ($part2_texts->type_no == 306)
                                            <tr>
                                                <td>{{$part2_texts->description}}<input type="hidden" name="section7[]" class="form-control" value="{{$part2_texts->description}}" readonly></td>
                                                <td><input type="button" onclick="deleteRow(this);" id="removeItem" name="removeItem" value="Remove" class="btn btn-danger"></td>
                                            </tr>
                                            @endif
                                            @endforeach
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <label>8.Administrative duties performed</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-9">
                                    <textarea class="form-control" id="description8" name="description8" rows="2"></textarea>
                                </div>
                                <div class="col-1"></div>
                                <div class="col-1">
                                    <input type="button" onclick="append_row8();" id="addItem8" name="addItem8" value="Add" class="btn btn-primary">
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-12">
                                    <table class="table table-bordered" id="serTable8" name="serTable8">
                                        <thead>
                                            <tr>

                                                <th class="col-11">Description</th>
                                                <th class="col-1">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if (count($part2_text)>0)
                                            @foreach ($part2_text as $part2_texts)
                                            @if ($part2_texts->type_no == 307)
                                            <tr>
                                                <td>{{$part2_texts->description}}<input type="hidden" name="section8[]" class="form-control" value="{{$part2_texts->description}}" readonly></td>
                                                <td><input type="button" onclick="deleteRow(this);" id="removeItem" name="removeItem" value="Remove" class="btn btn-danger"></td>
                                            </tr>
                                            @endif
                                            @endforeach
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <label>9.Special contribution rendered to the Department/Faculty </label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-9">
                                    <textarea class="form-control" id="description9" name="description9" rows="2"></textarea>
                                </div>
                                <div class="col-1"></div>
                                <div class="col-1">
                                    <input type="button" onclick="append_row9();" id="addItem9" name="addItem9" value="Add" class="btn btn-primary">
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-12">
                                    <table class="table table-bordered" id="serTable9" name="serTable9">
                                        <thead>
                                            <tr>

                                                <th class="col-11">Description</th>
                                                <th class="col-1">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if (count($part2_text)>0)
                                            @foreach ($part2_text as $part2_texts)
                                            @if ($part2_texts->type_no == 308)
                                            <tr>
                                                <td>{{$part2_texts->description}}<input type="hidden" name="section9[]" class="form-control" value="{{$part2_texts->description}}" readonly></td>
                                                <td><input type="button" onclick="deleteRow(this);" id="removeItem" name="removeItem" value="Remove" class="btn btn-danger"></td>
                                            </tr>
                                            @endif
                                            @endforeach
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <label>10.Any other special services rendered to the University</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-9">
                                    <textarea class="form-control" id="description10" name="description10" rows="2"></textarea>
                                </div>
                                <div class="col-1"></div>
                                <div class="col-1">
                                    <input type="button" onclick="append_row10();" id="addItem10" name="addItem10" value="Add" class="btn btn-primary">
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-12">
                                    <table class="table table-bordered" id="serTable10" name="serTable10">
                                        <thead>
                                            <tr>
                                                <th class="col-11">Description</th>
                                                <th class="col-1">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if (count($part2_text)>0)
                                            @foreach ($part2_text as $part2_texts)
                                            @if ($part2_texts->type_no == 309)
                                            <tr>
                                                <td>{{$part2_texts->description}}<input type="hidden" name="section10[]" class="form-control" value="{{$part2_texts->description}}" readonly></td>
                                                <td><input type="button" onclick="deleteRow(this);" id="removeItem" name="removeItem" value="Remove" class="btn btn-danger"></td>
                                            </tr>
                                            @endif
                                            @endforeach
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <p> <input type="checkbox" aria-label="Checkbox for following text input" id="ckBox"> I certify the above particulars furnished by me are true and correct.</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12 text-center">
                                    <input type="hidden" id="appID" name="appID" value="{{$appID}}">
                                    <input type="hidden" id="refNo" name="RefNo" value="{{$refID}}">
                                    <input type="hidden" id="empNo" name="empNo" value="{{$empNo}}">
                                    <button type="submit" id="submitBtn" name="submitBtn" value="submit" class="btn btn-primary">Submit</button>
                                    <button type="button" id="saveBtn" name="saveBtn" value="save" class="btn btn-warning">Save Without Submit</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </section>
</form>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    function deleteRow(btn) {
        var row = btn.parentNode.parentNode;
        row.parentNode.removeChild(row);
    }    

    function append_row5() {
        var descrip5 = $('#description5').val().trim();

        if (descrip5 === '') {
            Swal.fire({
                icon: 'error',
                title: 'Increment self evaluation',
                text: 'Please fill the text area in section 5'
            });
            return;
        }


        var row = $('<tr>');

        var cell = $('<td>').text(descrip5);

        var hiddenInput = $('<input>').attr({
            type: 'hidden',
            name: 'section5[]',
            class: 'form-control',
            value: descrip5,
            readonly: true
        });

        cell.append(hiddenInput);

        var removeBtn = $('<td>').html(
            '<input type="button" onclick="deleteRow(this);" value="Remove" class="btn btn-danger">'
        );

        row.append(cell);
        row.append(removeBtn);

        $('#serTable5 tbody').append(row);

        $('#description5').val('');
    }

    function append_row6() {
        var descrip6 = $('#description6').val().trim();

        if (descrip6 === '') {
            Swal.fire({
                icon: 'error',
                title: 'Increment self evaluation',
                text: 'Please fill the text area in section 6'
            });
            return;
        }

        var row = $('<tr>');
        var cell = $('<td>').text(descrip6);
        var hiddenInput = $('<input>').attr({
            type: 'hidden',
            name: 'section6[]',
            class: 'form-control',
            value: descrip6,
            readonly: true
        });
        cell.append(hiddenInput);
        var removeBtn = $('<td>').html('<input type="button" onclick="deleteRow(this);" value="Remove" class="btn btn-danger">');
        row.append(cell).append(removeBtn);
        $('#serTable6 tbody').append(row);
        $('#description6').val('');
    }

    function append_row7() {
        var descrip7 = $('#description7').val().trim();

        if (descrip7 === '') {
            Swal.fire({
                icon: 'error',
                title: 'Increment self evaluation',
                text: 'Please fill the text area in section 7'
            });
            return;
        }

        var row = $('<tr>');
        var cell = $('<td>').text(descrip7);
        var hiddenInput = $('<input>').attr({
            type: 'hidden',
            name: 'section7[]',
            class: 'form-control',
            value: descrip7,
            readonly: true
        });
        cell.append(hiddenInput);
        var removeBtn = $('<td>').html('<input type="button" onclick="deleteRow(this);" value="Remove" class="btn btn-danger">');
        row.append(cell).append(removeBtn);
        $('#serTable7 tbody').append(row);
        $('#description7').val('');
    }

    function append_row8() {
        var descrip8 = $('#description8').val().trim();

        if (descrip8 === '') {
            Swal.fire({
                icon: 'error',
                title: 'Increment self evaluation',
                text: 'Please fill the text area in section 8'
            });
            return;
        }

        var row = $('<tr>');
        var cell = $('<td>').text(descrip8);
        var hiddenInput = $('<input>').attr({
            type: 'hidden',
            name: 'section8[]',
            class: 'form-control',
            value: descrip8,
            readonly: true
        });
        cell.append(hiddenInput);
        var removeBtn = $('<td>').html('<input type="button" onclick="deleteRow(this);" value="Remove" class="btn btn-danger">');
        row.append(cell).append(removeBtn);
        $('#serTable8 tbody').append(row);
        $('#description8').val('');
    }

    function append_row9() {
        var descrip9 = $('#description9').val().trim();

        if (descrip9 === '') {
            Swal.fire({
                icon: 'error',
                title: 'Increment self evaluation',
                text: 'Please fill the text area in section 9'
            });
            return;
        }

        var row = $('<tr>');
        var cell = $('<td>').text(descrip9);
        var hiddenInput = $('<input>').attr({
            type: 'hidden',
            name: 'section9[]',
            class: 'form-control',
            value: descrip9,
            readonly: true
        });
        cell.append(hiddenInput);
        var removeBtn = $('<td>').html('<input type="button" onclick="deleteRow(this);" value="Remove" class="btn btn-danger">');
        row.append(cell).append(removeBtn);
        $('#serTable9 tbody').append(row);
        $('#description9').val('');
    }

    function append_row10() {
        var descrip10 = $('#description10').val().trim();

        if (descrip10 === '') {
            Swal.fire({
                icon: 'error',
                title: 'Increment self evaluation',
                text: 'Please fill the text area in section 10'
            });
            return;
        }

        var row = $('<tr>');
        var cell = $('<td>').text(descrip10);
        var hiddenInput = $('<input>').attr({
            type: 'hidden',
            name: 'section10[]',
            class: 'form-control',
            value: descrip10,
            readonly: true
        });
        cell.append(hiddenInput);
        var removeBtn = $('<td>').html('<input type="button" onclick="deleteRow(this);" value="Remove" class="btn btn-danger">');
        row.append(cell).append(removeBtn);
        $('#serTable10 tbody').append(row);
        $('#description10').val('');
    }

    $(document).ready(function() {

        // When the 'Submit' button is clicked
        $('#submitBtn').on('click', function() {



            $('#myForm').on('submit', function(e) {
                e.preventDefault(); // Prevent form from submitting immediately

                // Check if the checkbox is checked
                if ($('#ckBox').prop('checked')) {
                    // If the checkbox is checked, show confirmation dialog

                    Swal.fire({
                        title: 'Are you sure?',
                        text: "want to submit this form",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, submit it!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Submit the form if user confirms
                            e.target.submit();
                        }
                    });
                } else {
                    // If the checkbox is not checked, show a warning alert
                    Swal.fire({
                        icon: 'error',
                        title: 'Checkbox not checked',
                        text: 'Please tick the checkbox at the bottom of the form before submitting.',
                    });
                }
            });

        });

        // Save Without Submit
        $('#saveBtn').on('click', function() {
            // Show confirmation alert
            Swal.fire({
                title: 'Are you sure?',
                text: "Do you want to save this form without submitting?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, save it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Set the form action to 'ser_save' for saving
                    $('#myForm').attr('action', '{{ route("ac.incre.ser_save") }}');
                    // Submit the form to save
                    $('#myForm').submit();
                }
            });
        });

    });
</script>
@endsection