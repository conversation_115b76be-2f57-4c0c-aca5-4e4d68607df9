
<?php $__env->startSection('frontend1'); ?>
    <?php
        use Illuminate\Support\Facades\Storage;
    ?>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<section class="content-header" style="padding-top:10px;">
    <div class="">
      
      <h5 align="center" class="font-weight-bold">
        <?php echo e(strtoupper($vacancy->designations->designation_name)); ?> <?php if($vacancy->display_name != '' && $vacancy->designation_category == 138): ?>
                                            (<?php echo e(strtoupper($vacancy->display_name)); ?>) <?php endif; ?>
                                            -
                        <b><font style="font-size: 20px;">(
                            <?php if($vacancy->designation_category == 138): ?>
                            Permanent
                            <?php elseif($vacancy->designation_category == 140): ?>
                            Temporary
                            <?php elseif($vacancy->designation_category == 141): ?>
                            Contract
                            <?php elseif($vacancy->designation_category == 142): ?>
                            Assignment Basis
                            <?php endif; ?>
                            )</font></b>
      </h5>
      <h6 align="center">
        Application for <?php if($vacancy->designation_category == 138): ?>
        Permanent
        <?php elseif($vacancy->designation_category == 140): ?>
        Temporary
        <?php elseif($vacancy->designation_category == 141): ?>
        Contract
        <?php elseif($vacancy->designation_category == 142): ?>
        Assignment Basis
        <?php endif; ?> Non-Academic Positions
      </h6>
      
    </div>

    <?php if($errors->count() > 0): ?>
    <br>
    <div class="alert alert-danger" id="success-danger">
      <button type="button" class="close" data-dismiss="alert">x</button>
      <strong>Error!</strong>
      Fill in the compulsorily requried <span class="badge badge-light"><?php echo e($errors->count()); ?></span> fields and submit again.
    </div>
    <?php endif; ?>
  </section>
  <section class="content">
    <div class="col-12">
        <form action="<?php echo e(route('application.form.save')); ?>" method="POST" enctype="multipart/form-data">
            <div class="card card-outline card-danger">
                <?php echo csrf_field(); ?>
                <div class="card-body">

                        <div class="row">
                            <input type="hidden" name="vacancy_id" id="vacancy_id" value="<?php echo e($vacancy->id); ?>">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label>Title: <span style="color: #ff0000;">*</span></label>
                                    <select name="title_id" id="title_id" class="form-control" style="width: 100%" required>
                                        <option value="" selected disabled>Select Title</option>
                                        <?php $__currentLoopData = $titles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $title): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($title->id); ?>"> <?php echo e($title->category_name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <span class="text-danger">
                                        <?php $__errorArgs = ['title_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <?php echo e($message); ?>

                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label>Initials: <span style="color: #ff0000;">*</span></label>
                                    <input type="text" name="name_with_initials" class="form-control"
                                        id="name_with_initials" value="" placeholder="Ex:- A.B.C." maxlength="40" pattern="^[A-Z.]+$"  required/>
                                    <span class="text-danger">
                                        <?php $__errorArgs = ['name_with_initials'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <?php echo e($message); ?>

                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </span>
                                </div>
                            </div>

                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label>Last Name: <span style="color: #ff0000;">*</span></label>
                                    <input type="text" name="last_name" class="form-control"
                                        id="last_name" value="" placeholder="De Silva" maxlength="40"  required/>
                                    <span class="text-danger">
                                        <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <?php echo e($message); ?>

                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                            <div class="row">
                            <div class="col-md-8 col-sm-12">
                                <div class="form-group">
                                    <label>Names Denoted by Initials: <span style="color: #ff0000;">*</span></label>
                                    <input type="text" name="name_denoted_by_initials" class="form-control"
                                        id="name_denoted_by_initials" value="" required/>
                                    <span class="text-danger">
                                        <?php $__errorArgs = ['name_denoted_by_initials'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <?php echo e($message); ?>

                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label>Telephone No. - (Mobile): <span style="color: #ff0000;">*</span></label>
                                    <input type="text" name="mobile_no" class="form-control" id="mobile_no"
                                        value="<?php echo e($mobile); ?>" placeholder="Ex:-07xxxxxxxx" pattern="^[0-9]{10}$" required/>
                                </div>
                            </div>

                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label>Telephone No. - (Residence): </label>
                                    <input type="text" name="phone_no" class="form-control" id="phone_no" value="" pattern="^[0-9]{10}$"
                                        placeholder="Ex:-0112222222" />
                                    <span class="text-danger">
                                        <?php $__errorArgs = ['phone_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <?php echo e($message); ?>

                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label>Email Address: <span style="color: #ff0000;">*</span></label>
                                    <input type="text" name="email" class="form-control" id="email"
                                        value="" required/>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="input-group">

                                <div class="col-md-4 col-sm-12">
                                    <div class="form-group">
                                        <label>NIC No.: <span style="color: #ff0000;">*</span></label>
                                        <input type="text" name="nic" class="form-control" id="nic"
                                            value="<?php echo e($nic); ?>" required readonly/>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col col-md-12 col-sm-12">
                                        <div class="form-group">
                                            <label for="nic_copy">Please upload a copy of NIC (PDF Format Only)<span
                                                    style="color: #ff0000;">*</span></label>
                                            <div class="input-group">
                                                <div class="custom-file">
                                                    <input type="file" class="custom-file-input" id="nic_copy"
                                                        name="nic_copy" accept=".pdf" onchange="updateFileName(this)" required>
                                                    <label class="custom-file-label" for="nic_copy">Choose file</label>
                                                </div>
                                            </div>
                                            <span class="text-danger">
                                                <?php $__errorArgs = ['nic_copy'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <?php echo e($message); ?>

                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </span><br>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 col-sm-12">
                                <label><strong>Permanent Address:</strong></label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label class="font-weight-normal">Address Line 1: <span
                                            style="color: #ff0000;">*</span></label>
                                    <input type="text" name="permanent_address_line1" class="form-control"
                                        id="permanent_address_line1" value="" placeholder="Ex:-325B Temple Road" required/>
                                    <span class="text-danger">
                                        <?php $__errorArgs = ['permanent_address_line1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <?php echo e($message); ?>

                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label class="font-weight-normal">Address Line 2:</label>
                                    <input type="text" name="permanent_address_line2" class="form-control"
                                        id="permanent_address_line2" value="" />
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label class="font-weight-normal">Address Line 3:</label>
                                    <input type="text" name="permanent_address_line3" class="form-control"
                                        id="permanent_address_line3" value="" />
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label class="font-weight-normal">City: <span
                                            style="color: #ff0000;">*</span></label>
                                    <select name="permanent_address_city" id="permanent_address_city" class="form-control"
                                        style="width: 100%" required>
                                        <option value="" selected>Select City</option>
                                        <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($city->id); ?>"><?php echo e($city->name_en); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <script>
                            $(document).ready(function() {
                                $('#checkbox1').change(function() {
                                    if (this.checked) {
                                        $('#postal_address_line1').prop('readonly', true);
                                        $('#postal_address_line2').prop('readonly', true);
                                        $('#postal_address_line3').prop('readonly', true);
                                        $('#postal_address_city').prop('readonly', true);
                                        $('#postal_address_line1').val($('#permanent_address_line1').val());
                                        $('#postal_address_line2').val($('#permanent_address_line2').val());
                                        $('#postal_address_line3').val($('#permanent_address_line3').val());
                                        $('#postal_address_city').val($('#permanent_address_city').val()).trigger('change');


                                        // var skillsSelect = document.getElementById("permanent_address_city");
                                        // var selectedText = skillsSelect.options[skillsSelect.selectedIndex].text;
                                        // var value = $("#permanent_address_city option:selected").val();
                                    } else {
                                        $('#postal_address_line1').prop('readonly', false);
                                        $('#postal_address_line2').prop('readonly', false);
                                        $('#postal_address_line3').prop('readonly', false);
                                        $('#postal_address_city').prop('disabled', false);
                                        $('#postal_address_line1').val('');
                                        $('#postal_address_line2').val('');
                                        $('#postal_address_line3').val('');
                                        $('#postal_address_city').val('').trigger('change');
                                    }
                                });
                            });
                        </script>

                        <div class="row">
                            <div class="col-md-12 col-sm-12">
                                <label class="font-weight-normal">Same as Permanent Address:</label> <input
                                    type="checkbox" id="checkbox1">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-sm-12">
                                <label><strong>Postal Address:</strong></label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label class="font-weight-normal">Address Line 1: <span
                                            style="color: #ff0000;">*</span></label>
                                    <input type="text" name="postal_address_line1" class="form-control"
                                        id="postal_address_line1" value="" placeholder="Ex:-325B Temple Road" required/>
                                    <span class="text-danger">
                                        <?php $__errorArgs = ['postal_address_line1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <?php echo e($message); ?>

                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label class="font-weight-normal">Address Line 2:</label>
                                    <input type="text" name="postal_address_line2" class="form-control"
                                        id="postal_address_line2" value="" />
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label class="font-weight-normal">Address Line 3:</label>
                                    <input type="text" name="postal_address_line3" class="form-control"
                                        id="postal_address_line3" value="" />
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label class="font-weight-normal">City: <span
                                            style="color: #ff0000;">*</span></label>
                                    <select name="postal_address_city" id="postal_address_city" class="form-control"
                                        style="width: 100%" required>
                                        <option value="" selected>Select City</option>
                                        <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($city->id); ?>"><?php echo e($city->name_en); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label>Date of Birth: <span style="color: #ff0000;">*</span></label>
                                    <div class="input-group date" id="date_of_birth" data-target-input="nearest">
                                        <input type="date"  class="form-control" data-target="#date_of_birth"
                                            name="date_of_birth" value="<?php echo e($dob); ?>" required readonly/>
                                        <div class="input-group-append" data-target="#date_of_birth"
                                            data-toggle="datetimepicker">

                                        </div>
                                    </div>
                                    <span class="text-danger">
                                        <?php $__errorArgs = ['date_of_birth'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <?php echo e($message); ?>

                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </span>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label>Age to Application Date:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="age" name="age"
                                            readonly value="<?php echo e($age['years']); ?> Years <?php echo e($age['months']); ?> Months <?php echo e($age['days']); ?> Days"/>
                                        <div class="input-group-append">
                                            
                                        </div>
                                    </div>
                                </div>
                            </div>

                            

                            <!-- This script is used to automatically calculate the date of birth and age using the NIC -->
                            
                            <!-- **** End of birthday and age calculation **** -->

                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label>Civil Status: <span style="color: #ff0000;"> *</span></label>
                                    <select name="civil_status" id="civil_status" class="form-control"
                                        style="width: 100%" required>
                                        <option value="" selected disabled>Select Civil Status</option>
                                        <?php $__currentLoopData = $civilStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $civilStatus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($civilStatus->id); ?>">
                                                <?php echo e(ucfirst($civilStatus->category_name)); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <span class="text-danger">
                                        <?php $__errorArgs = ['civil_status_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <?php echo e($message); ?>

                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </span>
                                </div>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">

                                    <label for="pqualification_start_year">Highest Academic Qualification:</label>
                                    <select name="pqualification_type_1" class="form-control" id="pqualification_type_1">

                                        <option value="0" disabled="" selected="">Select Academic
                                            Qualification
                                        </option>
                                        <?php $__currentLoopData = $academicTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $academicType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($academicType->id); ?>">
                                                <?php echo e(ucfirst($academicType->category_name)); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label>Sri Lankan Citizenship:<span
                                            style="color: #ff0000;">*</span></label>
                                    <select name="state_of_citizenship_id" id="state_of_citizenship_id"
                                        class="form-control" style="width: 100%" required>
                                        <option value="" selected disabled>Select Citizenship Type</option>
                                        <option value="By Descent">By Descent</option>
                                        <option value="By Registration">By Registration</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label>Citizen Registration No.:<span style="color: #ff0000;">*</span> </label>
                                    <input type="text" name="citizen_registration_no" class="form-control"
                                        id="citizen_registration_no" value="" required/>
                                </div>
                            </div>

                            <script>
                                $(document).ready(function() {
                                    $('#state_of_citizenship_id').change(function() {
                                        if ($(this).val() == 'registration') {
                                            $('#citizen_registration_no').closest('.form-group').show();
                                            $('#citizen_registration_no').prop('disabled', false);
                                        } else {
                                            $('#citizen_registration_no').closest('.form-group').hide();
                                            $('#citizen_registration_no').prop('disabled', true);
                                            $('#citizen_registration_no').val('');
                                        }
                                    }).trigger('change'); // Trigger change event on page load to set initial state
                                });
                            </script>





                        </div>



                </div>



            </div>



            <div class="card card-outline">
                <div class="card-body">




                    <?php echo $__env->make('frontend.nonacademic.application.form_components.ol_results', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('frontend.nonacademic.application.form_components.al_results', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('frontend.nonacademic.application.form_components.higher_education', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('frontend.nonacademic.application.form_components.pro_qualification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('frontend.nonacademic.application.form_components.language_exam', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('frontend.nonacademic.application.form_components.experience', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('frontend.nonacademic.application.form_components.extra_curricular', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('frontend.nonacademic.application.form_components.public_work', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                </div>

                <div class="card card-outline card-danger">
                    <div class="card-body">
                      <div class="icheck-danger">
                        <input type="checkbox" id="agreeTerms" name="terms" value="1" required>
                        <label for="agreeTerms">
                          I hereby declare that the particulars furnished by me in the application are true and accurate. I am aware that if any particulars contained herein are found to be false or incorrect, I am liable to disqualification if the inaccuracy is discovered before the selection and dismissal without any compensation if the inaccuracy is discovered after the appointment.
                        </label>
                      </div>
                      <span class="text-danger"><?php $__errorArgs = ['terms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><br><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                      <br>

                    </div>
                  </div>

                <div class="form-group">
                    <div class="row justify-content-center">
                        <div class="col-md-4 text-center">
                            <input type="submit" name="submit" value="Submit" class="btn btn-primary btn-lg" id="submitBtn">
                        </div>
                    </div>
                </div>

                <!-- Floating Save as Draft button that appears when scrolling -->
                



                <!-- PDF Download Confirmation Modal -->
                <div class="modal fade" id="pdfDownloadModal" tabindex="-1" role="dialog" aria-labelledby="pdfDownloadModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="pdfDownloadModalLabel">Download Application PDF</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body text-center">
                                <p style="font-size:18px;">Do you want to download your application as a PDF?</p>
                            </div>
                            <div class="modal-footer justify-content-center">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="noPdfBtn">No, Continue</button>
                                <button type="button" class="btn btn-primary" id="yesPdfBtn">Yes, Download PDF</button>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    $(document).ready(function() {
                        // Store form data for PDF download
                        let formData;
                        let formAction;
                        let applicationId;

                        // The Submit button is disabled until terms are accepted
                        $('#agreeTerms').change(function() {
                            $('#submitBtn').prop('disabled', !this.checked);
                        });

                        // // Add form submission handler
                        // $('form').submit(function(e) {
                        //     // If submitting as draft, don't require terms checkbox
                        //     if ($(document.activeElement).attr('name') === 'save_draft') {
                        //         // Allow submission even if terms not checked
                        //         return true;
                        //     }

                        //     // For final submission, ensure terms are checked
                        //     if (!$('#agreeTerms').is(':checked')) {
                        //         e.preventDefault();
                        //         alert('Please accept the terms and conditions to submit your application.');
                        //         return false;
                        //     }

                        //     // If this is a final submission, show the PDF download modal
                        //     if ($(document.activeElement).attr('name') === 'submit') {
                        //         e.preventDefault();
                        //         formData = new FormData(this);
                        //         formAction = $(this).attr('action');

                        //         // Show the modal
                        //         $('#pdfDownloadModal').modal('show');
                        //         return false;
                        //     }

                        //     return true;
                        // });

                        // // Handle "No, Continue" button click
                        // $('#noPdfBtn').click(function() {
                        //     // Submit the form without PDF download
                        //     $.ajax({
                        //         url: formAction,
                        //         type: 'POST',
                        //         data: formData,
                        //         processData: false,
                        //         contentType: false,
                        //         success: function(response) {
                        //             Swal.fire({
                        //                 icon: 'success',
                        //                 title: 'Success!',
                        //                 text: 'Your application has been submitted successfully.',
                        //                 showConfirmButton: true
                        //             }).then((result) => {
                        //                 window.location.href = "<?php echo e(route('nonacademic.vancancy.application.home')); ?>";
                        //             });
                        //         },
                        //         error: function(xhr, status, error) {
                        //             console.error(error);
                        //             alert('An error occurred while submitting your application. Please try again.');
                        //         }
                        //     });
                        // });

                        // // Handle "Yes, Download PDF" button click
                        // $('#yesPdfBtn').click(function() {
                        //     // Submit the form and get the application ID
                        //     $.ajax({
                        //         url: formAction,
                        //         type: 'POST',
                        //         data: formData,
                        //         processData: false,
                        //         contentType: false,
                        //         success: function(response) {
                        //             if (response && response.application_id) {
                        //                 // Download the PDF
                        //                 window.location.href = "<?php echo e(route('application.form.pdf', '')); ?>/" + response.application_id;

                        //                 // After a short delay, redirect to the application form view
                        //                 setTimeout(function() {
                        //                     Swal.fire({
                        //                         title: 'Success!',
                        //                         text: 'Your application has been submitted successfully.',
                        //                         icon: 'success',
                        //                         confirmButtonText: 'OK',
                        //                         confirmButtonColor: '#28a745'
                        //                     }).then((result) => {
                        //                         if (result.isConfirmed) {
                        //                             window.location.href = "<?php echo e(route('nonacademic.vancancy.application.home')); ?>";
                        //                         }
                        //                     });
                        //                 }, 2000);
                        //             } else {
                        //                 alert('Application submitted successfully, but PDF download is not available.');
                        //                 window.location.href = "<?php echo e(route('application.form.view')); ?>";
                        //             }
                        //         },
                            //     error: function(xhr, status, error) {
                            //         console.error(error);
                            //         alert('An error occurred while submitting your application. Please try again.');
                            //     }
                            // });
                        });

                        // Floating Save as Draft button functionality
                        $(window).scroll(function() {
                            // Show floating button when scrolled down 300px
                            if ($(this).scrollTop() > 300) {
                                $('#floating-save-draft').fadeIn();
                            } else {
                                $('#floating-save-draft').fadeOut();
                            }
                        });

                        // The floating Save as Draft button is already a submit button with name="save_draft"
                        // No need for additional click handler
                    });
                </script>
            </div>





    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.nonacademic.frontend_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\University\HRSystem\resources\views/frontend/nonacademic/application/application_form.blade.php ENDPATH**/ ?>