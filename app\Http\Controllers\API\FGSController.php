<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FGSController extends Controller
{
    public function employeeAccountCreation()
    {
        $data = Employee::select('employees.email', 'employees.last_name', 'employees.initials', 'employees.employee_no')->where('employees.employee_status_id', 110)->where('email', '!=', NULL)->get();
        return $data;
    }

    public function employeeAccountCreationAdminOfficer()
    {
        $data = Employee::select('employees.email', 'employees.last_name', 'employees.initials', 'employees.employee_no')->where('employees.employee_status_id', 110)->where('email', '!=', NULL)->where('employees.faculty_id', 5)->get();
        return $data;
    }

    public function employeeStudyBoardChair<PERSON>erson(){


        $data = Employee::Join('designations','designations.id','=','employees.designation_id')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->select('employees.employee_no', 'employees.last_name', 'employees.initials', 'category_name as title', 'departments.department_name','employees.email')
            ->where('employees.employee_status_id', 110)
            ->where('designations.main_group',97)
            ->whereIn('employees.employee_work_type',[138,139])
            ->get();

        return $data;
    }

    public function employeeStudyBoardChairPersonList(Request $request)
    {
        $empIDs = $request->input('empIDs', []);

        $data = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
        ->join('departments','departments.id','=','employees.department_id')
            ->select(
                'employees.employee_no',
                'employees.initials',
                'employees.last_name',
                'employees.name_denoted_by_initials',
                'employees.employee_status_id',
                'employees.email',
                'employees.mobile_no',
                'categories.category_name as title',
                'departments.department_name',
            )
            ->whereIn('employees.employee_no', $empIDs)
            ->get();

        return $data;

    }

    public function empFGSEmployeeDataGet(Request $request)
    {

        $data = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('faculties', 'employees.faculty_id', '=', 'faculties.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->select(
                    'employees.employee_no',
                    'employees.email',
                    'employees.last_name',
                    'employees.name_denoted_by_initials',
                    'employees.nic',
                    'designations.designation_name',
                    'faculties.faculty_name',
                    DB::raw('CASE WHEN employees.faculty_id IN (50, 51) THEN "None" ELSE faculties.faculty_name END AS faculty_name'),
                    'departments.department_name',
                    'designations.salary_code',
                    'employees.initials',
                    'employees.mobile_no',
                    'title.category_name as title',
                    'employees.employee_status_id'
                )
                ->where('employees.employee_no', $request->employee_no)
                //->where('employee_status_id', 110)
                ->get();

        return $data;

    }

    public function getDepAndFac(Request $request){

        $data = Employee::join('departments','departments.id','=','employees.department_id')
        ->join('faculties','faculties.id','=','employees.faculty_id')
        ->select(
            'employees.department_id',
            'employees.faculty_id',
            'departments.department_name',
            'faculties.faculty_name'
        )
        ->where('employees.employee_no',$request->empNo)
        ->get();

        return $data;
    }
}
