@extends('admin.admin_master')
@section('admin')

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1 class="m-0">Salary Revision 2025 Summary</h1>
            </div>
            <div class="col-sm-3">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="#">Home</a></li>
                    <li class="breadcrumb-item active">Internal Summary</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">

        {{-- Employees with Salary Revision --}}
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <div class="card card-success">
                        <div class="card-header">
                            <h3 class="card-title">Employees Data With Salary Revision 2025 Data</h3>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example5" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Employee No</th>
                                        <th>Name</th>
                                        <th>Main Branch</th>
                                        <th>Designation</th>
                                        <th>Department</th>
                                        <th>Working Type</th>
                                        <th>Employee Status</th>
                                        <th>Salary Revision Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($withSalaryRevision as $data)
                                    <tr>
                                        <td>{{ $data->employee_no }}</td>
                                        <td>{{ $data->title }} {{ $data->initials }} {{ $data->last_name }}</td>
                                        <td>{{ $data->mainBranch->category_name }}</td>
                                        <td>{{ $data->designationName->designation_name }}</td>
                                        <td>{{ $data->getDepartmentName->department_name }}</td>
                                        <td>
                                            @if ($data->employee_work_type == 138 || $data->employee_work_type == 139)
                                                <span
                                                    class="badge badge-pill badge-success">{{ $data->workTypeName->category_name }}</span>
                                            @elseif ($data->employee_work_type == 140)
                                                <span
                                                    class="badge badge-pill badge-info">{{ $data->workTypeName->category_name }}</span>
                                            @elseif ($data->employee_work_type == 141 || $data->employee_work_type == 142)
                                                <span
                                                    class="badge badge-pill badge-primary">{{ $data->workTypeName->category_name }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if ($data->employee_status_id == 110)
                                            <span class="badge badge-pill badge-success">Active</span>
                                            @else
                                            <span class="badge badge-pill badge-danger">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if ($data->status == 0)
                                            <span class="badge badge-pill badge-warning">Pending First Checking</span>
                                            @elseif($data->status == 1 && $data->checking2_status == 1)
                                            <span class="badge badge-pill badge-info">First Checking Done</span>
                                            @elseif($data->checking2_status == 2 && $data->accept_status == 1)
                                            <span class="badge badge-pill badge-primary">Second Checking Done</span>
                                            @elseif($data->accept_status == 2)
                                            <span class="badge badge-pill badge-success">Salary Revision Accepted</span>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Employees without Salary Revision --}}
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <div class="card card-warning">
                        <div class="card-header">
                            <h3 class="card-title">Employees Data Without Salary Revision 2025 Data</h3>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example6" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Employee No</th>
                                        <th>Name</th>
                                        <th>Main Branch</th>
                                        <th>Designation</th>
                                        <th>Department</th>
                                        <th>Working Type</th>
                                        <th>Employee Status</th>
                                        <th>Employee Staus Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($withoutSalaryRevision as $data)
                                    <tr>
                                        <td>{{ $data->employee_no }}</td>
                                        <td>{{ $data->title }} {{ $data->initials }} {{ $data->last_name }}</td>
                                        <td>{{ $data->mainBranch->category_name }}</td>
                                        <td>{{ $data->designationName->designation_name }}</td>
                                        <td>{{ $data->getDepartmentName->department_name }}</td>
                                        <td>
                                            @if ($data->employee_work_type == 138 || $data->employee_work_type == 139)
                                                <span
                                                    class="badge badge-pill badge-success">{{ $data->workTypeName->category_name }}</span>
                                            @elseif ($data->employee_work_type == 140)
                                                <span
                                                    class="badge badge-pill badge-info">{{ $data->workTypeName->category_name }}</span>
                                            @elseif ($data->employee_work_type == 141 || $data->employee_work_type == 142)
                                                <span
                                                    class="badge badge-pill badge-primary">{{ $data->workTypeName->category_name }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if ($data->employee_status_id == 110)
                                            <span class="badge badge-pill badge-success">Active</span>
                                            @else
                                            <span class="badge badge-pill badge-danger">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge badge-pill badge-dark">{{ $data->empStatusTypeName->category_name }}</span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Salary Revision Records without Employee --}}
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <div class="card card-danger">
                        <div class="card-header">
                            <h3 class="card-title">Salary Revision Records Without Matching Employee</h3>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example7" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Employee No</th>
                                        <th>Designation</th>
                                        <th>Service Category</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($withoutEmployee as $data)
                                    <tr>
                                        <td>{{ $data->emp_no }}</td>
                                        <td>{{ $data->designation ?? '-' }} - {{ $data->sal_code ?? '-' }} {{ $data->grade != NULL || $data->grade != '' ? '-' : '' }}{{ $data->grade ?? '-' }}</td>
                                        <td>{{ strtoupper($data->service_category) }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</section>

@endsection
