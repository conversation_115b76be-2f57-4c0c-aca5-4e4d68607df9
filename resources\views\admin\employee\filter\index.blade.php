@extends('admin.admin_master')
@section('admin')

    <section class="content">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-sm-6">
              <h3 class="m-0">Custom Employee Filter View</h3>

            </div><!-- /.col -->
            <div class="col-sm-6">
              {{-- <ol class="breadcrumb float-sm-right">
                <li class="breadcrumb-item"><a href="#">Home</a></li>
                <li class="breadcrumb-item active">Custom Employee Filter View</li>
              </ol> --}}
              <ol class="breadcrumb float-sm-right">
                <a href="{{ route('filter.view') }}" style="float: right;" class="btn btn-sm btn-primary mb-2">Home Filter</a>
            </ol>
            </div><!-- /.col -->
          </div><!-- /.row -->
        </div><!-- /.container-fluid -->
      </div>
      <div class="container-fluid">
        <!-- Small boxes (Stat box) -->
        <div class="row">
            @role('super-admin|administrator|est-head|stat')
            <div class="col-lg-3 col-6">
              <!-- small box -->
              <div class="small-box bg-primary">
                <div class="inner">
                  <h5>Report</h5>
                  <p>Active Employee Common Details Report</p>
                </div>
                <div class="icon bg-white">
                  {{-- <i class="ion ion-android-contact"></i> --}}
                </div>
                <a href="{{ route('common.report.1') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
              </div>
            </div>
            @endrole
          @role('super-admin|administrator|est-head')
          @auth
          @if (Auth()->user()->main_branch_id == 53 || Auth()->user()->main_branch_id == 51)
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-secondary">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Employee List Non Academic Establishment Branch</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-android-contact"></i> --}}
              </div>
              <a href="{{ route('custom.report.1') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endif
          @endauth
          @endrole
          <!-- ./col -->
          @role('super-admin|administrator|est-head')
          @auth
          @if (Auth()->user()->main_branch_id == 53 || Auth()->user()->main_branch_id == 51)
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-secondary">
              <div class="inner">
                <h5>Report</h5>
                <p>Non Academic Female Employee birthday Between 1978-1988</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-ios-briefcase"></i> --}}
              </div>
              <a href="{{ route('custom.report.2') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endif
          @endauth
          @endrole
          <!-- ./col -->
          @role('super-admin|administrator|est-head')
          @auth
          @if (Auth()->user()->main_branch_id == 53 || Auth()->user()->main_branch_id == 51)
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-secondary">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Employee List Non Academic Establishment Branch</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-university"></i> --}}
              </div>
              <a href="{{ route('custom.report.3') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endif
          @endauth
          @endrole
          <!-- ./col -->
          @role('super-admin|administrator|est-head')
          @auth
          @if (Auth()->user()->main_branch_id == 52 || Auth()->user()->main_branch_id == 51)
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-secondary">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Contract Employee List Academic Establishment Branch</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.4') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endif
          @endauth
          @endrole
          <!-- ./col -->
          @role('super-admin|administrator|est-head')
          @auth
          @if (Auth()->user()->main_branch_id == 53 || Auth()->user()->main_branch_id == 51)
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-secondary">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Technical Officer List Non Academic Establishment Branch</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.5') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endif
          @endauth
          @endrole
          <!-- ./col -->
          @role('super-admin|administrator')
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Employee List For Department of Inland Revenue</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.6') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endrole
          <!-- ./col -->
          @role('super-admin|administrator')
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Employee List For TAX Purpose</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.7') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endrole

          @role('super-admin|administrator|stat')
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Employee List ( {{ date('Y F') }} )</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.8') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endrole

          @role('super-admin|administrator|est-head')
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h5>Report</h5>
                <p>Retirements Employee List ( {{ date('Y') + 1 }} )</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.9') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endrole

          @role('super-admin|administrator|stat')
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h5>Report</h5>
                <p>Employee details for Public and Semi Government Sector census</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.10') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endrole

          @role('super-admin|administrator')
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Employee List For Research Awards</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.11') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endrole

          @role('super-admin|administrator')
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Employee List For Address List</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.12') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endrole

          @role('super-admin|administrator')
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Academic Employee List (Before 2024 Appointment) - Report</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.13') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endrole
          @role('super-admin|administrator')
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Employee List With Initial Appointment Date - Report</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.14') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endrole

          @role('super-admin|administrator|est-head')

          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Employee Salary Revision List - Report</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.15') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

          @endrole

          @role('super-admin|administrator|est-head')
          @auth
          @if (Auth()->user()->main_branch_id == 52 || Auth()->user()->main_branch_id == 51)
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h5>Report</h5>
                <p>Active Non Permanent Employee List - Report</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.16') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endif
          @endauth
          @endrole

           @role('super-admin|administrator')

          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h5>Report</h5>
                <p>Academic and Executive Employee List for Investigate Department- Report</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-trophy"></i> --}}
              </div>
              <a href="{{ route('custom.report.17') }}" class="small-box-footer">Generate Report <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

          @endrole
        </div>
        <!-- /.row -->

        <!-- Main row -->

        <!-- /.row (main row) -->
      </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
