@extends('admin.admin_master')
@section('admin')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-8">
                <h3 class="m-0">Active Non Permanent Employee List - Report</h3>
            </div><!-- /.col -->
            <div class="col-sm-4">
                <ol class="breadcrumb float-sm-right">
                    <a href="{{ route('custom.filter.view') }}" style="float: right;" class="btn btn-sm btn-success mb-2 mr-2">Back</a>
                    <a href="{{ route('filter.view') }}" style="float: right;" class="btn btn-sm btn-primary mb-2">Home Filter</a>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <!-- /.box-header -->
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example5" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="5%">SN</th>
                                        <th>EMP NUMBER</th>
                                        <th>EMPLOYEE NAME</th>
                                        <th>DESIGNATION</th>
                                        <th>FACULTY</th>
                                        <th>DEPARTMENT / DIVISION</th>
                                        <th>WORKING TYPE</th>
                                        <th>EMPLOYEE CATEGORY</th>
                                        <th>FILE OPERATOR</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($employees as $key => $employee)
                                    <tr>
                                        <td>{{ $key+1 }}</td>
                                        <td>{{ ucfirst($employee->employee_no) }}</td>
                                        <td>{{ $employee->title_name }} {{ strtoupper($employee->initials) }} {{ ucfirst($employee->last_name) }}</td>
                                        <td>{{ $employee->designationName->designation_name }} </td>

                                        <td>{{ $employee->getFacultyName->faculty_name }}</td>
                                        <td>
                                            @if($employee->name_status == 1)
                                            Department of {{ $employee->getDepartmentName->department_name }}
                                            @else
                                            {{ $employee->getDepartmentName->department_name }}
                                            @endif
                                        </td>
                                        <td>{{ $employee->workTypeName->category_name }}</td>

                                        <td>{{ $employee->ugs_mis_name }}</td>

                                        @if($employee->assign_ma_user_id != 0)
                                        <td>{{ $employee->getAssignOperator->initials }} {{ $employee->getAssignOperator->last_name }}</td>
                                        @else
                                        <td class="text-danger">No File Assign Operator</td>
                                        @endif
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- /.box-body -->
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection
