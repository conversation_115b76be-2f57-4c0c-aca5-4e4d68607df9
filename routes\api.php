<?php

use App\Http\Controllers\API\CommonAPIController;
use App\Http\Controllers\API\EmployeeDataChangeController;
use App\Http\Controllers\API\EmployeeDataListController;
use App\Http\Controllers\API\FGSController;
use App\Http\Controllers\API\InsuranceController;
use App\Http\Controllers\API\ResearchAllownece;
use App\Http\Controllers\API\USJNETController;
use App\Http\Controllers\API\VehicelPassController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|


Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
*/


Route::post('/non/user/eligibility',[USJNETController::class,'nonUserEligibility']);

Route::post('/user/eligibility',[USJNETController::class,'userEligibility']);

//research allownce system
Route::post('/user/detials',[ResearchAllownece::class,'userDetailsGet']);
Route::post('/employee/data/get',[ResearchAllownece::class,'employeeDataGet']);
Route::post('/employee/department/get',[ResearchAllownece::class,'employeeDepartmentGet']);
Route::get('/research/allownce/eligibility/count',[ResearchAllownece::class,'eligibilityCount']);
Route::post('/user/detials/all',[ResearchAllownece::class,'userDetailsGetAll']);

//insurance system
Route::post('/emp/insurance/get',[InsuranceController::class,'empDetailsGet']);
Route::post('/emp/insurance/desig',[InsuranceController::class,'empDesigGet']);
Route::post('/emp/insurance/dep',[InsuranceController::class,'empDepGet']);
Route::post('/emp/insurance/fac',[InsuranceController::class,'empFacGet']);
Route::post('/emp/insurance/all',[InsuranceController::class,'allEmp']);
Route::get('/insurance/eligibility/count',[InsuranceController::class,'eligibilityCount']);

//vehical Psss system
Route::post('/emp/vehicalpass/get',[VehicelPassController::class,'empDetailsGet']);
Route::post('/emp/vehicalpass/all',[VehicelPassController::class,'allEmp']);
Route::get('/vehicle/pass/eligibility/count',[VehicelPassController::class,'eligibilityCount']);

//common Api Controller
Route::post('/nic/data',[CommonAPIController::class,'nicDataGet']);

//employee data update Api
Route::put('/mobile/number/update/{empNo}',[EmployeeDataChangeController::class,'mobileNumberUpdate']);
Route::put('/email/update/{empNo}',[EmployeeDataChangeController::class,'emailUpdate']);

//employee data list Api
Route::middleware('apikey')->post('/admin/officer/eligibility/list',[EmployeeDataListController::class,'AdminOfficerEligibilityList']);

//fgs data list Api
Route::middleware('apikey')->post('/emp/fgs/account/creation',[FGSController::class,'employeeAccountCreation']);
Route::middleware('apikey')->post('/emp/fgs/account/creation/admin/officer',[FGSController::class,'employeeAccountCreationAdminOfficer']);
Route::middleware('apikey')->post('/emp/fgs/study/board/chair/person',[FGSController::class,'employeeStudyBoardChairPerson']);
Route::middleware('apikey')->post('/emp/fgs/study/board/chair/person/list',[FGSController::class,'employeeStudyBoardChairPersonList']);
Route::middleware('apikey')->post('/emp/fgs/employee/data/get',[FGSController::class,'empFGSEmployeeDataGet']);
Route::middleware('apikey')->post('/emp/fgs/employee/dep/fac',[FGSController::class,'getDepAndFac']);
