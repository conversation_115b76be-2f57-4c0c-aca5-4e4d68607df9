@extends('admin.admin_master')
@section('admin')
    <section class="content">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-sm-6">
              <h3 class="m-0">Internal System Summary</h3>

            </div><!-- /.col -->
            <div class="col-sm-6">
              <ol class="breadcrumb float-sm-right">
                <li class="breadcrumb-item"><a href="#">Home</a></li>
                <li class="breadcrumb-item active">Internal System Summary</li>
              </ol>
            </div><!-- /.col -->
          </div><!-- /.row -->
        </div><!-- /.container-fluid -->
      </div>
      <div class="container-fluid">
        <!-- Small boxes (Stat box) -->
        <div class="row">
            @role('super-admin')


          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3></h3>
                <p>Email With Employee Number Similarity List</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-ios-people"></i> --}}
              </div>
              <a href="{{ route('find.similar.email.employee') }}" class="small-box-footer">Show List <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3></h3>
                <p>NIC With Employee Number Similarity List</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-ios-people"></i> --}}
              </div>
              <a href="{{ route('find.similar.nic.employee') }}" class="small-box-footer">Show List <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3></h3>
                <p>Faculty Academic Zoom Employee List</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-ios-people"></i> --}}
              </div>
              <a href="{{ route('faculty.academic.zoom.employee') }}" class="small-box-footer">Show List <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3></h3>
                <p>Department Academic Zoom Employee List</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-ios-people"></i> --}}
              </div>
              <a href="{{ route('department.employee.count.list') }}" class="small-box-footer">Show List <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3></h3>
                <p>Service Termination Employee List&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-ios-people"></i> --}}
              </div>
              <a href="{{ route('service.termination.employee') }}" class="small-box-footer">Show List <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3></h3>
                <p>Leave Termination Employee List&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-ios-people"></i> --}}
              </div>
              <a href="{{ route('leave.termination.employee') }}" class="small-box-footer">Show List <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3></h3>
                <p>NIC With DOB Similarity List&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-ios-people"></i> --}}
              </div>
              <a href="{{ route('nic.dob.employee') }}" class="small-box-footer">Show List <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3></h3>
                <p>Profile Image Check Employee List&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-ios-people"></i> --}}
              </div>
              <a href="{{ route('photo.list.employee') }}" class="small-box-footer">Show List <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3></h3>
                <p>Salary Revision 2025 Employee List&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-ios-people"></i> --}}
              </div>
              <a href="{{ route('salary.revision.employee') }}" class="small-box-footer">Show List <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3></h3>
                <p>Taxpayer Idendification Number(TIN) Summary List&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
              </div>
              <div class="icon bg-white">
                {{-- <i class="ion ion-ios-people"></i> --}}
              </div>
              <a href="{{ route('tin.list.employee') }}" class="small-box-footer">Show List <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          @endrole

          <!-- ./col -->

        </div>
        <!-- /.row -->

        <!-- Main row -->

        <!-- /.row (main row) -->
      </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
