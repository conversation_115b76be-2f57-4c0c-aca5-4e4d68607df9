<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Designation extends Model
{
    use HasFactory,SoftDeletes,LogsActivity;

    protected $dates = ['deleted_at'];

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_designations')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function designationGroup()
    {
    	return $this->belongsTo(DesignationSubGroup::class);
    }

    public function mainBranch()
    {
        return $this->belongsTo(Category::class,'main_group');
    }

    public function ugcMis()
    {
        return $this->belongsTo(Category::class,'ugc_mis');
    }

    public function ugcFinance()
    {
        return $this->belongsTo(Category::class,'ugc_finance');
    }

    public function mainDesignationName()
    {
        return $this->belongsTo(DesignationMainGroup::class,'designation_main_id');
    }

    public function mainDivision()
    {
        return $this->belongsTo(Category::class,'designation_division');
    }

    public function staffGrade()
    {
        return $this->belongsTo(Category::class,'staff_grade');
    }

    public function category()
    {
        return $this->hasOne(Category::class, 'id', 'staff_grade');
    }

    public function carderDesignation()
    {
        return $this->belongsTo(CarderDesignation::class);
    }

    public function designationCategory(){

        return $this->belongsTo(Category::class,'service_category_id');
    }
}
