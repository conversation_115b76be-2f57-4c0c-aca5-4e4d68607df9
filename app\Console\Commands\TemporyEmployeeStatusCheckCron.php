<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Models\Promotion;
use Illuminate\Console\Command;

class TemporyEmployeeStatusCheckCron extends Command
{

    protected $signature = 'tempcheck:cron';


    protected $description = 'Tempory Employee Active status Check';


    public function handle()
    {
        $employees = Employee::whereDate('salary_termination_date_1', '<', date('Y-m-d'))
        ->where('employee_work_type', 140)
            ->where('employee_status_id', 110)
            ->get();

        foreach ($employees as $employee) {
            $employee->employee_status_id = 111;
            $employee->employee_status_type_id = 144;
            $employee->save();

            $this->employeeRecordAdd($employee->employee_no);
        }

        return Command::SUCCESS;
    }

    private function employeeRecordAdd($empNo)
    {

        $employee = Employee::find($empNo);

        $data = new Promotion();
        $data->employee_no = $empNo;
        $data->type_id = 188;
        $data->designation_id = $employee->designation_id;
        $data->duty_assumed_date = $employee->current_appointment_date;
        $data->basic_salary = $employee->current_basic_salary;
        $data->last_working_date = $employee->salary_termination_date_1;
        $data->descriptions = "Old Temporry Employee Service Record Added Automatically";
        $data->added_user_id = 12394;
        $data->save();

        return true;
    }
}
