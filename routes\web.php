<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\UserController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Backend\AcademicYear\AcademicYearsController;
use App\Http\Controllers\Backend\ApplicationForm\ApplicationFormFillController;
use App\Http\Controllers\Backend\Bond\BondController;
use App\Http\Controllers\Backend\Commendation\AddOldCommendationsController;
use App\Http\Controllers\Backend\Election\nacElectionController;
use App\Http\Controllers\Backend\Promotion\PromotionController;
use App\Http\Controllers\Backend\Employee\EmployeeController;
use App\Http\Controllers\Backend\Employee\EmployeeCreationController;
use App\Http\Controllers\Backend\Employee\NewEmployeeController;
use App\Http\Controllers\Backend\ExamBoard\ExamBoardController;
use App\Http\Controllers\Backend\Increment\AcInrementController;
use App\Http\Controllers\Backend\Increment\AcMyIncrementController;
use App\Http\Controllers\Backend\Increment\NonAcademicController;
use App\Http\Controllers\Backend\Transfer\InternalTransferController;
use App\Http\Controllers\Backend\Increment\IncrementController;
use App\Http\Controllers\Backend\Leave\AcademicLeaveController;
use App\Http\Controllers\Backend\Transfer\ExternalTransferController;
use App\Http\Controllers\Backend\Leave\LeaveController;
use App\Http\Controllers\Backend\Promotion\NonAcademicPromitionApplicationController;
use App\Http\Controllers\Backend\Promotion\NonAcademicPromotionController;
use App\Http\Controllers\Backend\Promotion\PromotionDurationController;
use App\Http\Controllers\Backend\Transfer\InternalTransferNacController;
use App\Http\Controllers\Backend\Vacancy\AcademicVancnyController;
use App\Http\Controllers\Backend\Vacancy\VacancyController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Frontend\IndexController;
use App\Http\Controllers\Frontend\ReportController;
use App\Http\Controllers\InterviewPanel\InterviewPanelController;
use App\Http\Controllers\SeedFileGenerator;
use App\Http\Controllers\Setting\EmailController;
use App\Http\Controllers\Setting\EmployeeUpadateController;
use App\Http\Controllers\Setting\SettingController;
use App\Http\Controllers\Setup\CategoryController;
use App\Http\Controllers\Setup\CategoryTypeController;
use App\Http\Controllers\Setup\CityController;
use App\Http\Controllers\Setup\DepartmentController;
use App\Http\Controllers\Setup\DepartmentHeadController;
use App\Http\Controllers\Setup\DepartmentSubController;
use App\Http\Controllers\Setup\DesignationController;
use App\Http\Controllers\Setup\DesignationMainGroupController;
use App\Http\Controllers\Setup\DesignationSubGroupController;
use App\Http\Controllers\Setup\FacultyController;
use App\Http\Controllers\Setup\FacultyDeanController;
use App\Http\Controllers\Setup\FacultyExecutiveOfficerController;
use App\Http\Controllers\Setup\SalaryScaleController;
use App\Http\Controllers\Setup\SalaryScaleVersionController;
use App\Http\Controllers\Setup\UniversityController;
use App\Http\Controllers\SSO\SSOController;
use App\Http\Controllers\Summary\ExternalSystemSummaryController;
use App\Http\Controllers\Summary\IndexSummaryController;
use App\Http\Controllers\Summary\InternalSummaryController;
use App\Http\Controllers\UsjnetController;
use App\Http\Controllers\Backend\Increment\ProgressStatusController;
use App\Http\Controllers\Backend\Transfer\InternalTransferNonAccController;
use App\Http\Controllers\Backend\Leave\AnnualLeaveController;
use App\Http\Controllers\Backend\SalaryRevision\SalaryRevision2025Controller;
use App\Http\Controllers\Backend\Vacancy\nacRecruitmentProcessController;
use App\Http\Controllers\Backend\Vacancy\NonAcademicVacancyController;
use App\Http\Controllers\Frontend\NonAcademicVacancyApplicationController;
use App\Http\Controllers\Frontend\TINController;
use App\Http\Controllers\Setting\FileOperatorChangeController;
use App\Http\Controllers\Setup\CarderController;
use App\Http\Controllers\Setup\CarderDepartmentController;
use App\Http\Controllers\Setup\CarderDesignationController;
use App\Models\AnnualLeave;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get("/sso/login", [SSOController::class, 'getLogin'])->name("sso.login");
Route::get("/callback", [SSOController::class, 'getCallback'])->name("sso.callback");
Route::get("/sso/connect", [SSOController::class, 'connectUser'])->name("sso.connect");
Route::get("/sso/availability", [SSOController::class, 'userAvailerbility']);




/**Frontend Route List */

Route::post('/seed/file', [SeedFileGenerator::class, 'show'])->name('seed_file_data');

Route::get('/seed/file/generator', [SeedFileGenerator::class, 'index'])->name('seed.file.gen');

Route::get('/email/test/hrms/{employee_no}', [EmailController::class, 'sendTestEmail'])->name('send.test.email');

Route::get('/', [IndexController::class, 'homePage'])->name('home');

/**New Application Related Route List*/

Route::match(['get', 'post'], '/vacancy/list', [IndexController::class, 'vacancyList'])->name('vacancy.list');

Route::post('/job/select', [IndexController::class, 'jobSelect'])->name('job.select');

Route::get('/applicant/login', [IndexController::class, 'applicantLogin'])->name('applicant.login');

Route::post('/mobile/verification', [IndexController::class, 'mobileVerification'])->name('mobile.verification');

Route::get('/otp/screen', [IndexController::class, 'otpScreen'])->name('otp.screen');

Route::post('/otp/resend', [IndexController::class, 'otpResend'])->name('otp.resend');

Route::post('/otp/confirmation', [IndexController::class, 'otpConfirmation'])->name('otp.confimation');

Route::get('/vacancy/application', [IndexController::class, 'vacancyApplication'])->name('vacancy.application');

/**Application Edit and Submit Related Route List*/
Route::get('/applicant/edit/login', [IndexController::class, 'applicantEditLogin'])->name('applicant.edit.login');

Route::post('/edit/mobile/verification', [IndexController::class, 'editMobileVerification'])->name('edit.mobile.verification');

Route::get('/edit/otp/screen', [IndexController::class, 'editOtpScreen'])->name('edit.otp.screen');

Route::post('/edit/otp/resend', [IndexController::class, 'editOtpResend'])->name('edit.otp.resend');

Route::post('/edit/otp/confirmation', [IndexController::class, 'editOtpConfirmation'])->name('edit.otp.confimation');

Route::get('/enroll/list', [IndexController::class, 'enrollList'])->name('enroll.list');

Route::post('/application/edit', [IndexController::class, 'applicationEdit'])->name('application.edit');

Route::post('/vacancy/application/submit', [IndexController::class, 'vacancyApplicationSubmit'])->name('vacancy.application.submit');

Route::get('/application/final', [IndexController::class, 'applicationFinal'])->name('application.final');

/***************************************************************************************************** */

Route::get('/application/data/download/ol/1', [IndexController::class, 'applicationDataDownloadOl1'])->name('application.data.download.ol1');

Route::get('/application/data/download/ol/2', [IndexController::class, 'applicationDataDownloadOl2'])->name('application.data.download.ol2');

Route::get('/application/data/download/al/1', [IndexController::class, 'applicationDataDownloadAl1'])->name('application.data.download.al1');

Route::get('/application/data/download/al/2', [IndexController::class, 'applicationDataDownloadAl2'])->name('application.data.download.al2');

Route::get('/application/data/download/release/letter', [IndexController::class, 'applicationDataDownloadReleaseLetter'])->name('application.data.download.release.letter');

/***************************************************************************************************** */

Route::delete('/degree/delete/{id}', [IndexController::class, 'degreeDelete'])->name('degree.delete');

Route::delete('/degree/old/delete/{id}', [IndexController::class, 'degreeOldDelete'])->name('degree.old.delete');

Route::delete('/degree/subject/delete/{id}', [IndexController::class, 'degreeSubjectDelete'])->name('degree.subject.delete');

Route::delete('/degree/subject/old/delete/{id}', [IndexController::class, 'degreeSubjectOldDelete'])->name('degree.subject.old.delete');

Route::get('/application/data/download/degree/{id}', [IndexController::class, 'applicationDataDownloadDegree'])->name('application.data.download.degree');

Route::delete('/application/data/delete/degree/{id}', [IndexController::class, 'applicationDataDeleteDegree'])->name('application.data.delete.degree');

Route::delete('/diploma/delete/{id}', [IndexController::class, 'diplomaDelete'])->name('diploma.delete');

Route::delete('/diploma/old/delete/{id}', [IndexController::class, 'diplomaOldDelete'])->name('diploma.old.delete');

Route::get('/application/data/download/diploma/{id}', [IndexController::class, 'applicationDataDownloadDiploma'])->name('application.data.download.diploma');

Route::delete('/application/data/delete/diploma/{id}', [IndexController::class, 'applicationDataDeleteDiploma'])->name('application.data.delete.diploma');

Route::delete('/squlification/delete/{id}', [IndexController::class, 'squlificationDelete'])->name('squlification.delete');

Route::delete('/squlification/old/delete/{id}', [IndexController::class, 'squlificationOldDelete'])->name('squlification.old.delete');

Route::delete('/membership/delete/{id}', [IndexController::class, 'membershipDelete'])->name('membership.delete');

Route::delete('/membership/old/delete/{id}', [IndexController::class, 'membershipOldDelete'])->name('membership.old.delete');

Route::delete('/pqulification/delete/{id}', [IndexController::class, 'pqulificationDelete'])->name('pqulification.delete');

Route::delete('/pqulification/old/delete/{id}', [IndexController::class, 'pqulificationOldDelete'])->name('pqulification.old.delete');

Route::delete('/employment/record/delete/{id}', [IndexController::class, 'employmentRecordDelete'])->name('employment.record.delete');

Route::delete('/employment/record/old/delete/{id}', [IndexController::class, 'employmentRecordOldDelete'])->name('employment.record.old.delete');

Route::get('/application/data/download/employment/record/{id}', [IndexController::class, 'applicationDataDownloadEmploymentRecord'])->name('application.data.download.employment.record');

Route::delete('/application/data/delete/employment/record/{id}', [IndexController::class, 'applicationDataDeleteEmploymentRecord'])->name('application.data.delete.employment.record');

Route::delete('/bond/record/delete/{id}', [IndexController::class, 'bondRecordDelete'])->name('bond.record.delete');

Route::delete('/bond/record/old/delete/{id}', [IndexController::class, 'bondRecordOldDelete'])->name('bond.record.old.delete');

Route::delete('/referee/record/delete/{id}', [IndexController::class, 'refereeRecordDelete'])->name('referee.record.delete');

Route::delete('/referee/record/old/delete/{id}', [IndexController::class, 'refereeRecordOldDelete'])->name('referee.record.old.delete');

Route::get('/release/letter/download', [IndexController::class, 'releaseLetterDownload'])->name('release.letter.download');

Route::get('/notice/download/{id}', [IndexController::class, 'noticeDownload'])->name('notice.download');

Route::get('/sor/1/download', [IndexController::class, 'sorDownload1'])->name('sor.download1');

Route::get('/sor/2/download', [IndexController::class, 'sorDownload2'])->name('sor.download2');

Route::get('/application/detail/instruction', [IndexController::class, 'detailInstruction'])->name('detail.instruction');

Route::get('/application/photograph/instruction/doc', [IndexController::class, 'photographInstruction'])->name('photograph.instruction');

/****************************************************************************************** */

Route::get('/final/report/download', [ReportController::class, 'finalReportDownload'])->name('final.report.download');

Route::post('list/report/download', [ReportController::class, 'listReportDownload'])->name('list.report.download');

Route::get('merge/report/download', [ReportController::class, 'mergeReportDownload'])->name('merge.report.download');


/*****************Promotion application related route list ******************************/

//non Academic application

Route::get('/promotion/nac/application/index', [NonAcademicPromitionApplicationController::class, 'openPage1'])->name('nonAcademic.promotion.application.page1.open');

Route::post('/promotion/nac/application', [NonAcademicPromitionApplicationController::class, 'index'])->name('nonAcademic.promotion.application.index');

Route::post('/promotion/nac/application/store', [NonAcademicPromitionApplicationController::class, 'store'])->name('nonAcademic.promotion.application.store');

Route::get('/promotion/nac/application/final', [NonAcademicPromitionApplicationController::class, 'final'])->name('nonAcademic.promotion.application.final');

Route::match(['get', 'post'], '/promotion/nac/application/status', [NonAcademicPromitionApplicationController::class, 'applicationStatus'])->name('nonAcademic.promotion.application.status');

Route::get('/nonAcademic/promotion/notice', [NonAcademicPromitionApplicationController::class, 'openPdf'])->name('nac.promotion.notice');

/*************************************************************************** */

//Route::get('/notice/download/{id}', [IndexController::class, 'VacancyNoticeDownload'])->name('vancancy.notice.download');

Route::get('/notice/show/{id}', [VacancyController::class, 'VacancyNoticeShow'])->name('vacancy.notice.show');

Route::get('/degree/pdf/{id}', [AcademicVancnyController::class, 'DegreePdf'])->name('degree.pdf');

Route::get('/diploma/pdf/{id}', [AcademicVancnyController::class, 'DiplomaPdf'])->name('diploma.pdf');

Route::get('/emprecord/pdf/{id}', [AcademicVancnyController::class, 'EmpRecordPdf'])->name('emprecord.pdf');

Route::get('/release/pdf/{id}', [AcademicVancnyController::class, 'ReleasePdf'])->name('release.pdf');

//*************************** ac/as increment ********************************************/
//self evaluation
Route::get('/ac/selEval/page1/open', [AcMyIncrementController::class, 'acUsj1stPageOpen'])->name('ac.incre.selfEval.page1.open');
Route::get('/ac/selEval/open', [AcMyIncrementController::class, 'selfEvalOpen'])->name('ac.incre.selfEval.open');
Route::post('/ac/selEval/store', [AcMyIncrementController::class, 'serSubmit'])->name('ac.incre.selfEval.store');
Route::post('/ac/selEval/save', [AcMyIncrementController::class, 'serSave'])->name('ac.incre.ser_save');
//self assessment
Route::get('/as/selAssessment/open', [AcMyIncrementController::class, 'selfAssessmentOpen'])->name('as.incre.selAssessment.open');
Route::post('/as/selAssessment/store', [AcMyIncrementController::class, 'sel_assessment_store'])->name('as.incre.selAssessment.store');
//SER print
Route::get('academic/increment/ser/print/{id}', [AcMyIncrementController::class, 'serPrint'])->name('ac.incre.ser.print');

//******************** nac increment ************************************************************ */
Route::get('/increment/progress/{id}', [ProgressStatusController::class, 'incrementStatus'])->name('increment.progress');


//***********************Non Academic Recruitment ********************************************** */

Route::get('/vacancies/nonacademic', [NonAcademicVacancyApplicationController::class, 'index'])->name('nonacademic.vancancy.application.home');

Route::match(['get', 'post'], '/non/academic/vanancy/list', [NonAcademicVacancyApplicationController::class, 'vacancyList'])->name('nonacademic.vacancy.list');

Route::get('/non/academic/application/detail/instruction', [NonAcademicVacancyApplicationController::class, 'detailInstruction'])->name('nonacademic.detail.instruction');

Route::post('/non/academic/job/select', [NonAcademicVacancyApplicationController::class, 'jobSelect'])->name('nonacademic.job.select');

Route::get('/non/academic/applicant/login', [NonAcademicVacancyApplicationController::class, 'applicantLogin'])->name('nonacademic.applicant.login');

Route::post('/user/verification', [NonAcademicVacancyApplicationController::class, 'userVerification'])->name('user.verification');

Route::get('/non/academic/application/index', [ApplicationFormFillController::class, 'FormIndex'])->name('application.form.view');

Route::match(['get','post'],'application/form/add', [ApplicationFormFillController::class, 'FormSave'])->name('application.form.save');

Route::match(['get','post'],'application/form/pdf/{id}', [ApplicationFormFillController::class, 'print_navpdf'])->name('application.form.pdf');

Route::get('application/drafts', [ApplicationFormFillController::class, 'draftApplications'])->name('application.drafts');

Route::get('application/draft/delete/{id}', [ApplicationFormFillController::class, 'deleteDraftApplication'])->name('application.draft.delete');

Route::get('/sor/3/download', [NonAcademicVacancyApplicationController::class, 'sorDownload3'])->name('sor.download3');
Route::get('application/nic-document/{id}', [ApplicationFormFillController::class, 'viewNicDocument'])->name('application.nic.document');
Route::get('application/document/{id}', [ApplicationFormFillController::class, 'viewDocument'])->name('application.document');


//down page
Route::get('application/down/page', [ApplicationFormFillController::class, 'downPageOpen'])->name('application.down');
Route::get('nac/application/submit/page/{id}', [ApplicationFormFillController::class, 'submitPageOpen'])->name('nac.application.submit.open');

//TIN number collect
Route::get('/tin/index', [TINController::class, 'TINIndex'])->name('tin.index');
Route::post('/tin/submit', [TINController::class, 'TINSubmit'])->name('tin.submit');
Route::get('/tin/success/{empNo}', [TINController::class, 'TINSuccess'])->name('tin.success');
Route::get('/tin/pending/reminder/mail', [TINController::class, 'TINPendingReminderMail'])->name('tin.pending.reminder.mail')->middleware('role:super-admin');


/*Admin Panal Route List*/

Route::group(['middleware' => 'prevent-back-history'], function () {

	//Admin Related Route

	Route::get('/dashboard', [IndexSummaryController::class, 'dashboard'])->name('admin.dashboard');

	Route::get('/admin/logout', [AuthenticatedSessionController::class, 'destroy'])->name('admin.logout');

	//Route::get('/admin/profile',[AdminController::class,'adminProfile'])->name('admin.profile');

	//Route::get('/admin/profile',[AdminController::class,'adminProfile'])->name('admin.profile');

	Route::get('/notification', [IndexSummaryController::class, 'notification'])->name('notification');

	Route::get('/notification/read/{id}', [IndexSummaryController::class, 'notificationRead'])->name('notification.read');

	Route::get('/notification/unread/{id}', [IndexSummaryController::class, 'notificationUnread'])->name('notification.unread');

	Route::get('/notification/delete/{id}', [IndexSummaryController::class, 'notificationDelete'])->name('notification.delete');

	Route::get('/department/load/ajax/{faculty_id}', [Controller::class, 'departmentLoad']);

	Route::get('/sub/department/load/ajax/{department_id}', [Controller::class, 'subDepartmentLoad']);

	Route::get('/district/load/ajax/{province_id}', [Controller::class, 'districtLoad']);

	Route::get('/city/load/ajax/{district_id}', [Controller::class, 'cityLoad']);

	Route::get('/employee/status/type/load/ajax/{employee_status_id}', [Controller::class, 'employeeStatusTypeLoad']);

	Route::get('/designation/main/group/load/ajax/{designation_id}', [Controller::class, 'designationMainGroupLoad']);

	Route::get('/sub/designation/load/ajax/{designation_main_id}', [Controller::class, 'subDesignationGroupLoad']);

	Route::get('/designation/load/ajax/{designation_main_id}', [Controller::class, 'DesignationLoad']);

	Route::get('/employee/group/load/ajax/{main_branch_id}', [Controller::class, 'EmployeeGroupLoad']);

	Route::get('/degree/class/load/ajax/{degree_type}', [Controller::class, 'EmployeeDegreeClass']);

	Route::get('/department/all/load/ajax', [Controller::class, 'departmentAllLoad']);

	Route::get('/vacancy/department/load/ajax/{faculty_id}', [Controller::class, 'vacancyDepartmentLoad']);

	Route::get('/ajax/autocomplete/search', [Controller::class, 'selectSearch'])->name('autocomplete');

	Route::post('/check/application/status/count', [Controller::class, 'checkApplicationStatusCount'])->name('check.application.status.count');

	Route::post('/check/application/department/info', [Controller::class, 'checkApplicationDepartmentInfo'])->name('check.application.department.info');

	Route::post('/check/application/head/status/count', [Controller::class, 'checkApplicationHeadStatusCount'])->name('check.application.head.status.count');

	Route::post('/check/application/finalize/status/count', [Controller::class, 'checkApplicationFinalizeStatusCount'])->name('check.application.finalize.status.count');

	Route::post('/check/application/vacancy/final', [Controller::class, 'checkApplicationVacancyFinal'])->name('check.application.vacancy.final');

	require __DIR__ . '/auth.php';

	Route::middleware('role:user')->prefix('profile')->group(function () {

		Route::get('/view', [AdminController::class, 'ProfileView'])->name('profile.view');

		Route::get('/password/view', [AdminController::class, 'PasswordView'])->name('password.view');

		Route::post('/password/update', [AdminController::class, 'PasswordUpdate'])->name('admin.password.update');
	}); //user profile controller route list

	Route::middleware('role:super-admin|administrator|est-head')->prefix('user')->group(function () {

		Route::get('/view', [UserController::class, 'UserView'])->name('user.view');
		Route::get('/add/view', [UserController::class, 'UserAdd'])->name('user.add');
		Route::post('/store', [UserController::class, 'UserStore'])->name('user.store');
		Route::get('/edit/{id}', [UserController::class, 'UserEdit'])->name('user.edit');
		Route::post('/update/{id}', [UserController::class, 'UserUpdate'])->name('user.update');
		Route::get('/delete/{id}', [UserController::class, 'UserDelete'])->name('user.delete');
		Route::get('/inactive/{id}', [UserController::class, 'UserInactive'])->name('user.inactive');
		Route::get('/active/{id}', [UserController::class, 'UserActive'])->name('user.active');
		Route::post('/users/{user}/roles', [UserController::class, 'assignRole'])->name('users.roles');
		Route::get('/users/{user}/roles/{role}', [UserController::class, 'removeRole'])->name('users.roles.remove');
		Route::post('/users/{user}/permissions', [UserController::class, 'givePermission'])->name('users.permissions');
		Route::get('/users/{user}/permissions/{permission}', [UserController::class, 'revokePermission'])->name('users.permissions.revoke');
		Route::get('/detail/{id}', [UserController::class, 'UserDetail'])->name('user.detail');
		Route::get('/role/list', [UserController::class, 'UserRoleList'])->name('user.role.list');
		Route::get('/active/log/view/{id}', [UserController::class, 'UserActiveLogView'])->name('user.active.log.view');

		Route::get('/current/view', [UserController::class, 'CurrentUserView'])->name('current.user.view');
		Route::get('/session/remove/{id}', [UserController::class, 'SessionRemove'])->name('session.remove');

		Route::match(['get', 'post'], '/login/history/view', [UserController::class, 'loginHistoryView'])->name('login.history.view');
		Route::get('/login/history/search', [UserController::class, 'loginHistorySearch'])->name('login.history.search');

		Route::match(['get', 'post'], '/active/log/history/view', [UserController::class, 'ActiveLogHistoryView'])->name('active.log.history.view');
	}); //user manage controller route list

	//Role Related Route List
	Route::middleware('role:super-admin|administrator')->prefix('role')->group(function () {

		Route::get('/index', [RoleController::class, 'roleIndex'])->name('role.index');
		Route::get('/add', [RoleController::class, 'roleAdd'])->name('role.add');
		Route::post('/store', [RoleController::class, 'roleStore'])->name('role.store');
		Route::get('/edit/{id}', [RoleController::class, 'roleEdit'])->name('role.edit');
		Route::post('/update/{id}', [RoleController::class, 'roleUpdate'])->name('role.update');
		Route::get('/delete/{id}', [RoleController::class, 'roleDelete'])->name('role.delete');
		Route::post('permission/role/update/{role}', [RoleController::class, 'permissionRoleUpdate'])->name('role.permission.update');
		Route::get('/roles/{role}/permissions/{permission}', [RoleController::class, 'revokePermission'])->name('roles.permissions.revoke');
	});

	//Permission Related Route List
	Route::middleware('role:super-admin')->prefix('permission')->group(function () {

		Route::get('/index', [PermissionController::class, 'permissionIndex'])->name('permission.index');
		Route::get('/add', [PermissionController::class, 'permissionAdd'])->name('permission.add');
		Route::post('/store', [PermissionController::class, 'permissionStore'])->name('permission.store');
		Route::get('/edit/{id}', [PermissionController::class, 'permissionEdit'])->name('permission.edit');
		Route::post('/update/{id}', [PermissionController::class, 'permissionUpdate'])->name('permission.update');
		Route::get('/delete/{id}', [PermissionController::class, 'permissionDelete'])->name('permission.delete');
		Route::post('/permissions/{permission}/roles', [PermissionController::class, 'assignRole'])->name('permissions.roles');
		Route::get('/permissions/{permission}/roles/{role}', [PermissionController::class, 'removeRole'])->name('permissions.roles.remove');
	});

	//Setup Related Route List //
	Route::middleware('role:super-admin|administrator|est-head|cc|sc|stat')->prefix('setups')->group(function () {

		Route::get('/catagory/type/view', [CategoryTypeController::class, 'CategoryTypeIndex'])->name('category.type.index');
		Route::get('/catagory/type/add', [CategoryTypeController::class, 'CategoryTypeAdd'])->name('category.type.add');
		Route::post('/category/type/store', [CategoryTypeController::class, 'CategoryTypeStore'])->name('category.type.store');
		Route::get('/category/type/edit/{id}', [CategoryTypeController::class, 'CategoryTypeEdit'])->name('category.type.edit');
		Route::post('/category/type/update/{id}', [CategoryTypeController::class, 'CategoryTypeUpdate'])->name('category.type.update');
		Route::get('/category/type/delete/{id}', [CategoryTypeController::class, 'CategoryTypeDelete'])->name('category.type.delete');
		Route::get('/category/list/{id}', [CategoryTypeController::class, 'CategoryList'])->name('category.list');

		Route::get('/category/view', [CategoryController::class, 'CategoryIndex'])->name('category.index');
		Route::get('/category/add', [CategoryController::class, 'CategoryAdd'])->name('category.add');
		Route::post('/category/store', [CategoryController::class, 'CategoryStore'])->name('category.store');
		Route::get('/category/edit/{id}', [CategoryController::class, 'CategoryEdit'])->name('category.edit');
		Route::post('/category/update/{id}', [CategoryController::class, 'CategoryUpdate'])->name('category.update');
		Route::get('/category/delete/{id}', [CategoryController::class, 'CategoryDelete'])->name('category.delete');

		Route::get('/faculty/view', [FacultyController::class, 'facultyIndex'])->name('faculty.index');
		Route::get('/faculty/add', [FacultyController::class, 'facultyAdd'])->name('faculty.add');
		Route::post('/faculty/store', [FacultyController::class, 'facultyStore'])->name('faculty.store');
		Route::get('/faculty/edit/{id}', [FacultyController::class, 'facultyEdit'])->name('faculty.edit');
		Route::post('/faculty/update/{id}', [FacultyController::class, 'facultyUpdate'])->name('faculty.update');
		Route::get('/faculty/softdelete/{id}', [FacultyController::class, 'facultySoftdelete'])->name('faculty.soft.delete');
		Route::get('/faculty/restore/{id}', [FacultyController::class, 'facultyRestore'])->name('faculty.restore');
		Route::get('/faculty/delete/{id}', [FacultyController::class, 'facultyDelete'])->name('faculty.delete');
		Route::get('/department/list/{id}', [FacultyController::class, 'departmentList'])->name('department.list');

		Route::get('/faculty/dean/view', [FacultyDeanController::class, 'facultyDeanIndex'])->name('faculty.dean.index');

		Route::get('/department/head/view', [DepartmentHeadController::class, 'departmentHeadIndex'])->name('department.head.index');
		Route::get('/department/head/add', [DepartmentHeadController::class, 'departmentHeadAdd'])->name('department.head.add');
		Route::post('/department/head/store', [DepartmentHeadController::class, 'departmentHeadStore'])->name('department.head.store');
		Route::get('/department/head/edit/{id}', [DepartmentHeadController::class, 'departmentHeadEdit'])->name('department.head.edit');
		Route::post('/department/head/update/{id}', [DepartmentHeadController::class, 'departmentHeadUpdate'])->name('department.head.update');
		Route::get('/department/head/delete/{id}', [DepartmentHeadController::class, 'departmentHeadDelete'])->name('department.head.delete');

		Route::get('/department/view', [DepartmentController::class, 'departmentIndex'])->name('department.index');
		Route::get('/department/add', [DepartmentController::class, 'departmentAdd'])->name('department.add');
		Route::post('/department/store', [DepartmentController::class, 'departmentStore'])->name('department.store');
		Route::get('/department/edit/{id}', [DepartmentController::class, 'departmentEdit'])->name('department.edit');
		Route::post('/department/update/{id}', [DepartmentController::class, 'departmentUpdate'])->name('department.update');
		Route::get('/department/softdelete/{id}', [DepartmentController::class, 'departmentSoftdelete'])->name('department.soft.delete');
		Route::get('/department/restore/{id}', [DepartmentController::class, 'departmentRestore'])->name('department.restore');
		Route::get('/department/delete/{id}', [DepartmentController::class, 'departmentDelete'])->name('department.delete');

		Route::get('/department/sub/view', [DepartmentSubController::class, 'departmentSubIndex'])->name('department.sub.index');
		Route::get('/department/sub/add', [DepartmentSubController::class, 'departmentSubAdd'])->name('department.sub.add');
		Route::post('/department/sub/store', [DepartmentSubController::class, 'departmentSubStore'])->name('department.sub.store');
		Route::get('/department/sub/edit/{id}', [DepartmentSubController::class, 'departmentSubEdit'])->name('department.sub.edit');
		Route::post('/department/sub/update/{id}', [DepartmentSubController::class, 'departmentSubUpdate'])->name('department.sub.update');
		Route::get('/department/sub/softdelete/{id}', [DepartmentSubController::class, 'departmentSubSoftdelete'])->name('department.sub.soft.delete');
		Route::get('/department/sub/restore/{id}', [DepartmentSubController::class, 'departmentSubRestore'])->name('department.sub.restore');
		Route::get('/department/sub/delete/{id}', [DepartmentSubController::class, 'departmentSubDelete'])->name('department.sub.delete');

		Route::get('/designation/main/group/view', [DesignationMainGroupController::class, 'designationMainGroupIndex'])->name('designation.main.group.index');
		Route::get('/designation/main/group/add', [DesignationMainGroupController::class, 'designationMainGroupAdd'])->name('designation.main.group.add');
		Route::post('/designation/main/group/store', [DesignationMainGroupController::class, 'designationMainGroupStore'])->name('designation.main.group.store');
		Route::get('/designation/main/group/edit/{id}', [DesignationMainGroupController::class, 'designationMainGroupEdit'])->name('designation.main.group.edit');
		Route::post('/designation/main/group/update/{id}', [DesignationMainGroupController::class, 'designationMainGroupUpdate'])->name('designation.main.group.update');
		Route::get('/designation/main/group/softdelete/{id}', [DesignationMainGroupController::class, 'designationMainGroupSoftdelete'])->name('designation.main.group.soft.delete');
		Route::get('/designation/main/group/restore/{id}', [DesignationMainGroupController::class, 'designationMainGroupRestore'])->name('designation.main.group.restore');
		Route::get('/designation/main/group/delete/{id}', [DesignationMainGroupController::class, 'designationMainGroupDelete'])->name('designation.main.group.delete');
		Route::get('/designation/sub/group/list/{id}', [DesignationMainGroupController::class, 'designationSubGroupList'])->name('designation.sub.group.list');

		Route::get('/designation/sub/group/view', [DesignationSubGroupController::class, 'designationSubGroupIndex'])->name('designation.sub.group.index');
		Route::get('/designation/sub/group/add', [DesignationSubGroupController::class, 'designationSubGroupAdd'])->name('designation.sub.group.add');
		Route::post('/designation/sub/group/store', [DesignationSubGroupController::class, 'designationSubGroupStore'])->name('designation.sub.group.store');
		Route::get('/designation/sub/group/edit/{id}', [DesignationSubGroupController::class, 'designationSubGroupEdit'])->name('designation.sub.group.edit');
		Route::post('/designation/sub/group/update/{id}', [DesignationSubGroupController::class, 'designationSubGroupUpdate'])->name('designation.sub.group.update');
		Route::get('/designation/sub/group/softdelete/{id}', [DesignationSubGroupController::class, 'designationSubGroupSoftdelete'])->name('designation.sub.group.soft.delete');
		Route::get('/designation/sub/group/restore/{id}', [DesignationSubGroupController::class, 'designationSubGroupRestore'])->name('designation.sub.group.restore');
		Route::get('/designation/sub/group/delete/{id}', [DesignationSubGroupController::class, 'designationSubGroupDelete'])->name('designation.sub.group.delete');

		Route::get('/designation/view', [DesignationController::class, 'designationIndex'])->name('designation.index');
		Route::get('/designation/add', [DesignationController::class, 'designationAdd'])->name('designation.add');
		Route::post('/designation/store', [DesignationController::class, 'designationStore'])->name('designation.store');
		Route::get('/designation/edit/{id}', [DesignationController::class, 'designationEdit'])->name('designation.edit');
		Route::post('/designation/update/{id}', [DesignationController::class, 'designationUpdate'])->name('designation.update');
		Route::get('/designation/softdelete/{id}', [DesignationController::class, 'designationSoftdelete'])->name('designation.soft.delete');
		Route::get('/designation/restore/{id}', [DesignationController::class, 'designationRestore'])->name('designation.restore');
		Route::get('/designation/delete/{id}', [DesignationController::class, 'designationDelete'])->name('designation.delete');
		Route::get('/designation/inactive/{id}', [DesignationController::class, 'designationInactive'])->name('designation.inactive');
		Route::get('/designation/active/{id}', [DesignationController::class, 'designationActive'])->name('designation.active');

		Route::get('/city/view', [CityController::class, 'cityIndex'])->name('city.index');
		Route::get('/city/add', [CityController::class, 'cityAdd'])->name('city.add');
		Route::post('/city/store', [CityController::class, 'cityStore'])->name('city.store');
		Route::get('/city/edit/{id}', [CityController::class, 'cityEdit'])->name('city.edit');
		Route::post('/city/update/{id}', [CityController::class, 'cityUpdate'])->name('city.update');
		Route::get('/city/softdelete/{id}', [CityController::class, 'citySoftdelete'])->name('city.soft.delete');
		Route::get('/city/restore/{id}', [CityController::class, 'cityRestore'])->name('city.restore');
		Route::get('/city/delete/{id}', [CityController::class, 'cityDelete'])->name('city.delete');

		Route::get('/university/view', [UniversityController::class, 'universityIndex'])->name('university.index');
		Route::get('/university/add', [UniversityController::class, 'universityAdd'])->name('university.add');
		Route::post('/university/store', [UniversityController::class, 'universityStore'])->name('university.store');
		Route::get('/university/edit/{id}', [UniversityController::class, 'universityEdit'])->name('university.edit');
		Route::post('/university/update/{id}', [UniversityController::class, 'universityUpdate'])->name('university.update');
		Route::get('/university/softdelete/{id}', [UniversityController::class, 'universitySoftdelete'])->name('university.soft.delete');
		Route::get('/university/restore/{id}', [UniversityController::class, 'universityRestore'])->name('university.restore');
		Route::get('/university/delete/{id}', [UniversityController::class, 'universityDelete'])->name('university.delete');

		Route::get('/salary/scale/view', [SalaryScaleController::class, 'SalaryScsaleIndex'])->name('salary.scale.index');
		Route::get('/salary/scale/add', [SalaryScaleController::class, 'SalaryScsaleAdd'])->name('salary.scale.add');
		Route::post('/salary/scale/store', [SalaryScaleController::class, 'SalaryScsaleStore'])->name('salary.scale.store');
		Route::get('/salary/scale/edit/{id}', [SalaryScaleController::class, 'SalaryScsaleEdit'])->name('salary.scale.edit');
        Route::get('/salary/scale/show/{id}', [SalaryScaleController::class, 'SalaryScsaleShow'])->name('salary.scale.show');
		Route::post('/salary/scale/update/{id}', [SalaryScaleController::class, 'SalaryScsaleUpdate'])->name('salary.scale.update');
		Route::get('/salary/scale/delete/{id}', [SalaryScaleController::class, 'SalaryScsaleDelete'])->name('salary.scale.delete');

        // Salary Scale Version Routes
        Route::get('/salary/scale/version', [SalaryScaleVersionController::class, 'index'])->name('salary.scale.version.index');
        Route::get('/salary/scale/version/create', [SalaryScaleVersionController::class, 'create'])->name('salary.scale.version.create');
        Route::post('/salary/scale/version', [SalaryScaleVersionController::class, 'store'])->name('salary.scale.version.store');
        Route::get('/salary/scale/version/{id}/edit', [SalaryScaleVersionController::class, 'edit'])->name('salary.scale.version.edit');
        Route::put('/salary/scale/version/{id}', [SalaryScaleVersionController::class, 'update'])->name('salary.scale.version.update');
        Route::delete('/salary/scale/version/{id}', [SalaryScaleVersionController::class, 'destroy'])->name('salary.scale.version.destroy');
        Route::post('/salary/scale/version/{id}/toggle-status', [SalaryScaleVersionController::class, 'toggleStatus'])->name('salary.scale.version.toggle-status');

        Route::get('/faculty/executive/officer/view', [FacultyExecutiveOfficerController::class, 'FacultyExecutiveOfficerView'])->name('faculty.executive.officer.index');

        Route::get('/designation/carder/view', [CarderController::class, 'CarderDesignationIndex'])->name('carder.designation.index');
        Route::get('/designation/carder/add', [CarderController::class, 'CarderDesignationAdd'])->name('carder.designation.add');
        Route::post('/designation/carder/store', [CarderController::class, 'CarderDesignationStore'])->name('carder.designation.store');
        Route::get('/designation/carder/edit/{id}', [CarderController::class, 'CarderDesignationEdit'])->name('carder.designation.edit');
        Route::post('/designation/carder/update/{id}', [CarderController::class, 'CarderDesignationUpdate'])->name('carder.designation.update');
        Route::get('/designation/carder/delete/{id}', [CarderController::class, 'CarderDesignationDelete'])->name('carder.designation.delete');

        Route::get('/designation/carder/assign/view', [CarderDesignationController::class, 'CarderDesignationAssignIndex'])->name('carder.designation.assign.index');
        Route::get('/designation/carder/assign/add/{id}', [CarderDesignationController::class, 'CarderDesignationAssignAdd'])->name('carder.designation.assign.add');
        Route::post('/designation/carder/assign/store', [CarderDesignationController::class, 'CarderDesignationAssignStore'])->name('carder.designation.assign.store');
        Route::get('/designation/carder/assign/edit/{id}', [CarderDesignationController::class, 'CarderDesignationAssignEdit'])->name('carder.designation.assign.edit');
        Route::get('/designation/carder/assign/delete/{id}', [CarderDesignationController::class, 'CarderDesignationAssignDelete'])->name('carder.designation.assign.delete');

        Route::get('/department/carder/assign/view', [CarderDepartmentController::class, 'CarderDepartmentAssignIndex'])->name('carder.department.assign.index');
        Route::get('/department/carder/assign/add/{id}', [CarderDepartmentController::class, 'CarderDepartmentAssignAdd'])->name('carder.department.assign.add');
        Route::get('/department/carder/assign/edit/{id}', [CarderDepartmentController::class, 'CarderDepartmentAssignEdit'])->name('carder.department.assign.edit');
        Route::post('/department/carder/assign/update/{id}', [CarderDepartmentController::class, 'CarderDepartmentAssignUpdate'])->name('carder.department.assign.update');
        Route::post('/department/carder/assign/store', [CarderDepartmentController::class, 'CarderDepartmentAssignStore'])->name('carder.department.assign.store');
        Route::get('/department/carder/assign/show/{id}', [CarderDepartmentController::class, 'CarderDepartmentAssignShow'])->name('carder.department.assign.show');
        Route::get('/department/carder/assign/delete/{id}', [CarderDepartmentController::class, 'CarderDepartmentAssignDelete'])->name('carder.department.assign.delete');
	});

	//Vacancy Related Route List
	Route::middleware('role:super-admin|administrator|est-head|cc|sc|head')->prefix('vacancy')->group(function () {

		Route::get('/view', [VacancyController::class, 'VacancyIndex'])->name('vacancy.index');
		Route::get('/trash/view', [VacancyController::class, 'VacancyTrashIndex'])->name('vacancy.trash.index');
		Route::get('/add', [VacancyController::class, 'VacancyAdd'])->name('vacancy.add');
		Route::post('/store', [VacancyController::class, 'VacancyStore'])->name('vacancy.store');
		Route::get('/edit/{id}', [VacancyController::class, 'VacancyEdit'])->name('vacancy.edit');
		Route::post('/update/{id}', [VacancyController::class, 'VacancyUpdate'])->name('vacancy.update');
		Route::get('/soft/delete/{id}', [VacancyController::class, 'VacancySoftdelete'])->name('vacancy.soft.delete');
		Route::get('/restore/{id}', [VacancyController::class, 'VacancyRestore'])->name('vacancy.restore');
		Route::get('/delete/{id}', [VacancyController::class, 'VacancyDelete'])->name('vacancy.delete');
		Route::get('/inactive/{id}', [VacancyController::class, 'VacancyInactive'])->name('vacancy.inactive');
		Route::get('/active/{id}', [VacancyController::class, 'VacancyActive'])->name('vacancy.active');
		Route::get('/extend/{id}', [VacancyController::class, 'VacancyExtend'])->name('vacancy.extend');
		Route::post('/extend/update/{id}', [VacancyController::class, 'VacancyExtendUpdate'])->name('vacancy.extend.update');
		Route::get('/show/{id}', [VacancyController::class, 'VacancyShow'])->name('vacancy.show');

		Route::get('/operator/view', [VacancyController::class, 'VacancyOperatorView'])->name('vacancy.operator.view');
		Route::get('/operator/list/{id}', [VacancyController::class, 'VacancyOperatorList'])->name('vacancy.operator.list');
		Route::get('/operator/add/{id}', [VacancyController::class, 'VacancyOperatorAdd'])->name('vacancy.operator.add');
		Route::post('/operator/store', [VacancyController::class, 'VacancyOperatorStore'])->name('vacancy.operator.store');
		Route::delete('/operator/delete/{id}', [VacancyController::class, 'VacancyOperatorDelete'])->name('vacancy.operator.delete');

		Route::get('/notice/index', [VacancyController::class, 'VacancyNoticeIndex'])->name('vacancy.notice.index');
		Route::get('/notice/add', [VacancyController::class, 'VacancyNoticeAdd'])->name('vacancy.notice.add');
		Route::post('/notice/store', [VacancyController::class, 'VacancyNoticeStore'])->name('vacancy.notice.store');

		Route::get('/notice/edit/{id}', [VacancyController::class, 'VacancyNoticeEdit'])->name('vacancy.notice.edit');
		Route::post('/notice/update/{id}', [VacancyController::class, 'VacancyNoticeUpdate'])->name('vacancy.notice.update');
		Route::get('/notice/delete/{id}', [VacancyController::class, 'VacancyNoticeDelete'])->name('vacancy.notice.delete');

		Route::get('/application/view', [AcademicVancnyController::class, 'VacancyApplicationView'])->name('vacancy.application.view');
		Route::get('/application/complete/list/{id}', [AcademicVancnyController::class, 'VacancyApplicationCompleteList'])->name('vacancy.application.complete.list');
		Route::get('/application/incomplete/list/{id}', [AcademicVancnyController::class, 'VacancyApplicationIncompleteList'])->name('vacancy.application.incomplete.list');
		Route::post('/application/show', [AcademicVancnyController::class, 'VacancyApplicationShow'])->name('vacancy.application.show');
		Route::post('/application/report/download', [AcademicVancnyController::class, 'applicationReportDownload'])->name('application.report.download');
		Route::get('/application/filter', [AcademicVancnyController::class, 'VacancyApplicationFilter'])->name('vacancy.application.filter');
		Route::get('/application/research/summary/view', [AcademicVancnyController::class, 'VacancyApplicationResearchSummaryView'])->name('vacancy.application.research.summary.view');
		Route::get('/research/summary/edit/{id}', [AcademicVancnyController::class, 'VacancyResearchSummaryEdit'])->name('vacancy.research.summary.edit');
		Route::post('/research/summary/update/{id}', [AcademicVancnyController::class, 'VacancyResearchSummaryUpdate'])->name('vacancy.research.summary.update');

		Route::get('/application/check/first/view', [AcademicVancnyController::class, 'VacancyApplicationFirstCheckView'])->name('vacancy.application.first.check.view');
		Route::get('/application/check/first/list/{id}', [AcademicVancnyController::class, 'VacancyApplicationFirstCheckList'])->name('vacancy.application.first.check.list');
		Route::post('/application/check/view', [AcademicVancnyController::class, 'VacancyApplicationCheckView'])->name('vacancy.application.check.view');
		Route::get('/application/check/view/show', [AcademicVancnyController::class, 'VacancyApplicationCheckViewShow'])->name('vacancy.application.check.view.show');
		Route::post('/application/submit/ma', [AcademicVancnyController::class, 'VacancyApplicationSubmitMA'])->name('vacancy.application.submit.ma');
		Route::post('/application/submit/ma/complete', [AcademicVancnyController::class, 'VacancyApplicationSubmitMAComplete'])->name('vacancy.application.submit.ma.complete');

		Route::get('/application/admin/officer/view', [AcademicVancnyController::class, 'VacancyApplicationAdminOfficerView'])->name('vacancy.application.admin.officer.view');
		Route::post('/application/admin/officer/complete', [AcademicVancnyController::class, 'VacancyApplicationAdminOfficerComplete'])->name('vacancy.application.admin.officer.complete');

		Route::get('/admin/officer/view', [AcademicVancnyController::class, 'VacancyAdminOfficerView'])->name('vacancy.admin.officer.view');
		Route::get('/admin/officer/edit/view/{id}', [AcademicVancnyController::class, 'VacancyAdminOfficerEditView'])->name('vacancy.admin.officer.edit.view');
		Route::post('/admin/officer/update/{id}', [AcademicVancnyController::class, 'VacancyAdminOfficerUpdate'])->name('vacancy.admin.officer.update');

		Route::get('/application/dept/head/view', [UsjnetController::class, 'VacancyApplicationDeptHeadView'])->name('vacancy.application.dept.head.view');
		Route::get('/application/dept/head/list/{id}', [AcademicVancnyController::class, 'VacancyApplicationDeptHeadList'])->name('vacancy.application.dept.head.list');
		Route::post('/application/dept/check/view', [AcademicVancnyController::class, 'VacancyApplicationDeptHeadCheckView'])->name('vacancy.application.check.dept.head.view');
		Route::get('/application/dept/check/view/show', [AcademicVancnyController::class, 'VacancyApplicationDeptHeadCheckViewShow'])->name('vacancy.application.dept.head.check.view.show');
		Route::post('/application/submit/dept/head', [AcademicVancnyController::class, 'VacancyApplicationSubmitDeptHead'])->name('vacancy.application.submit.dept.head');
		Route::post('/application/submit/dept/head/complete', [AcademicVancnyController::class, 'VacancyApplicationSubmitDeptHeadComplete'])->name('vacancy.application.submit.dept.head.complete');
		Route::post('/application/submit/dept/final/report', [AcademicVancnyController::class, 'VacancyApplicationSubmitFinalReport'])->name('vacancy.application.dept.final.report');
		Route::get('/application/dept/head/final/print/{id}', [AcademicVancnyController::class, 'VacancyApplicationDeptHeadFinalPrint'])->name('vacancy.application.dept.head.final.print');
		Route::get('/application/dept/head/final/reset/{id}', [AcademicVancnyController::class, 'VacancyApplicationDeptHeadFinalReset'])->name('vacancy.application.dept.head.final.reset');

		Route::get('/interview/pending/list', [AcademicVancnyController::class, 'VacancyInterviewPendingList'])->name('vacancy.interview.pending.list');
		Route::get('/interview/pending/application/list/{id}', [AcademicVancnyController::class, 'VacancyInterviewPendingApplicationList'])->name('vacancy.interview.pending.application.list');

		Route::get('/interview/assign/list', [AcademicVancnyController::class, 'VacancyInterviewAssignList'])->name('vacancy.interview.assign.list');
		Route::get('/interview/assign/application/list/{id}', [AcademicVancnyController::class, 'VacancyInterviewAssignApplicationList'])->name('vacancy.interview.assign.application.list');
		Route::post('/interview/schedule/download/individual', [AcademicVancnyController::class, 'VacancyInterviewScheduleDownloadIndividual'])->name('vacancy.interview.schedule.download.individual');
		Route::post('/interview/schedule/download/individual/webview', [AcademicVancnyController::class, 'VacancyInterviewScheduleDownloadIndividualWebview'])->name('vacancy.interview.schedule.download.individual.webview');
		Route::post('/interview/schedule/individual/webview', [AcademicVancnyController::class, 'VacancyInterviewScheduleIndividualWebview'])->name('vacancy.interview.schedule.individual.webview');

		Route::post('/interview/panel/member/letter', [AcademicVancnyController::class, 'VacancyInterviewPanelMemberLetter'])->name('vacancy.interview.panel.member.letter');
		Route::post('/applicant/interview/calling/email', [AcademicVancnyController::class, 'ApplicantInterviewCallingEmail'])->name('applicant.interview.calling.email');
		Route::post('/applicant/interview/calling/letter', [AcademicVancnyController::class, 'ApplicantInterviewCallingLetter'])->name('applicant.interview.calling.letter');
		//Route::get('/applicant/interview/calling/email/reset/{id}', [AcademicVancnyController::class, 'ApplicantInterviewCallingEmailReset'])->name('applicant.interview.calling.email.reset');

		Route::get('/interview/complete/list', [AcademicVancnyController::class, 'VacancyInterviewCompleteList'])->name('vacancy.interview.complete.list');
		Route::get('/interview/complete/application/list/{id}', [AcademicVancnyController::class, 'VacancyInterviewCompleteApplicationList'])->name('vacancy.interview.complete.application.list');
		Route::post('/application/complete/check/view', [AcademicVancnyController::class, 'VacancyApplicationCompleteCheckView'])->name('vacancy.application.complete.check.view');
		Route::get('/application/complete/check/view/show', [AcademicVancnyController::class, 'VacancyApplicationCompleteCheckViewShow'])->name('vacancy.application.complete.check.view.show');
		Route::post('/application/submit/result', [AcademicVancnyController::class, 'VacancyApplicationSubmitResult'])->name('vacancy.application.submit.result');
		Route::post('/application/submit/result/finalize', [AcademicVancnyController::class, 'VacancyApplicationSubmitResultFinalize'])->name('vacancy.application.submit.result.finalize');

		Route::get('/interview/completed/list', [AcademicVancnyController::class, 'VacancyInterviewCompletedList'])->name('vacancy.interview.completed.list');
		Route::match(['get', 'post'], '/interview/completed/application/list', [AcademicVancnyController::class, 'VacancyInterviewCompletedApplicationList'])->name('vacancy.interview.completed.application.list');
		Route::post('/applicant/duty/assume', [AcademicVancnyController::class, 'ApplicantDutyAssume'])->name('applicant.duty.assume');
		Route::get('/application/duty/assume/view/show', [AcademicVancnyController::class, 'ApplicantDutyAssumeShow'])->name('applicant.duty.assume.show');
		Route::post('/application/duty/assume/ma/sumbit', [AcademicVancnyController::class, 'VacancyDutyAssumeMaSubmit'])->name('applicant.duty.assume.ma.submit');

		Route::post('/applicant/convert/employee', [AcademicVancnyController::class, 'ApplicantConvertEmployee'])->name('applicant.convert.employee');
		Route::get('/application/convert/employee/show', [AcademicVancnyController::class, 'ApplicantConvertEmployeeShow'])->name('applicant.convert.employee.show');
		Route::post('/application/convert/employee/sumbit', [AcademicVancnyController::class, 'ApplicantConvertEmployeeSubmit'])->name('applicant.convert.employee.submit');
		Route::post('/created/employee/update', [AcademicVancnyController::class, 'CreatedEmployeeUpdate'])->name('created.employee.update');

		Route::match(['get', 'post'], 'vacancy/finalization', [AcademicVancnyController::class, 'VacancyFinalization'])->name('vacancy.finalization');
		Route::post('vacancy/finalization/type1', [AcademicVancnyController::class, 'VacancyFinalizationType1'])->name('vacancy.finalization.type1');
		Route::post('vacancy/finalization/type2', [AcademicVancnyController::class, 'VacancyFinalizationType2'])->name('vacancy.finalization.type2');
		Route::post('vacancy/finalization/type3', [AcademicVancnyController::class, 'VacancyFinalizationType3'])->name('vacancy.finalization.type3');
		Route::post('vacancy/finalization/type4', [AcademicVancnyController::class, 'VacancyFinalizationType4'])->name('vacancy.finalization.type4');
		Route::post('vacancy/finalization/type5', [AcademicVancnyController::class, 'VacancyFinalizationType5'])->name('vacancy.finalization.type5');
		Route::post('vacancy/finalization/type6', [AcademicVancnyController::class, 'VacancyFinalizationType6'])->name('vacancy.finalization.type6');

		//Application Form

		Route::get('applications/view', [ApplicationFormFillController::class, 'applicationsView'])->name('applications.view');
		//Route::get('application/form/edit/{id}', [ApplicationFormFillController::class, 'ViewHigherQulification'])->name('application.form.view');
		//Route::match(['get','post'],'application/form/view/{id}', [ApplicationFormFillController::class, 'ViewHigherQulification'])->name('application.form.higher.qualification');
	});

    Route::middleware('role:super-admin|administrator|est-head|cc|sc')->prefix('non/academic/vacancy')->group(function () {

        //Non Academic Vacancy
        Route::get('/view', [NonAcademicVacancyController::class, 'VacancyIndex'])->name('nonacademic.vacancy.index');
		Route::get('/add', [NonAcademicVacancyController::class, 'VacancyAdd'])->name('nonacademic.vacancy.add');
		Route::post('/store', [NonAcademicVacancyController::class, 'VacancyStore'])->name('nonacademic.vacancy.store');
		Route::get('/edit/{id}', [NonAcademicVacancyController::class, 'VacancyEdit'])->name('nonacademic.vacancy.edit');
		Route::post('/update/{id}', [NonAcademicVacancyController::class, 'VacancyUpdate'])->name('nonacademic.vacancy.update');
		Route::get('/delete/{id}', [NonAcademicVacancyController::class, 'VacancyDelete'])->name('nonacademic.vacancy.delete');
		Route::get('/inactive/{id}', [NonAcademicVacancyController::class, 'VacancyInactive'])->name('nonacademic.vacancy.inactive');
		Route::get('/active/{id}', [NonAcademicVacancyController::class, 'VacancyActive'])->name('nonacademic.vacancy.active');
		Route::get('/extend/{id}', [NonAcademicVacancyController::class, 'VacancyExtend'])->name('nonacademic.vacancy.extend');
		Route::post('/extend/update/{id}', [NonAcademicVacancyController::class, 'VacancyExtendUpdate'])->name('nonacademic.vacancy.extend.update');
		Route::get('/show/{id}', [NonAcademicVacancyController::class, 'VacancyShow'])->name('nonacademic.vacancy.show');

        Route::post('/application/show', [NonAcademicVacancyController::class, 'VacancyApplicationShow'])->name('nonacademic.vacancy.application.show');
		Route::post('/application/report/download', [NonAcademicVacancyController::class, 'applicationReportDownload'])->name('nonacademic.application.report.download');

        //Non Academic Vacancy Operator
        Route::get('/operator/view', [NonAcademicVacancyController::class, 'VacancyOperatorView'])->name('nonacademic.vacancy.operator.view');
		Route::get('/operator/list/{id}', [NonAcademicVacancyController::class, 'VacancyOperatorList'])->name('nonacademic.vacancy.operator.list');
		Route::get('/operator/add/{id}', [NonAcademicVacancyController::class, 'VacancyOperatorAdd'])->name('nonacademic.vacancy.operator.add');
		Route::post('/operator/store', [NonAcademicVacancyController::class, 'VacancyOperatorStore'])->name('nonacademic.vacancy.operator.store');
        Route::get('/operator/delete/{id}', [NonAcademicVacancyController::class, 'VacancyOperatorDelete'])->name('nonacademic.vacancy.operator.delete');
		Route::get('/operator/delete/new/{id}', [NonAcademicVacancyController::class, 'VacancyOperatorNewDelete'])->name('nonacademic.vacancy.operator.new.delete');

		//remove multiple
		Route::get('/remove/multiple/page1/open',[ApplicationFormFillController::class,'removeMulti_page1_open'])->name('nac.vacancy.remove.multiple.page1.open');
		Route::get('/remove/multiple/page2/open/{id}',[ApplicationFormFillController::class,'removeMulti_page2_open'])->name('nac.vacancy.remove.multiple.page2.open');
		Route::get('/remove/multiple/page3/open/{id}',[ApplicationFormFillController::class,'removeMulti_page3_open'])->name('nac.vacancy.remove.multiple.page3.open');
		Route::get('/remove/multiple/protect/{id}',[ApplicationFormFillController::class,'protect_application'])->name('nac.vacancy.remove.multiple.protect');
		Route::get('/remove/multiple/remove/{id}',[ApplicationFormFillController::class,'remove_application'])->name('nac.vacancy.remove.multiple.remove');

        Route::get('/age/calculation/modification',[ApplicationFormFillController::class,'ageCalculationModification'])->name('age.calculation.modification');

        Route::get('/shortlist/page1/open',[nacRecruitmentProcessController::class,'shortList_page1_open'])->name('nac.recruitment.process.shortList.page1.open');
		Route::get('/shortlist/page2/open/{id}',[nacRecruitmentProcessController::class,'shortList_page2_open'])->name('nac.recruitment.process.shortList.page2.open');
		Route::get('/shortlist/page3/open/{id}',[nacRecruitmentProcessController::class,'shortList_page3_open'])->name('nac.recruitment.process.shortList.page3.open');
		Route::get('/shortlist/next/page/open/{id}',[nacRecruitmentProcessController::class,'shortList_next_open'])->name('nac.recruitment.process.shortList.nextPage.open');
		Route::get('/shortlist/pervious/page/open/{id}',[nacRecruitmentProcessController::class,'shortList_pervious_open'])->name('nac.recruitment.process.shortList.pervious.open');
		Route::post('/shortlist/store',[nacRecruitmentProcessController::class,'shortList_store'])->name('nac.recruitment.process.shortList.store');
		Route::get('/shortlist/back/to/list/{id}',[nacRecruitmentProcessController::class,'shortList_backToList'])->name('nac.recruitment.process.backTo.list.open');
		Route::post('/shortlist/complete',[nacRecruitmentProcessController::class,'shortListComplete'])->name('nac.recruitment.process.shortList.complete');

        Route::get('/interview/pending/list', [nacRecruitmentProcessController::class, 'VacancyInterviewPendingList'])->name('nac.vacancy.interview.pending.list');
		Route::get('/interview/pending/application/list/{id}', [nacRecruitmentProcessController::class, 'VacancyInterviewPendingApplicationList'])->name('nac.vacancy.interview.pending.application.list');

        Route::get('/writing/exam/result/pending/list', [nacRecruitmentProcessController::class, 'WritingExamResultPendingList'])->name('nac.vacancy.writing.exam.result.pending.list');
		Route::get('/writing/exam/result/entry/list/{id}', [nacRecruitmentProcessController::class, 'WritingExamResultEntryList'])->name('nac.vacancy.writing.exam.result.entry.list');
		Route::get('/writing/exam/result/check/view/show/{id}', [nacRecruitmentProcessController::class, 'WritingExamResultCheckViewShow'])->name('nac.vacancy.writing.exam.result.check.view.show');
		Route::post('/writing/exam/result/store', [nacRecruitmentProcessController::class, 'WritingExamResultStore'])->name('nac.vacancy.writing.exam.result.store');
		Route::post('/writing/exam/result/finalize', [nacRecruitmentProcessController::class, 'WritingExamResultFinalize'])->name('nac.vacancy.writing.exam.result.finalize');
        Route::get('/writing/exam/notification/send/{id}', [nacRecruitmentProcessController::class, 'WritingExamNotificationSend'])->name('nac.writing.exam.notification.send');
        Route::get('/writing/exam/notification/email/send/{id}', [nacRecruitmentProcessController::class, 'WritingExamEmailNotificationSend'])->name('nac.writing.exam.email.notification.send');

        Route::get('/practical/exam/result/pending/list', [nacRecruitmentProcessController::class, 'PracticalExamResultPendingList'])->name('nac.vacancy.practical.exam.result.pending.list');
		Route::get('/practical/exam/result/entry/list/{id}', [nacRecruitmentProcessController::class, 'PracticalExamResultEntryList'])->name('nac.vacancy.practical.exam.result.entry.list');
		Route::get('/practical/exam/result/check/view/show/{id}', [nacRecruitmentProcessController::class, 'PracticalExamResultCheckViewShow'])->name('nac.vacancy.practical.exam.result.check.view.show');
		Route::post('/practical/exam/result/store', [nacRecruitmentProcessController::class, 'PracticalExamResultStore'])->name('nac.vacancy.practical.exam.result.store');
		Route::post('/practical/exam/result/finalize', [nacRecruitmentProcessController::class, 'PracticalExamResultFinalize'])->name('nac.vacancy.practical.exam.result.finalize');

        Route::get('/interview/assign/list', [nacRecruitmentProcessController::class, 'VacancyInterviewAssignList'])->name('nac.vacancy.interview.assign.list');
		Route::get('/interview/assign/application/list/{id}', [nacRecruitmentProcessController::class, 'VacancyInterviewAssignApplicationList'])->name('nac.vacancy.interview.assign.application.list');
        Route::post('/interview/schedule/download/individual/webview', [nacRecruitmentProcessController::class, 'VacancyInterviewScheduleDownloadIndividualWebview'])->name('nac.vacancy.interview.schedule.download.individual.webview');
		Route::post('/interview/schedule/individual/webview', [nacRecruitmentProcessController::class, 'VacancyInterviewScheduleIndividualWebview'])->name('nac.vacancy.interview.schedule.individual.webview');
        Route::post('/interview/schedule/individual/bulk/webview', [nacRecruitmentProcessController::class, 'VacancyInterviewScheduleBulkWebview'])->name('nac.vacancy.interview.schedule.individual.bulk.webview');

        Route::get('/interview/result/pending/list', [nacRecruitmentProcessController::class, 'InterviewResultPendingList'])->name('nac.vacancy.interview.result.pending.list');
		Route::get('/interview/result/entry/list/{id}', [nacRecruitmentProcessController::class, 'InterviewResultEntryList'])->name('nac.vacancy.interview.result.entry.list');
		Route::get('/interview/result/check/view/show/{id}', [nacRecruitmentProcessController::class, 'InterviewResultCheckViewShow'])->name('nac.vacancy.interview.result.check.view.show');
		Route::post('/interview/result/store', [nacRecruitmentProcessController::class, 'InterviewResultStore'])->name('nac.vacancy.interview.result.store');
		Route::post('/interview/result/finalize', [nacRecruitmentProcessController::class, 'InterviewResultFinalize'])->name('nac.vacancy.interview.result.finalize');

        Route::get('/interview/completed/list', [nacRecruitmentProcessController::class, 'VacancyInterviewCompletedList'])->name('nac.vacancy.interview.completed.list');
		Route::get('/interview/completed/application/list/{id}', [nacRecruitmentProcessController::class, 'VacancyInterviewCompletedApplicationList'])->name('nac.vacancy.interview.completed.application.list');
		Route::get('/applicant/duty/assume/view/show/{id}', [nacRecruitmentProcessController::class, 'ApplicantDutyAssumeViewShow'])->name('nac.applicant.duty.assume');
		Route::post('/applicant/duty/assume/store', [nacRecruitmentProcessController::class, 'ApplicantDutyAssumeStore'])->name('nac.applicant.duty.assume.store');
        Route::get('/application/convert/employee/show/{id}', [nacRecruitmentProcessController::class, 'ApplicantConvertEmployeeViewShow'])->name('nac.applicant.convert.employee');
		Route::post('/application/convert/employee/store', [nacRecruitmentProcessController::class, 'ApplicantConvertEmployeeStore'])->name('nac.applicant.convert.employee.store');
        Route::post('/vacancy/finalize', [nacRecruitmentProcessController::class, 'VacancyFinalize'])->name('nac.vacancy.finalize');

    });

	//Employee Related Route List
	Route::middleware('role:super-admin|administrator|est-head|cc|sc|email-admin|stat')->prefix('employee')->group(function () {

		Route::get('/active/list', [EmployeeController::class, 'activeList'])->name('employee.index');
		Route::get('/inactive/list', [EmployeeController::class, 'inactiveList'])->name('employee.inactive.list');
		Route::get('/check/index', [EmployeeController::class, 'checkView'])->name('employee.check.view');
		Route::post('/check', [EmployeeController::class, 'check'])->name('employee.check');
		Route::get('/create', [EmployeeController::class, 'add'])->name('employee.add');
		Route::get('/create/temp', [EmployeeController::class, 'tempAdd'])->name('employee.temp.add');
		Route::get('/create/cont', [EmployeeController::class, 'ContAdd'])->name('employee.cont.add');
		Route::post('/store/temp', [EmployeeController::class, 'tempStore'])->name('employee.temp.store');
		Route::post('/store/cont', [EmployeeController::class, 'contStore'])->name('employee.cont.store');
		Route::post('/store', [EmployeeController::class, 'store'])->name('employee.store');
		Route::get('/show/{id}', [EmployeeController::class, 'show'])->name('employee.show');
		Route::get('/show/{employee_no}/{notification_id}', [EmployeeController::class, 'showNotification'])->name('employee.show.notification');
		Route::get('/edit/{id}', [EmployeeController::class, 'edit'])->name('employee.edit');
		Route::get('/edit/temp/{id}', [EmployeeController::class, 'tempEdit'])->name('employee.temp.edit');
		Route::get('/edit/cont/{id}', [EmployeeController::class, 'contEdit'])->name('employee.cont.edit');
		Route::get('/edit/assign/basis/{id}', [EmployeeController::class, 'assignBasisEdit'])->name('employee.assign.basis.edit');
		Route::post('/update/{id}', [EmployeeController::class, 'update'])->name('employee.update');
		Route::post('/update/temp/{id}', [EmployeeController::class, 'tempUpdate'])->name('employee.temp.update');
		Route::post('/update/cont/{id}', [EmployeeController::class, 'contUpdate'])->name('employee.cont.update');
		Route::post('/update/assign/basis/{id}', [EmployeeController::class, 'assignBasisUpdate'])->name('employee.assign.basis.update');
		Route::get('/softdelete/{id}', [EmployeeController::class, 'softDelete'])->name('employee.soft.delete');
		Route::get('/restore/{id}', [EmployeeController::class, 'restore'])->name('employee.restore');
		Route::get('/delete/{id}', [EmployeeController::class, 'delete'])->name('employee.delete');
		Route::get('/lock/{id}', [EmployeeController::class, 'lock'])->name('employee.lock');
		Route::get('/unlock/{id}', [EmployeeController::class, 'unlock'])->name('employee.unlock');

		Route::get('/email/create', [EmployeeController::class, 'emailCreate'])->name('email.create');
		Route::post('/email/store', [EmployeeController::class, 'emailStore'])->name('email.store');
		Route::get('/address/view', [EmployeeController::class, 'addressView'])->name('address.view');

		Route::get('/old/bulk/upload', [EmployeeController::class, 'bulkDataUploadView'])->name('employee.bulk.index');
		Route::match(['get', 'post'], '/import', [EmployeeController::class, 'import'])->name('employee.bulk.import');

		Route::get('/lock/list/view', [EmployeeController::class, 'lockLiisView'])->name('lock.list.view');
		Route::get('/lock/history/show/{id}', [EmployeeController::class, 'lockHistoryShow'])->name('lock.history.show');

		/*********************************************************************************************** */
		Route::get('/new/check/index', [NewEmployeeController::class, 'newCheckView'])->name('new.employee.check.view');
		Route::post('/new/check', [NewEmployeeController::class, 'newCheck'])->name('new.employee.check');
		Route::get('/new/create', [NewEmployeeController::class, 'newAdd'])->name('new.employee.add');
		Route::get('/new/create/{employee_no}/{notification_id}', [NewEmployeeController::class, 'newAddNotification'])->name('new.employee.add.notification');
		Route::post('/new/store', [NewEmployeeController::class, 'newStore'])->name('new.employee.stored');

		Route::get('/employee/search', [EmployeeController::class, 'employeeSearch'])->name('employee.search');
		Route::post('/employee/search/result', [EmployeeController::class, 'employeeSearchResult'])->name('employee.search.results');
		Route::get('/employee/suggestions', [EmployeeController::class, 'employeeSuggestion'])->name('employee.suggestions');
		/************************************************************************************************ */

		Route::get('/filter/view', [EmployeeController::class, 'filterView'])->name('filter.view');
		Route::match(['get', 'post'], '/operator/filter/view', [EmployeeController::class, 'opratorFilterView'])->name('operator.filter.view');
		Route::get('/operator/search', [EmployeeController::class, 'operatorSearch'])->name('operator.search');

		Route::match(['get', 'post'], '/designation/filter/view', [EmployeeController::class, 'designationFilterView'])->name('designation.filter.view');
		Route::get('/designation/search', [EmployeeController::class, 'designationSearch'])->name('designation.search');

		Route::match(['get', 'post'], '/designation/main/group/filter/view', [EmployeeController::class, 'designationMainGroupFilterView'])->name('designation.main.group.filter.view');
		Route::get('/designation/main/group/search', [EmployeeController::class, 'designationMainGroupSearch'])->name('designation.main.group.search');

		Route::match(['get', 'post'], '/designation/sub/group/filter/view', [EmployeeController::class, 'designationSubGroupFilterView'])->name('designation.sub.group.filter.view');
		Route::get('/designation/sub/group/search', [EmployeeController::class, 'designationSubGroupSearch'])->name('designation.sub.group.search');

		Route::match(['get', 'post'], '/faculty/filter/view', [EmployeeController::class, 'facultyFilterView'])->name('faculty.filter.view');
		Route::get('/faculty/search', [EmployeeController::class, 'facultySearch'])->name('faculty.search');

		Route::match(['get', 'post'], '/department/filter/view', [EmployeeController::class, 'departmentFilterView'])->name('department.filter.view');
		Route::get('/department/search', [EmployeeController::class, 'departmentSearch'])->name('department.search');

		Route::match(['get', 'post'], '/main/branch/filter/view', [EmployeeController::class, 'mainBranchFilterView'])->name('main.branch.filter.view');
		Route::get('/main/branch/search', [EmployeeController::class, 'mainBranchSearch'])->name('main.branch.search');

		Route::match(['get', 'post'], '/main/group/filter/view', [EmployeeController::class, 'mainGroupFilterView'])->name('main.group.filter.view');
		Route::get('/main/group/search', [EmployeeController::class, 'mainGroupSearch'])->name('main.group.search');

		Route::match(['get', 'post'], '/status/filter/view', [EmployeeController::class, 'statusFilterView'])->name('status.filter.view');
		Route::get('/status/search', [EmployeeController::class, 'statusSearch'])->name('status.search');

		Route::match(['get', 'post'], '/working/type/filter/view', [EmployeeController::class, 'workingTypeFilterView'])->name('working.type.filter.view');
		Route::get('/working/type/search', [EmployeeController::class, 'workingTypeSearch'])->name('working.type.search');

		Route::match(['get', 'post'], '/system/user/filter/view', [EmployeeController::class, 'systemUserFilterView'])->name('system.user.filter.view');
		Route::get('/system/user/search', [EmployeeController::class, 'systemUserSearch'])->name('system.user.search');

		Route::match(['get', 'post'], '/education/filter/view', [EmployeeController::class, 'educationFilterView'])->name('education.filter.view');
		Route::get('/education/search', [EmployeeController::class, 'educationSearch'])->name('education.search');

		Route::match(['get', 'post'], '/gender/filter/view', [EmployeeController::class, 'genderFilterView'])->name('gender.filter.view');
		Route::get('/gender/search', [EmployeeController::class, 'genderSearch'])->name('gender.search');

		Route::match(['get', 'post'], '/race/filter/view', [EmployeeController::class, 'raceFilterView'])->name('race.filter.view');
		Route::get('/race/search', [EmployeeController::class, 'raceSearch'])->name('race.search');

		Route::match(['get', 'post'], '/plocation/filter/view', [EmployeeController::class, 'plocationFilterView'])->name('plocation.filter.view');
		Route::get('/plocation/search', [EmployeeController::class, 'plocationSearch'])->name('plocation.search');

		Route::match(['get', 'post'], '/clocation/filter/view', [EmployeeController::class, 'clocationFilterView'])->name('clocation.filter.view');
		Route::get('/clocation/search', [EmployeeController::class, 'clocationSearch'])->name('clocation.search');

        Route::match(['get', 'post'], '/type/filter/view', [EmployeeController::class, 'typeFilterView'])->name('type.filter.view');
		Route::get('/type/search', [EmployeeController::class, 'typeSearch'])->name('type.search');

		Route::get('/custom/filter/view', [EmployeeController::class, 'customFilterView'])->name('custom.filter.view');

		Route::get('/common/report/1', [EmployeeController::class, 'commonReport1'])->name('common.report.1');
		Route::get('/custom/report/1', [EmployeeController::class, 'customReport1'])->name('custom.report.1');
		Route::get('/custom/report/2', [EmployeeController::class, 'customReport2'])->name('custom.report.2');
		Route::get('/custom/report/3', [EmployeeController::class, 'customReport3'])->name('custom.report.3');
		Route::get('/custom/report/4', [EmployeeController::class, 'customReport4'])->name('custom.report.4');
		Route::get('/custom/report/5', [EmployeeController::class, 'customReport5'])->name('custom.report.5');
		Route::get('/custom/report/6', [EmployeeController::class, 'customReport6'])->name('custom.report.6');
		Route::get('/custom/report/7', [EmployeeController::class, 'customReport7'])->name('custom.report.7');
		Route::get('/custom/report/8', [EmployeeController::class, 'customReport8'])->name('custom.report.8');
		Route::get('/custom/report/9', [EmployeeController::class, 'customReport9'])->name('custom.report.9');
		Route::get('/custom/report/10', [EmployeeController::class, 'customReport10'])->name('custom.report.10');
		Route::get('/custom/report/11', [EmployeeController::class, 'customReport11'])->name('custom.report.11');
        Route::get('/custom/report/12', [EmployeeController::class, 'customReport12'])->name('custom.report.12');
        Route::get('/custom/report/13', [EmployeeController::class, 'customReport13'])->name('custom.report.13');
        Route::get('/custom/report/14', [EmployeeController::class, 'customReport14'])->name('custom.report.14');
        Route::get('/custom/report/15', [EmployeeController::class, 'customReport15'])->name('custom.report.15');
        Route::get('/custom/report/16', [EmployeeController::class, 'customReport16'])->name('custom.report.16');
        Route::get('/custom/report/17', [EmployeeController::class, 'customReport17'])->name('custom.report.17');

		Route::get('/updation/{id}', [SettingController::class, 'employeeSetting'])->name('employee.updation');

		Route::match(['get', 'post'], '/file/assgin/change/view/{employee_no}', [SettingController::class, 'fileAssignChangeView'])->name('file.assign.change.view');
		Route::post('/new/operator/store', [SettingController::class, 'newOperatorStore'])->name('new.operator.store');

		Route::get('/main/branch/change/view/{employee_no}', [SettingController::class, 'mainBranchChangeView'])->name('main.branch.change.view');
		Route::post('/new/branch/store', [SettingController::class, 'newBranchStore'])->name('new.branch.store');

		Route::get('/new/record/create', [SettingController::class, 'newEmployeeCreate'])->name('new.employee.create');
		Route::post('/new/record/store', [SettingController::class, 'newEmployeeStore'])->name('new.employee.store');
		Route::get('/new/final', [SettingController::class, 'newEmployeeFinal'])->name('new.employee.final');

		Route::match(['get', 'post'], '/new/list', [SettingController::class, 'newEmployeeList'])->name('new.employee.list');
		Route::get('/new/search', [SettingController::class, 'newEmployeeSearch'])->name('new.employee.search');

		Route::get('/new/record/edit/{id}', [SettingController::class, 'newEmployeeEdit'])->name('new.employee.edit');
		Route::post('/new/record/update/{id}', [SettingController::class, 'newEmployeeUpdate'])->name('new.employee.update');
		Route::get('/new/record/delete/{id}', [SettingController::class, 'newEmployeeDelete'])->name('new.employee.delete');

        Route::get('/change/list', [EmployeeUpadateController::class, 'employeeChange'])->name('employee.change');
        Route::get('/change/approval/{id}', [EmployeeUpadateController::class, 'employeeChangeApproval'])->name('employee.change.approval');
		Route::get('/change/reject/{id}', [EmployeeUpadateController::class, 'employeeChangeReject'])->name('employee.change.reject');

		Route::get('/nic/update/view/{employee_no}', [EmployeeUpadateController::class, 'nicUpdateView'])->name('nic.update.view');
		Route::post('/nic/update/store', [EmployeeUpadateController::class, 'nicUpdateStore'])->name('nic.update.store');

		Route::get('/name/update/view/{employee_no}', [EmployeeUpadateController::class, 'nameUpdateView'])->name('name.update.view');
		Route::post('/name/update/store', [EmployeeUpadateController::class, 'nameUpdateStore'])->name('name.update.store');

		Route::get('/mobile/update/view/{employee_no}', [EmployeeUpadateController::class, 'mobileUpdateView'])->name('mobile.update.view');
		Route::post('/mobile/update/store', [EmployeeUpadateController::class, 'mobileUpdateStore'])->name('mobile.update.store');

		Route::get('/email/update/view/{employee_no}', [EmployeeUpadateController::class, 'emailUpdateView'])->name('email.update.view');
		Route::post('/email/update/store', [EmployeeUpadateController::class, 'emailUpdateStore'])->name('email.update.store');

		Route::get('/dob/update/view/{employee_no}', [EmployeeUpadateController::class, 'dobUpdateView'])->name('dob.update.view');
		Route::post('/dob/update/store', [EmployeeUpadateController::class, 'dobUpdateStore'])->name('dob.update.store');

		Route::get('/gender/update/view/{employee_no}', [EmployeeUpadateController::class, 'genderUpdateView'])->name('gender.update.view');
		Route::post('/gender/update/store', [EmployeeUpadateController::class, 'genderUpdateStore'])->name('gender.update.store');

		Route::get('/civil/status/update/view/{employee_no}', [EmployeeUpadateController::class, 'civilStatusUpdateView'])->name('civil.status.update.view');
		Route::post('/civil/status/update/store', [EmployeeUpadateController::class, 'civilStatusUpdateStore'])->name('civil.status.update.store');

		Route::get('/race/religion/update/view/{employee_no}', [EmployeeUpadateController::class, 'raceReligionUpdateView'])->name('race.religion.update.view');
		Route::post('/race/religion/update/store', [EmployeeUpadateController::class, 'raceReligionUpdateStore'])->name('race.religion.update.store');

		Route::get('/address/update/view/{employee_no}', [EmployeeUpadateController::class, 'addressUpdateView'])->name('address.update.view');
		Route::post('/address/update/store', [EmployeeUpadateController::class, 'addressUpdateStore'])->name('address.update.store');

        Route::get('/postal/address/update/view/{employee_no}', [EmployeeUpadateController::class, 'postalAddressUpdateView'])->name('postal.address.update.view');
		Route::post('/postal/address/update/store', [EmployeeUpadateController::class, 'postalAddressUpdateStore'])->name('postal.address.update.store');

		Route::get('/citizenship/update/view/{employee_no}', [EmployeeUpadateController::class, 'citizenshipUpdateView'])->name('citizenship.update.view');
		Route::post('/citizenship/update/store', [EmployeeUpadateController::class, 'citizenshipUpdateStore'])->name('citizenship.update.store');

        Route::get('/file/ref/number/update/view/{employee_no}', [EmployeeUpadateController::class, 'fileRefNumberUpdateView'])->name('file.ref.number.update.view');
		Route::post('/file/ref/number/update/store', [EmployeeUpadateController::class, 'fileRefNumberUpdateStore'])->name('file.ref.number.update.store');

        Route::get('/education/update/view/{employee_no}', [EmployeeUpadateController::class, 'educationUpdateView'])->name('education.update.view');
        Route::post('/education/update/store', [EmployeeUpadateController::class, 'educationUpdateStore'])->name('education.update.store');

		Route::get('/designation/update/view/{employee_no}', [EmployeeUpadateController::class, 'designationUpdateView'])->name('designation.update.view');
		Route::post('/designation/update/store', [EmployeeUpadateController::class, 'designationUpdateStore'])->name('designation.update.store');

		Route::get('/basic/salary/update/view/{employee_no}', [EmployeeUpadateController::class, 'basicSalaryUpdateView'])->name('basic.salary.update.view');
		Route::post('/basic/salary/update/store', [EmployeeUpadateController::class, 'basicSalaryUpdateStore'])->name('basic.salary.update.store');

		Route::get('/confirmation/update/view/{employee_no}', [EmployeeUpadateController::class, 'confirmationUpdateView'])->name('confirmation.update.view');
        Route::post('/confirmation/update/store', [EmployeeUpadateController::class, 'confirmationUpdateStore'])->name('confirmation.update.store');

		Route::get('/status/update/view/{employee_no}', [EmployeeUpadateController::class, 'statusUpdateView'])->name('status.update.view');
        Route::post('/status/update/store', [EmployeeUpadateController::class, 'statusUpdateStore'])->name('status.update.store');

		Route::get('/appointment/update/view/{employee_no}', [EmployeeUpadateController::class, 'appointmentUpdateView'])->name('appointment.update.view');
        Route::post('/appointment/update/store', [EmployeeUpadateController::class, 'appointmentUpdateStore'])->name('appointment.update.store');

        Route::get('/fund/number/update/view/{employee_no}', [EmployeeUpadateController::class, 'fundNumberUpdateView'])->name('fund.number.update.view');
        Route::post('/fund/number/update/store', [EmployeeUpadateController::class, 'fundNumberUpdateStore'])->name('fund.number.update.store');

        Route::get('/working/location/update/view/{employee_no}', [EmployeeUpadateController::class, 'workingLocationUpdateView'])->name('working.location.update.view');
        Route::post('/working/location/update/store', [EmployeeUpadateController::class, 'workingLocationUpdateStore'])->name('working.location.update.store');

        Route::get('/carder/location/update/view/{employee_no}', [EmployeeUpadateController::class, 'carderLocationUpdateView'])->name('carder.location.update.view');
        Route::post('/carder/location/update/store', [EmployeeUpadateController::class, 'carderLocationUpdateStore'])->name('carder.location.update.store');


		Route::get('/generate/add/list', [EmployeeCreationController::class, 'GenerateAddList'])->name('employee.generate.add.list');
		Route::get('/generate/add/view', [EmployeeCreationController::class, 'GenerateAddView'])->name('employee.generate.add.view');
		Route::post('/generate/add/submit', [EmployeeCreationController::class, 'generateAddSubmit'])->name('employee.generate.add.submit');
		Route::get('/generate/add/permanent', [EmployeeCreationController::class, 'generateAddPermanent'])->name('employee.generate.add.permanent');
		Route::get('/generate/add/tempory', [EmployeeCreationController::class, 'generateAddTempory'])->name('employee.generate.add.tempory');
		Route::get('/generate/add/contract', [EmployeeCreationController::class, 'generateAddContract'])->name('employee.generate.add.contract');
		Route::get('/generate/add/assignment/basis', [EmployeeCreationController::class, 'generateAddAssignmentBasis'])->name('employee.generate.add.assignment.basis');
		Route::post('/generate/add/store', [EmployeeCreationController::class, 'generateAddStore'])->name('employee.generate.add.store');
		Route::get('/generate/edit/permanent/{id}', [EmployeeCreationController::class, 'generateEditPermanent'])->name('employee.generate.edit.permanent');
		Route::get('/generate/edit/tempory/{id}', [EmployeeCreationController::class, 'generateEditTempory'])->name('employee.generate.edit.tempory');
		Route::get('/generate/edit/contract/{id}', [EmployeeCreationController::class, 'generateEditContract'])->name('employee.generate.edit.contract');
		Route::get('/generate/edit/assignment/basis/{id}', [EmployeeCreationController::class, 'generateEditAssignmentBasis'])->name('employee.generate.edit.assignment.basis');
		Route::post('/generate/update/{id}', [EmployeeCreationController::class, 'generateUpdate'])->name('employee.generate.update');
		Route::get('/generate/complete', [EmployeeCreationController::class, 'generateComplete'])->name('employee.generate.complete');
		Route::get('/generate/delete/{id}', [EmployeeCreationController::class, 'generateDelete'])->name('employee.generate.delete');
		Route::get('/generate/list/{notification_id}', [EmployeeCreationController::class, 'GenerateList'])->name('employee.generate.list.notification');
	});

	//Promotion Related Route List
	Route::middleware('role:super-admin|administrator|est-head|cc|sc|head')->prefix('promotion')->group(function () {

		Route::match(['get', 'post'], '/history/index', [PromotionController::class, 'oldPromo'])->name('promotion.old.add');
		Route::post('/history/submit', [PromotionController::class, 'oldPromoSubmit'])->name('promotion.old.add.submit');
		Route::get('/edit/{id}', [PromotionController::class, 'oldPromotionEdit'])->name('promotion.old.edit');
		Route::post('/update/{id}', [PromotionController::class, 'oldPromotionUpdate'])->name('promotion.old.update');
		Route::get('/delete/{id}', [PromotionController::class, 'oldPromotionDelete'])->name('promotion.old.delete');


		//promotion duration
		Route::get('/duration/add', [PromotionDurationController::class, 'index'])->name('prmotion_duration.add');
		Route::post('/duration/store', [PromotionDurationController::class, 'store'])->name('promotion_duration.store');
		Route::get('/duration/edit/{id}', [PromotionDurationController::class, 'edit'])->name('promotion.duration.edit');
		Route::post('/duration/update/{id}', [PromotionDurationController::class, 'update'])->name('promotion.duration.update');
		Route::get('/duration/delete/{id}', [PromotionDurationController::class, 'delete'])->name('promotion.duration.delete');

		//non academic application all
		Route::match(['get', 'post'], 'nac/application/all', [NonAcademicPromotionController::class, 'promotionAllOpen'])->name('nonAcademic.promotionAll.open');
		Route::get('nac/application/all/detail/{id}', [NonAcademicPromotionController::class, 'applicationDetailsOpen'])->name('nonAcademic.promotionAll.details.open');

		// nac 1stChecking
		Route::get('nac/check/first/open', [NonAcademicPromotionController::class, 'CheckingOpen'])->name('nac.promo.1stchecking.open');
		Route::get('nac/check/first/detail/{id1}', [NonAcademicPromotionController::class, 'checkingDetails'])->name('nac.promo.1stchecking.detais');
		Route::post('nac/check/first/save', [NonAcademicPromotionController::class, 'checkingSave'])->name('nac.promo.1stChecking.save');


		//non academic promotion Eligibility
		Route::get('nac/eligible/list', [NonAcademicPromotionController::class, 'PromoEligibility'])->name('nac.promo.eligible.list');
		Route::get('nac/eligible/list/detail/{id1}/{id2}', [NonAcademicPromotionController::class, 'PromoEligibilityDetails'])->name('nac.promo.eligible.list.detais');
		Route::post('nac/eligible/list/save', [NonAcademicPromotionController::class, 'promoEligibilityStore'])->name('nac.promo.eligible.list.save');
		//Route::get('nac/check/first/detail/{id}',[NonAcademicPromotionController::class,'checkingDetails'])->name('nac.promo.1stchecking.detais');

		// nac officer approval
		Route::get('nac/officer/approval/open', [NonAcademicPromotionController::class, 'officerApprovalOpen'])->name('nac.promo.officer.approval.open');
		Route::get('nac/officer/approval/detail/{id}', [NonAcademicPromotionController::class, 'officerApprovalDetails'])->name('nac.promo.officer.approval.detais');
		Route::post('nac/officer/approval/save', [NonAcademicPromotionController::class, 'officerAppStore'])->name('nac.promo.officer.approval.save');

		// nac head Recommendation
		Route::get('nac/head/approval/open', [UsjnetController::class, 'headApprovalOpen'])->name('nac.promo.head.approval.open');
		Route::get('nac/head/approval/detail/{id}', [UsjnetController::class, 'headApprovalDetails'])->name('nac.promo.head.approval.detais');
		Route::post('nac/head/approval/save', [UsjnetController::class, 'headAppStore'])->name('nac.promo.head.approval.save');



		//interview
		Route::get('nac/promotion/interview/open', [NonAcademicPromotionController::class, 'interviewPendingOpen'])->name('nac.promo.interview.pending.open');
		Route::get('nac/promotion/interview/detail/{id}', [NonAcademicPromotionController::class, 'interviewPendingDetails'])->name('nac.promo.interview.pending.detais');

		//pending All
		Route::match(['get', 'post'], 'nac/application/pending/all', [NonAcademicPromotionController::class, 'pendingAll'])->name('nonAcademic.promotion.pending.all');

		//Interview Assigned
		Route::match(['get', 'post'], 'interview/assigned/open', [NonAcademicPromotionController::class, 'interviewAssignedOpen'])->name('interview.assigned.open');
		Route::post('interview/assigned/store', [NonAcademicPromotionController::class, 'interviewAssignedStore'])->name('interview.assigned.store');
		Route::get('interview/assigned/delete/{id}', [NonAcademicPromotionController::class, 'interviewAssignedDelete'])->name('interview.assigned.delete');

		//Letters
		Route::get('nac/promotion/letters/open', [NonAcademicPromotionController::class, 'promoLetterOpen'])->name('nac.promotion.letters.open');
		Route::get('/LetterPanelMemeberApp_Search', [NonAcademicPromotionController::class, 'LetterPanelMemeberAppSearc'])->name('nac.promo.letter.Appoint.PMemebers.Search');
		Route::post('/panelMember/Appointment/Letter/Print', [NonAcademicPromotionController::class, 'panelMembAppLetterPrint'])->name('nac.PMember.Appletter.print');
		Route::get('/LetterInterviewee_Search', [NonAcademicPromotionController::class, 'LetterIntervieweeSearc'])->name('nac.promo.letter.Appoint.Interviewee.Search');
		Route::post('/interviewees/Letter/Print', [NonAcademicPromotionController::class, 'intervieweesLetterPrint'])->name('nac.interviewees.letter.print');

		//interview shedule
		Route::match(['get', 'post'], 'nac/interview/shedule/open', [NonAcademicPromotionController::class, 'int_shedule_open'])->name('nac.interview.shedule.open');
		Route::post('/nac/interview/shedule/print', [NonAcademicPromotionController::class, 'int_shedule_print'])->name('nac.interview.shedule.print');

		//interview results
		Route::match(['get', 'post'], 'interview/results/open', [NonAcademicPromotionController::class, 'interviwResultsOpen'])->name('interview.results.open');
		Route::get('nac/promotion/interview/results/detail/{id}', [NonAcademicPromotionController::class, 'interviewResultsDetails'])->name('nac.promo.interview.results.detais');
		Route::post('nac/interview/results/store', [NonAcademicPromotionController::class, 'interviewResultStore'])->name('nac.interview.results.store');

		//Exam
		Route::get('nac/promotion/exam/pending/open', [NonAcademicPromotionController::class, 'examPendingOpen'])->name('nac.promo.exam.pending.open');
		Route::get('nac/promotion/exam/pending/detail/{id}', [NonAcademicPromotionController::class, 'examPendingDetails'])->name('nac.promo.exam.pending.detais');

		//exam Assigned
		Route::match(['get', 'post'], 'exam/assigned/open', [NonAcademicPromotionController::class, 'examAssingOpen'])->name('exam.assigned.open');
		Route::post('exam/assigned/store', [NonAcademicPromotionController::class, 'examAssignedStore'])->name('exam.assigned.store');
		Route::get('exam/assigned/delete/{id}', [NonAcademicPromotionController::class, 'examAssignedDelete'])->name('exam.assigned.delete');

		//exam results
		Route::match(['get', 'post'], 'exam/results/open', [NonAcademicPromotionController::class, 'examResultsOpen'])->name('exam.results.open');
		Route::post('exam/results/store', [NonAcademicPromotionController::class, 'examResultsStore'])->name('exam.results.store');

		//duty assumed
		Route::get('nac/duty/assumed/open', [NonAcademicPromotionController::class, 'dutyAssumedOpen'])->name('nac.promo.duty.assumed.open');
		Route::get('nac/duty/assumed/detail/{id}', [NonAcademicPromotionController::class, 'dutyAssumedDetails'])->name('nac.promo.duty.assumed.detais');
		Route::post('nac/duty/assumed/store', [NonAcademicPromotionController::class, 'dutyAssumeStore'])->name('nac.promo.duty.assumed.store');

		//confirm
		Route::get('nac/confirm/open', [NonAcademicPromotionController::class, 'officerConfirmOpen'])->name('nac.promo.confirm.open');
		Route::get('nac/confirm/detail/{id}', [NonAcademicPromotionController::class, 'officerConfirmDetails'])->name('nac.promo.confirm.detais');
		Route::post('nac/confirm/store', [NonAcademicPromotionController::class, 'officerConfirmStore'])->name('nac.promo.confirm.store');

		//applicant's reportx
		Route::get('nac/applicant/report/open', [NonAcademicPromotionController::class, 'applicantReportOpen'])->name('nac.promo.applicant.report.open');
		Route::get('/nac/applicant/report/print/{id}', [NonAcademicPromotionController::class, 'applicantReportPrint'])->name('nac.promo.applicant.report.print');

		//budget list
		Route::get('nac/budget/list', [NonAcademicPromotionController::class, 'budgetList'])->name('nac.promo.budget.list');

		//MA promotion 1st checking
		Route::get('nac/ma/promotion/1stChecking/list', [NonAcademicPromotionController::class, 'maPromoCheckingOpen'])->name('nac.promo.maPromo.1stchecking.list.open');
		Route::get('nac/ma/promotion/1stChecking/details/{id}', [NonAcademicPromotionController::class, 'checkingDetailsMAPromo'])->name('nac.promo.maPromo.1stchecking.details.open');
		Route::post('nac/ma/promotion/1stChecking/store',[NonAcademicPromotionController::class,'maPromo1stCheckingStore'])->name('nac.promo.maPromo.1stChecking.details.store');
	});

	//Employeen Increments Related Route List
	Route::middleware('role:super-admin|administrator|est-head|cc|sc|lc|head|reg|dean|vc')->prefix('increment')->group(function () {

		Route::match(['get', 'post'], '/history/index', [IncrementController::class, 'index'])->name('increments.old.index');
		Route::post('/history/submit', [IncrementController::class, 'store'])->name('increment.old.add.submit');
		Route::get('/show/list', [IncrementController::class, 'oldIncrementShow'])->name('increment.show.old');

		Route::get('/edit/{id}', [IncrementController::class, 'oldIncrementEdit'])->name('increment.edit.old');
		Route::post('/update/{id}', [IncrementController::class, 'oldIncrementUpdate'])->name('increment.update.old');
		Route::get('/delete/{id}', [IncrementController::class, 'oldIncrementDelete'])->name('increment.delete.old');

		Route::match(['get', 'post'], '/month/list', [IncrementController::class, 'monthListIncrement'])->name('increment.month.list');
		Route::get('/month/list/search', [IncrementController::class, 'monthListIncrementSearch'])->name('increment.month.list.search');


		//Non Acadamic routes
		Route::get('/nonAc/month/list', [NonAcademicController::class, 'index'])->name('nonAc.month.list');
		Route::get('/nonAc/reverse/list', [NonAcademicController::class, 'reverseList'])->name('nonAc.reverse.list');
		Route::get('/nonAc/month/MaLeaveToArList', [NonAcademicController::class, 'MaLeaveToArList'])->name('nonAc.month.MaLeaveToArList');
		Route::get('/nonAc/month/HodToArList', [NonAcademicController::class, 'HodToArList'])->name('nonAc.month.HodToArList');

		Route::get('/nonAc/operator/{id}', [NonAcademicController::class, 'MAView'])->name('nonAc_operator');
		Route::get('/nonAc/leaveClerk/{id}', [NonAcademicController::class, 'leaveClerk'])->name('nonAc_leaveClerk');
		Route::get('/nonAc/arToHead/{id}', [NonAcademicController::class, 'arToHead'])->name('nonAc_arToHead');
		Route::get('/nonAc/head/{id}', [NonAcademicController::class, 'head'])->name('nonAc_head');
		Route::get('/nonAc/arToRegistar/{id}', [NonAcademicController::class, 'arToRegistar'])->name('nonAc_arToRegistar');
		Route::get('/nonAc/registarToAr/{id}', [NonAcademicController::class, 'registarToAr'])->name('nonAc_registarToAr');

		Route::post('/nonAc/operator/submit', [NonAcademicController::class, 'submitOperator'])->name('nonAc_operator_submit');
		Route::post('/nonAc/leaveClerk/submit', [NonAcademicController::class, 'submitleaveClerk'])->name('nonAc_leaveClerk_submit');
		Route::post('/nonAc/arToHead/submit', [NonAcademicController::class, 'submitArToHead'])->name('nonAc_arToHead_submit');
		Route::post('/nonAc/arToHead/rejectToClark', [NonAcademicController::class, 'rejectToClark'])->name('rejectToClark');
		Route::post('/nonAc/arToHead/rejectToLeaveClark', [NonAcademicController::class, 'rejectToLeaveClark'])->name('rejectToLeaveClark');
		Route::post('/nonAc/Head/submit', [NonAcademicController::class, 'submitHead'])->name('nonAc_Headsubmit');
		Route::post('/nonAc/submitArToRegistar/submit', [NonAcademicController::class, 'submitArToRegistar'])->name('nonAc_submitArToRegistar');

		Route::post('/nonAc/registar/submit', [NonAcademicController::class, 'submitRegistar'])->name('nonAc_registar_submit');
		Route::get('head/show/increment', [UsjnetController::class, 'headIncrement'])->name('head.show.increment');
		Route::get('registar/show/increment', [UsjnetController::class, 'RegistarIncrement'])->name('registar.show.increment');
		Route::match(['get', 'post'], 'summery/show/increment', [NonAcademicController::class, 'IncrementSummery'])->name('summery.show.increment');
		Route::get('summery/show/increment/search', [NonAcademicController::class, 'monthListIncrementSearch'])->name('summery.show.increment.search');
		Route::get('/nonAc/FinalPrintSummery/{id}', [NonAcademicController::class, 'FinalPrintSummery'])->name('nonAc_FinalPrintSummeryr');
        Route::get('/nonAc/progress/{id}', [NonAcademicController::class, 'progressSummery'])->name('nonAc.progress.summery');

		//Academic
		//1st checking
		Route::match(['get', 'post'], 'academic/1stchecking/open', [AcInrementController::class, 'checkingListOpen'])->name('ac.incre.checking.open');
		Route::get('academic/1stchecking/details/{id}', [AcInrementController::class, 'CheckingDetail'])->name('ac.incre.checking.details');
		Route::post('academic/1stCheck/save', [AcInrementController::class, 'checkingSave'])->name('ac.incre.checking.save');

		//establishment head
		Route::get('academic/eshtablishmentHeat/list/open', [AcInrementController::class, 'diviHeadListOpen'])->name('ac.incre.eshtab.head.list.open');
		Route::get('academic/eshtablishmentHeat/details/{id}', [AcInrementController::class, 'diviHeadForwardDetail'])->name('ac.incre.eshtab.head.details');
		Route::post('academic/eshtablishmentHeat/forward/save', [AcInrementController::class, 'diviHeadForwardStore'])->name('ac.incre.eshtab.head.save');

		//hod list
		Route::get('academic/hod/list/open', [UsjnetController::class, 'hodListOpen'])->name('ac.incre.hod.list.open');
		Route::get('academic/hod/forward/{id}', [AcInrementController::class, 'hodForward'])->name('ac.incre.hod.forward');
		Route::post('academic/hod/forward/save', [AcInrementController::class, 'hodForwardStore'])->name('ac.incre.hod.forward.save');

		// //self evaluation
		// Route::get('/ac/selEval/page1/open', [UsjnetController::class, 'acUsj1stPageOpen'])->name('ac.incre.selfEval.page1.open');
		// Route::get('/ac/selEval/open', [AcInrementController::class, 'selfEvalOpen'])->name('ac.incre.selfEval.open');
		// Route::post('/ac/selEval/store', [AcInrementController::class, 'serSubmit'])->name('ac.incre.selfEval.store');


		//All increment status
		Route::match(['get', 'post'], 'academic/all/status', [AcInrementController::class, 'allIncrement'])->name('ac.incre.all.status');
		Route::get('academic/all/status/open/{id}', [AcInrementController::class, 'allStatusOpen'])->name('ac.incre.all.status.open');

		//hod recommendation
		Route::get('academic/hod/recommendation/{id}', [AcInrementController::class, 'hodRecommendation'])->name('ac.incre.hod.recommendation');
		Route::post('academic/hod/recommendation/store', [AcInrementController::class, 'hodRecomStore'])->name('ac.incre.hod.recommendation.store');
	   //dean
	   Route::get('academic/dean/list/open',[UsjnetController::class,'deanListOpen'])->name('ac.incre.dean.list.open');
	   Route::get('academic/dean/recommendation/{id}',[AcInrementController::class,'deanRecommendation'])->name('ac.incre.dean.recommendation');
	   Route::post('academic/dean/recommendation/store',[AcInrementController::class,'deanRecomStore'])->name('ac.incre.dean.recommendation.store');

		//due list
		Route::get('academic/deu/list/open', [AcInrementController::class, 'deuListOpen'])->name('ac.incre.deu.list.open');
		Route::get('academic/deu/details/open/{id}', [AcInrementController::class, 'dueDetails'])->name('ac.incre.deu.details.open');

	   //my increment
	//    Route::get('academic/myIncrement',[UsjnetController::class,'acUsj1stPageOpen'])->name('ac.increment.myIncremet');

		//head Dean
		Route::get('academic/hod/dean/recommendation', [AcInrementController::class, 'select_hod_dean'])->name('ac.increment.hod.dean.recm');

	   //vc
	   Route::get('academic/vc/list/open',[UsjnetController::class,'vcListOpen'])->name('ac.incre.vc.list.open');
	   Route::get('academic/vc/approval/{id}',[AcInrementController::class,'vcApproval'])->name('ac.incre.vc.approval');
	   Route::post('academic/vc/approval/store',[AcInrementController::class,'vcApprovalStore'])->name('ac.incre.vc.approval.store');

		//MA
		Route::get('academic/ma/list/open', [AcInrementController::class, 'maListOpen'])->name('ac.incre.ma.list.open');
		Route::get('academic/ma/details/{id}', [AcInrementController::class, 'maDetails'])->name('ac.incre.ma.details');
		Route::post('academic/ma/store',[AcInrementController::class,'maStore'])->name('ac.incre.ma.store');

		//Print
		Route::get('academic/increment/print/{id}', [AcInrementController::class, 'increPrint'])->name('ac.incre.print');

		//Finance list
		Route::get('academic/finance/list/open', [AcInrementController::class, 'financeListOpen'])->name('ac.incre.finance.list.open');
		Route::post('academic/finance/list/store',[AcInrementController::class,'financeListStore'])->name('ac.incre.finance.list.store');

		//Finance list view
		Route::match(['get', 'post'], 'academic/finance/list/view', [AcInrementController::class, 'financeListView'])->name('ac.incre.finance.list.view');
		Route::get('academic/finance/list/view/details/{id}', [AcInrementController::class, 'financeListViewDetails'])->name('ac.incre.finance.list.view.details');
		Route::get('academic/finance/list/print/{id}', [AcInrementController::class, 'financeListPrint'])->name('ac.incre.finance.list.print');
	});

	//Employeen Leave Related Route List
	Route::middleware('role:super-admin|administrator|est-head|sc|lc|cc')->prefix('leave')->group(function () {

		Route::match(['get', 'post'], '/index', [LeaveController::class, 'index'])->name('leave.old.index');
		Route::get('/edit/{id}', [LeaveController::class, 'edit'])->name('leave.old.edit');
		Route::post('/update/{id}', [LeaveController::class, 'update'])->name('leave.old.update');

		Route::get('/bulk/upload', [LeaveController::class, 'bulkDataUploadView'])->name('leave.bulk.index');
		Route::match(['get', 'post'], '/import', [LeaveController::class, 'import'])->name('leave.bulk.import');

		Route::match(['get', 'post'], '/print', [LeaveController::class, 'print'])->name('leave.old.print');
		Route::get('/data/print/{id}', [LeaveController::class, 'dataPrint'])->name('leave.data.print');

		// Academic History
		Route::match(['get', 'post'], '/academic/history/open', [AcademicLeaveController::class, 'historyOpen'])->name('leave.ac.history.open');
		Route::post('/academic/history/store', [AcademicLeaveController::class, 'historyStore'])->name('leave.ac.history.store');
	});

	// **************Annual leave***************
	Route::get('/annualLeave/index', [AnnualLeaveController::class, 'AnnualLeaveMA'])->name('annual.leave.clerk.index');

	Route::get('/annualLeave/addNew/{empNo}', [AnnualLeaveController::class, 'AddLeaveData'])->name('add.new.leave');

	// HOD Index
	Route::get('/annualLeave/hodIndex', [AnnualLeaveController::class, 'HODViewEmployees'])->name('hod.leave.index');
	// HOD view
	Route::get('/annualLeave/hodView{empNo}', [AnnualLeaveController::class, 'HODViewLeaves'])->name('hod.leave.view');
	// HOD forward
	Route::post('annualLeave/HOD/update', [AnnualLeaveController::class, 'HODForward'])->name('hod.leave.forward');
	// HOD Reversal
	Route::get('annualLeave/HOD/revers{tableID}', [AnnualLeaveController::class, 'HODRevers'])->name('hod.leave.revers');

	// executive officer department Index
	Route::get('/annualLeave/executive_officer_deptIndex', [AnnualLeaveController::class, 'executiveDept'])->name('executive.officer.leave.detp.index');
	// executive Officer Emp View
	Route::get('/annualLeave/executive_officer_EmpView/{deptNo}/{pid}', [AnnualLeaveController::class, 'executiveEmp'])->name('executive.officer.leave.emp.view');
	// executive Officer Leave View
	Route::get('/annualLeave/executive_officer_LeaveView{empNo}', [AnnualLeaveController::class, 'executiveLeave'])->name('executive.officer.leave.view');
	// executive Officer forward
	Route::post('annualLeave/executive/update', [AnnualLeaveController::class, 'executiveForward'])->name('executive.leave.forward');
	// Month filter in department index
	Route::get('annualLeave/executive/filter', [AnnualLeaveController::class, 'monthFilter'])->name('executive.leave.month.filter');


	// Duty Clerk faculty Index
	Route::get('/annualLeave/duty_clerk_facultyIndex', [AnnualLeaveController::class, 'clerkFaculty'])->name('clerk.leave.faculty.index');
	// Duty Clerk department View
	Route::get('/annualLeave/duty_clerk_deptView{facultyNo}', [AnnualLeaveController::class, 'clerkDepartment'])->name('clerk.leave.detp.view');
	// Duty Clerk Emp View
	Route::get('/annualLeave/duty_clerk_EmpView/{deptNo}/{pid}', [AnnualLeaveController::class, 'clerkEmployee'])->name('clerk.leave.emp.view');
	// Duty Clerk Leave View
	Route::get('/annualLeave/duty_clerk_LeaveView{empNo}', [AnnualLeaveController::class, 'clerkLeave'])->name('clerk.leave.view');
	// Duty Clerk Leave Update
	Route::post('/annualLeave/duty_clerk/update/{id}', [AnnualLeaveController::class, 'clerkLeaveUpdate'])->name('clerk.leave.update');
	// Duty Clerk Add New Leave Record
	Route::post('/annualLeave/duty_clerk/new_record', [AnnualLeaveController::class, 'storeLeave_dutyClerk'])->name('clerk.add.new.leave');
	// Duty Clerk forward
	Route::post('annualLeave/duty_clerk/update', [AnnualLeaveController::class, 'dutyClerkForward'])->name('clerk.leave.forward');
	// Month filter in department
	Route::get('annualLeave/duty_clerk/filter{facultyNo}', [AnnualLeaveController::class, 'monthFilter_clerk'])->name('clerk.leave.month.filter');


	// Deputy registrar faculty Index
	Route::get('/annualLeave/deputy_registrar_facultyIndex', [AnnualLeaveController::class, 'DRFaculty'])->name('deputy.registrar.leave.faculty.index');
	// Deputy registrar department View
	Route::get('/annualLeave/deputy_registrar_deptView{facultyNo}', [AnnualLeaveController::class, 'DRDepartment'])->name('deputy.registrar.leave.detp.view');
	// Deputy registrar Emp View
	Route::get('/annualLeave/deputy_registrar_EmpView/{deptNo}/{pid}', [AnnualLeaveController::class, 'DREmployee'])->name('deputy.registrar.leave.emp.view');
	// Deputy registrar Leave View
	Route::get('/annualLeave/deputy_registrar_LeaveView{empNo}', [AnnualLeaveController::class, 'DRLeave'])->name('deputy.registrar.leave.view');
	// Deputy Registrar forward
	Route::post('annualLeave/deputy_registrar/update', [AnnualLeaveController::class, 'DRApproval'])->name('deputy.registrar.leave.approval');
	// Month filter in department
	Route::get('annualLeave/deputy_registrar/filter{facultyNo}', [AnnualLeaveController::class, 'monthFilter_DR'])->name('deputy.registrar.leave.month.filter');
	// Deputy Registrar Reversal to duty clerk
	Route::post('annualLeave/deputy_registrar/reversal', [AnnualLeaveController::class, 'DRReversal'])->name('deputy.registrar.leave.reversal');

	// Store individual leave
	Route::post('annualLeave/store', [AnnualLeaveController::class, 'storeLeave'])->name('annualLeave.store');
	// remove individual leave
	Route::delete('/annualLeave/delete/{id}', [AnnualLeaveController::class, 'deleteRecord'])->name('leave.delete');
	// Store Leave  Summary
	Route::post('annualLeave/summary/store', [AnnualLeaveController::class, 'storeLeaveSummary'])->name('annualLeave.summary.store');

	// *****************************************

	//Employeen Transfers Related Route List
	Route::middleware('role:super-admin|administrator|est-head|cc|sc')->prefix('inttransfer')->group(function () {
		//Employee Internal Transfer Related Route List
		Route::match(['get', 'post'], '/internal/history/index', [InternalTransferController::class, 'indexInternal'])->name('transfer.internal.history.index');
		Route::post('/internal/history/store', [InternalTransferController::class, 'storeInternal'])->name('transfer.internal.history.store');

		Route::get('/internal/edit/{id}', [InternalTransferController::class, 'editInternal'])->name('transfer.internal.history.edit');
		Route::post('/internal/update/{id}', [InternalTransferController::class, 'updateInternal'])->name('transfer.internal.history.update');
		Route::get('/internal/delete/{id}', [InternalTransferController::class, 'deleteInternal'])->name('transfer.internal.history.delete');

		Route::match(['get', 'post'], '/eligibility/open', [InternalTransferNacController::class, 'eligibleListOpen'])->name('transfer.internal.eligibility.open');
		//internal transfer
		Route::match(['get','post'],'/internal/transfer/employeetransfer/index', [InternalTransferNonAccController::class, 'indexNonAcInternal'])->name('transfer.internal.nonaccedemic');
		Route::post('/internal/transfer/employeetransfer/store', [InternalTransferNonAccController::class, 'storeInternal'])->name('transfer.internal.nonaccedemic.store');
		//Route::get('/internal/transfer/employeetransfer/{id}', [InternalTransferNonAccController::class, 'editInternal'])->name('transfer.internal.nonaccedemic.edit');
		//Route::post('/internal/update/{id}', [InternalTransferNonAccController::class, 'updateInternal'])->name('transfer.internal.nonaccedemic.update');
		Route::get('/internal/transfer/employeetransfer/delete/{id}', [InternalTransferNonAccController::class, 'internalNonAceTransferDelete'])->name('transfer.internal.nonaccedemic.delete');

		//Transfer list
		Route::match(['get','post'],'/internal/transfer/NonAcEmployeetransfer/list', [InternalTransferNonAccController::class, 'internalNonAccTransferList'])->name('transfer.nonacedemic.list');
		Route::match(['get','post'],'/internal/transfer/NonAcEmployeetransfer/details', [InternalTransferNonAccController::class, 'internalNonAceTransferApprove'])->name('transfer.nonacedemic.approve');
		Route::post('/mobile/update/store', [EmployeeUpadateController::class, 'depChangeStore'])->name('department.update.store');

	});

	//Employeen Transfers Related Route List
	Route::middleware('role:super-admin|administrator|est-head|cc|sc')->prefix('exttransfer')->group(function () {

		//Employee external Transfer Related Route List
		Route::match(['get', 'post'], '/external/history/index', [ExternalTransferController::class, 'indexExternal'])->name('transfer.external.history.index');
		Route::post('/external/history/store', [ExternalTransferController::class, 'storeExternalHistory'])->name('transfer.external.history.store');

		Route::get('/external/edit/{id}', [ExternalTransferController::class, 'editExternal'])->name('transfer.external.history.edit');
		Route::post('/external/update/{id}', [ExternalTransferController::class, 'updateExternal'])->name('transfer.external.history.update');
		Route::get('/external/delete/{id}', [ExternalTransferController::class, 'deleteExternal'])->name('transfer.external.history.delete');
	});


	//Employee Bond Related Route List
	Route::middleware('role:super-admin|administrator|est-head|cc|sc')->prefix('bond')->group(function () {

		Route::match(['get', 'post'], '/history/index', [BondController::class, 'oldBondIndex'])->name('old.bond.index');
		Route::post('/history/store', [BondController::class, 'oldBondStore'])->name('old.bond.store');

		Route::get('/edit/{id}', [BondController::class, 'oldBondEdit'])->name('old.bond.edit');
		Route::post('/update/{id}', [BondController::class, 'oldBondUpdate'])->name('old.bond.update');
		Route::get('/delete/{id}', [BondController::class, 'oldBondDelete'])->name('old.bond.delete');

		Route::get('/institute/add/{id}', [BondController::class, 'oldBondInstituteAdd'])->name('old.bond.institute.add');
		Route::post('/institute/store', [BondController::class, 'oldBondInstituteStore'])->name('old.bond.institute.store');
		Route::get('/institute/edit/{id}', [BondController::class, 'oldBondInstituteEdit'])->name('old.bond.institute.edit');
		Route::post('/institute/update/{id}', [BondController::class, 'oldBondInstituteUpdate'])->name('old.bond.institute.update');
		Route::get('/institute/delete/{id}', [BondController::class, 'oldBondInstituteDelete'])->name('old.bond.institute.delete');
	});

	//Commendation Related Route List
	Route::middleware('role:super-admin|administrator|est-head|cc|sc')->prefix('commendation')->group(function () {
		Route::match(['get', 'post'], '/history/index', [AddOldCommendationsController::class, 'index'])->name('commendation.old.history.index');
		Route::post('/history/store', [AddOldCommendationsController::class, 'store'])->name('commendation.old.history.store');

		Route::get('/edit/{id}', [AddOldCommendationsController::class, 'oldCommendationEdit'])->name('old.commendation.edit');
		Route::post('/update/{id}', [AddOldCommendationsController::class, 'oldCommendationUpdate'])->name('old.commendation.update');
		Route::get('/delete/{id}', [AddOldCommendationsController::class, 'oldCommendationDelete'])->name('old.commendation.delete');

		//add new
		Route::get('/add/new/open', [AddOldCommendationsController::class, 'addNewOpen'])->name('commendation.add.new.open');
		Route::post('/add/new/store', [AddOldCommendationsController::class, 'addNewStore'])->name('commendation.add.new.store');
		Route::get('/add/new/remove/{id}', [AddOldCommendationsController::class, 'addNewRemove'])->name('commendation.add.new.remove');

		//accept
		Route::get('/add/new/accept/open', [AddOldCommendationsController::class, 'acceptOpen'])->name('commendation.add.new.accept.open');
		Route::get('/add/new/accept/details/{id}', [AddOldCommendationsController::class, 'addNewAcceptDetails'])->name('commendation.add.new.accept.details');
		Route::post('/add/new/accept/store', [AddOldCommendationsController::class, 'addnewAcceptStore'])->name('commendation.add.new.accept.store');
	});

	//Interview Panel Related Route List
	Route::middleware('role:super-admin|administrator|est-head|cc|sc')->prefix('interviewPanel')->group(function () {

        Route::get('/add/open', [InterviewPanelController::class, 'addInterPanelOpen'])->name('inetviewPanel.add.open');
		Route::post('/add/open/store', [InterviewPanelController::class, 'addInterPanelStore'])->name('inetviewPanel.add.open.store');
		Route::get('/final', [InterviewPanelController::class, 'interviewPanelFinal'])->name('interviewPanel.final');
		Route::get('/edit/{id}', [InterviewPanelController::class, 'interviewPanelEdit'])->name('interviewPanel.edit');
		Route::post('/update/{id}', [InterviewPanelController::class, 'interviewPanelUpdate'])->name('interviewPanel.update');
		Route::get('/delete/{id}', [InterviewPanelController::class, 'interviewPanelDelete'])->name('interviewPanel.delete');
		Route::get('/show/{id}', [InterviewPanelController::class, 'interviewPanelShow'])->name('interviewPanel.show');

		//add members
		Route::match(['get', 'post'], '/add/members/open', [InterviewPanelController::class, 'addPanelMemberOpen'])->name('interviewPanel.add.members.open');
		Route::post('/search_result', [InterviewPanelController::class, 'emp_search'])->name('employee.emp_search');
		Route::post('/members/store', [InterviewPanelController::class, 'interviwMemberStore'])->name('interviewPanel.add.members.store');
		Route::get('/member/delete/{id}', [InterviewPanelController::class, 'panelMemberDelete'])->name('interviewPanel.member.delete');
	});

	//Bulk Email Related Route List
	Route::middleware('role:super-admin')->prefix('operation')->group(function () {

		Route::get('/nic/generate', [EmailController::class, 'nicGenerate'])->name('nic.generate');
		Route::get('/zoom/status/update', [EmailController::class, 'zoomStatusUpdate'])->name('zoom.status.update');
		Route::get('/email/test/{id}', [EmailController::class, 'emailTest'])->name('email.test');
		Route::get('/increment/head/update', [EmailController::class, 'incrementHeadUpdate'])->name('increment.head.update');
		Route::get('/increment/head/update1', [EmailController::class, 'incrementHeadUpdate1'])->name('increment.head.update1');
		Route::get('/increment/dept/update', [EmailController::class, 'incrementDeptUpdate'])->name('increment.dept.update');
	});

	//Exam Board Related Route List
	Route::middleware('role:super-admin|administrator|est-head|cc|sc')->prefix('examBoard')->group(function () {

		Route::get('/add/open', [ExamBoardController::class, 'addExamBoardOpen'])->name('examBoard.add.open');
		Route::post('/add/store', [ExamBoardController::class, 'examBoardStore'])->name('examBoard.add.store');
		Route::match(['get', 'post'], '/add/details/open', [ExamBoardController::class, 'examDetailsOpen'])->name('examBoard.details.add.Open');
		Route::post('/add/details/store', [ExamBoardController::class, 'examDetailsSave'])->name('examBoard.details.add.store');
	});

	//Summary Related Route List
	Route::middleware('role:super-admin')->prefix('summary')->group(function () {

		Route::get('/external/system', [ExternalSystemSummaryController::class, 'externalSystem'])->name('exteranl.system');
		Route::get('/net/user/employeeno', [ExternalSystemSummaryController::class, 'usjNetUserEmployeeNo'])->name('net.user.employeeno');
		Route::get('/net/user/email', [ExternalSystemSummaryController::class, 'usjNetUserEmail'])->name('net.user.email');
		Route::get('/salary/user', [ExternalSystemSummaryController::class, 'salaryUser'])->name('salary.user');
		Route::get('/net/salary/user', [ExternalSystemSummaryController::class, 'usjNetsalaryUser'])->name('net.salary.user');
		Route::get('/salary/active/user', [ExternalSystemSummaryController::class, 'salaryActiveUser'])->name('salary.active.user');
		Route::get('/bank/detail/employee', [ExternalSystemSummaryController::class, 'bankDetailEmployee'])->name('bank.detail.employee');
		Route::get('/upf/etf/employee', [ExternalSystemSummaryController::class, 'upfEtfEmployee'])->name('upf.etf.employee');
		Route::get('/ldap1/employee', [ExternalSystemSummaryController::class, 'ldap1Employee'])->name('ldap1.employee');
		Route::get('/ldap2/employee', [ExternalSystemSummaryController::class, 'ldap2Employee'])->name('ldap2.employee');
		Route::get('/ldap1/employee/email', [ExternalSystemSummaryController::class, 'ldap1EmployeeEmail'])->name('ldap1.employee.email');
		Route::get('/zoom/employee', [ExternalSystemSummaryController::class, 'zoomEmployee'])->name('zoom.employee');
		Route::get('/academic/employee', [ExternalSystemSummaryController::class, 'academicEmployee'])->name('ldap.academic.employee');
		Route::get('/non/academic/employee', [ExternalSystemSummaryController::class, 'nonAcademicEmployee'])->name('ldap.non.academic.employee');
		Route::get('/ldap1/net/employee', [ExternalSystemSummaryController::class, 'ldap1NetEmployee'])->name('ldap1.net.employee');
		Route::get('/ldap1/net/employee/email', [ExternalSystemSummaryController::class, 'ldap1NetEmployeeEmail'])->name('ldap1.net.employee.email');
		Route::get('/email/request', [ExternalSystemSummaryController::class, 'emailRequest'])->name('email.request');

		Route::post('net/mobile/update/{id}', [ExternalSystemSummaryController::class, 'NetMobileUpdate'])->name('net.mobile.update');
		Route::post('salary/upf/update/{id}', [ExternalSystemSummaryController::class, 'salaryUpFUpdate'])->name('salary.upf.update');
		Route::post('salary/etf/update/{id}', [ExternalSystemSummaryController::class, 'salaryEtfUpdate'])->name('salary.etf.update');


		Route::get('/internal/system', [InternalSummaryController::class, 'InternalSystem'])->name('internal.system');
		Route::get('/similar/nic/employee', [InternalSummaryController::class, 'findSimilarNICEmployee'])->name('find.similar.nic.employee');
		Route::get('/similar/email/employee', [InternalSummaryController::class, 'findSimilarEmailEmployee'])->name('find.similar.email.employee');
		Route::get('/faculty/academic/zoom/employee', [InternalSummaryController::class, 'facultyAcademicZoomEmployee'])->name('faculty.academic.zoom.employee');
		Route::get('/department/employee/count/list', [InternalSummaryController::class, 'DepartmentEmployeeCountList'])->name('department.employee.count.list');
		Route::get('/service/termination/employee', [InternalSummaryController::class, 'serviceTerminationEmployee'])->name('service.termination.employee');
		Route::get('/leave/termination/employee', [InternalSummaryController::class, 'leaveTerminationEmployee'])->name('leave.termination.employee');
		Route::get('/nic/dob/employee', [InternalSummaryController::class, 'nicDobEmployee'])->name('nic.dob.employee');
		Route::get('/photo/list/employee', [InternalSummaryController::class, 'photoListEmployee'])->name('photo.list.employee');
        Route::get('/salary/revision/list/employee', [InternalSummaryController::class, 'salaryRevisionEmployee'])->name('salary.revision.employee');
        Route::get('/tin/list/employee', [InternalSummaryController::class, 'TINListEmployee'])->name('tin.list.employee');
	});

	//Academic Years Related Route List
	Route::middleware('role:super-admin|administrator|est-head|cc|sc')->prefix('academicYears')->group(function () {

		Route::get('/add/new/open', [AcademicYearsController::class, 'addNewOpen'])->name('academicYears.add.new.open');
		Route::post('/add/new/store', [AcademicYearsController::class, 'addNewStore'])->name('academicYears.add.new.store');
	});

	//nac election
	Route::middleware('role:super-admin|administrator|cc|sc|lc')->prefix('election')->group(function () {

		Route::get('/nac/vote/check/open', [nacElectionController::class, 'voteChecking'])->name('nac.election.vote.checking');
		Route::get('/nac/vote/confirm/{id1}/{id2}', [nacElectionController::class, 'voteConfirm'])->name('nac.election.vote.confirm');
		Route::post('/nac/vote/check/store', [nacElectionController::class, 'voteStore'])->name('nac.election.vote.checking.store');
		Route::get('/nac/vote/summary/display/open', [nacElectionController::class, 'summaryDisplay'])->name('nac.election.summary.display.open');
		Route::get('/vote-summary/data', [nacElectionController::class, 'getVoteSummaryData'])->name('vote-summary.data');
		Route::get('/nac/vote/summary/dep/open', [nacElectionController::class, 'summary_dep_open'])->name('nac.election.summary.department.open');
		Route::get('/nac/vote/counting/open', [nacElectionController::class, 'voteCountingOpen'])->name('nac.election.vote.counting.open');

		Route::post('/increment-vote', [nacElectionController::class, 'incrementVote'])->name('increment.vote');
	});

	//salary revision
	Route::middleware('role:super-admin|administrator|est-head|cc|sc')->prefix('salRevision')->group(function () {

        Route::get('/allList/open', [SalaryRevision2025Controller::class, 'allListOpen'])->name('salary.revision.allList.open');
		Route::get('/1stCheckingList/open', [SalaryRevision2025Controller::class, 'checkingListOpen'])->name('salary.revision.1stCheckingList.open');
		Route::get('/1stCheckingDetails/open/{id}', [SalaryRevision2025Controller::class, 'checkingDetailsOpen'])->name('salary.revision.1stCheckingDetails.open');
		Route::post('/1stChecking/store',[SalaryRevision2025Controller::class,'checkingSave'])->name('salary.revision.1stChecking.store');

		Route::get('/completedDetails/open/{id}', [SalaryRevision2025Controller::class, 'completedDetailsOpen'])->name('salary.revision.completedDetails.open');

		//2nd checking
		Route::get('/2ndCheckingList/open', [SalaryRevision2025Controller::class, 'checking2Open'])->name('salary.revision.2ndCheckingList.open');
		Route::get('/2ndCheckingDetails/open/{id}', [SalaryRevision2025Controller::class, 'checking2DetailsOpen'])->name('salary.revision.2ndCheckingDetails.open');
		Route::get('/2ndChecking/store/{id}', [SalaryRevision2025Controller::class, 'check2_save'])->name('salary.revision.2ndChecking.store');
		Route::get('/2ndChecking/backTo/1stChecking/{id}', [SalaryRevision2025Controller::class, 'check2_back_to_checking1'])->name('salary.revision.2ndChecking.backTo.1stChecking');

		//accept
		Route::get('/acceptList/open', [SalaryRevision2025Controller::class, 'acceptListOpen'])->name('salary.revision.acceptList.open');
		Route::get('/accept/backTo/1stChecking/{id}', [SalaryRevision2025Controller::class, 'accept_back_to_checking1'])->name('salary.revision.accept.backTo.1stChecking');
		Route::get('/acceptDetails/open/{id}', [SalaryRevision2025Controller::class, 'acceptDetailsOpen'])->name('salary.revision.acceptDetails.open');
		Route::get('/accept/store/{id}', [SalaryRevision2025Controller::class, 'acceptSave'])->name('salary.revision.accept.store');
		Route::get('/accept/all/store', [SalaryRevision2025Controller::class, 'acceptSaveAll'])->name('salary.revision.accept.all.store');

		//print
		Route::get('/print/2025/{id}', [SalaryRevision2025Controller::class, 'print2025'])->name('salary.revision.print.2025');
	});


    Route::middleware('role:super-admin|administrator|est-head')->prefix('setting')->group(function () {

        Route::get('/file/operator/change/index', [FileOperatorChangeController::class, 'index'])->name('file.operator.change.index');
        Route::post('/file/operator/change/assign', [FileOperatorChangeController::class, 'assign'])->name('file.operator.change.assign');
        Route::post('/file/operator/get/employees', [FileOperatorChangeController::class, 'getEmployees'])->name('file.operator.get.employees');
    });


});//prevent Back History Middleware

