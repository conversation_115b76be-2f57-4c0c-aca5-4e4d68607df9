<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('application_forms', function (Blueprint $table) {

            $table->integer('gender_id')->nullable()->after('duty_assume_date');
            $table->integer('faculty_id')->nullable()->after('gender_id');
            $table->integer('department_id')->nullable()->after('faculty_id');
            $table->string('sub_department_id')->nullable()->after('department_id');
            $table->integer('carder_faculty_id')->nullable()->after('sub_department_id');
            $table->integer('carder_department_id')->nullable()->after('carder_faculty_id');
            $table->string('carder_sub_department_id')->nullable()->after('carder_department_id');
            $table->string('file_reference_number')->nullable()->after('carder_sub_department_id');
            $table->decimal('current_basic_salary', 10, 2)->nullable()->after('file_reference_number');
            $table->date('salary_termination_date')->nullable()->after('current_basic_salary');
            $table->integer('added_ma_user_id')->nullable()->after('salary_termination_date');
            $table->date('added_ma_date')->nullable()->after('added_ma_user_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            $table->dropColumn('gender_id');
            $table->dropColumn('faculty_id');
            $table->dropColumn('department_id');
            $table->dropColumn('sub_department_id');
            $table->dropColumn('carder_faculty_id');
            $table->dropColumn('carder_department_id');
            $table->dropColumn('carder_sub_department_id');
            $table->dropColumn('file_reference_number');
            $table->dropColumn('current_basic_salary');
            $table->dropColumn('salary_termination_date');
            $table->dropColumn('added_ma_user_id');
            $table->dropColumn('added_ma_date');
        });
    }
};
