@extends('admin.admin_master')
@section('admin')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Department Heads List</h1>
                </div><!-- /.col -->
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Department Heads</li>
                    </ol>
                </div><!-- /.col -->
            </div><!-- /.row -->
        </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="box">
                        <div class="box-header with-border">
                            <div class="row">
                                <div class="col-md-12">
                                    <a href="{{ route('department.head.add') }}" style="float: right;"
                                        class="btn btn-success mb-5">Add Department Head</a>
                                </div>
                            </div>
                        </div>
                        <!-- /.box-header -->
                        <div class="box-body">
                            <div class="table-responsive">
                                <table id="example1" class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Dept ID</th>
                                            <th>Department Name</th>
                                            <th>Department Head</th>
                                            <th>Appointment Type</th>
                                            <th>Head Position</th>
                                            <th>Appointment Date</th>
                                            <th>Termination Date</th>
                                            @role('super-admin')
                                                <th>Role</th>
                                            @endrole
                                            <th>Status</th>
                                            <th>Assign Person</th>
                                            <th width="25%" data-priority="1">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($departmentHeads as $key => $departmentHead)
                                            @php
                                                $headRoleCount = App\Models\User::join(
                                                    'employees',
                                                    'employees.employee_no',
                                                    '=',
                                                    'users.employee_no',
                                                )
                                                    ->join(
                                                        'model_has_roles',
                                                        'model_has_roles.model_id',
                                                        '=',
                                                        'users.id',
                                                    )
                                                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                                                    ->select(
                                                        'users.id',
                                                        'users.status_id',
                                                        'employees.initials',
                                                        'employees.last_name',
                                                        'employees.employee_no',
                                                    )
                                                    ->selectRaw('GROUP_CONCAT(roles.name) as roles')
                                                    ->where('users.id', '!=', 1)
                                                    ->where('employees.employee_no', $departmentHead->emp_no)
                                                    ->where(function ($query) {
                                                        $query->where('roles.id', 8);
                                                    })
                                                    ->groupBy(
                                                        'users.id',
                                                        'users.status_id',
                                                        'employees.initials',
                                                        'employees.last_name',
                                                        'employees.employee_no',
                                                    )
                                                    ->orderBy('users.id')
                                                    ->count();

                                            @endphp
                                            @auth
                                                @if (Auth()->user()->employee_no == $departmentHead->added_user_id)
                                                    <tr style="background-color: #fffccd;">
                                                    @else
                                                    <tr>
                                                @endif
                                            @endauth
                                            <td>{{ $departmentHead->department_id }}</td>
                                            <td>{{ ucfirst($departmentHead->departmentName->department_name) }}</td>
                                            <td>{{ $departmentHead->employeeName->initials }}
                                                {{ $departmentHead->employeeName->last_name }}</td>
                                            <td>{{ $departmentHead->AppointmentTypeName->category_name }}</td>
                                            <td>{{ $departmentHead->HeadPositionName->category_name }}</td>
                                            <td>{{ date('d-M-Y', strtotime($departmentHead->start_date)) }}</td>
                                            <td>{{ $departmentHead->end_date != '1970-01-01' ? date('d-M-Y', strtotime($departmentHead->end_date)) : '-' }}
                                            </td>
                                            @role('super-admin')
                                            <td>
                                                @if($headRoleCount == 1)
                                                <span class="badge badge-pill badge-success">Head Role Attached</span>
                                                @else
                                                <span class="badge badge-pill badge-danger">Only User Role</span>
                                                @endif
                                            </td>
                                            @endrole
                                            <td>
                                                @if ($departmentHead->active_status == 1)
                                                    <span class="badge badge-pill badge-success">Active</span>
                                                @else
                                                    <span class="badge badge-pill badge-danger">Deactive</span>
                                                @endif
                                            </td>
                                            <td>{{ $departmentHead->Assignuser->initials }}
                                                {{ $departmentHead->Assignuser->last_name }}</td>
                                            <td>
                                                <a href="{{ route('department.head.edit', encrypt($departmentHead->id)) }}"
                                                    class="btn btn-sm btn-info mb-1">Change</a>
                                                @role('super-admin|administrator|est-head|cc')
                                                    <a href="{{ route('employee.show', encrypt($departmentHead->emp_no)) }}"
                                                        class="btn btn-sm btn-primary">Show <i class="fa fa-eye"></i></a>
                                                @endrole
                                                @role('sc')
                                                    @auth
                                                        @if (Auth()->user()->employee_no == $departmentHead->added_user_id)
                                                            <a href="{{ route('employee.show', encrypt($departmentHead->emp_no)) }}"
                                                                class="btn btn-sm btn-primary">Show <i class="fa fa-eye"></i></a>
                                                        @endif
                                                    @endauth
                                                @endrole
                                                @role('super-admin')
                                                    <a href="{{ route('department.head.delete', encrypt($departmentHead->id)) }}"
                                                        class="btn btn-sm btn-danger" id="delete"> <i
                                                            class="fas fa-trash"></i></a>
                                                @endrole
                                            </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <!-- /.box -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
