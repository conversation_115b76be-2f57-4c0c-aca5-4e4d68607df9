
<?php $__env->startSection('admin'); ?>
<!-- Content Header (Page header) -->
  <!-- Main content -->
  <section class="content">
    <div class="container-fluid">
      <div class="row">
        <!-- left column -->
        <div class="col-md-12">
          <!-- general form elements -->
          <div class="card card-primary">
            <div class="card-header">
              <h3 class="card-title"><a href="<?php echo e(URL::previous()); ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true" style="font-size: 25px;"></i></a> Vacancy Add Form</h3>
            </div>
            <!-- /.card-header -->
            <!-- form start -->
            <form method="post" action="<?php echo e(route('nonacademic.vacancy.store')); ?>"  enctype="multipart/form-data">
              <?php echo csrf_field(); ?>
              <div class="card-body">
                <div class="row">
                    <div class="col col-md-6">
                        <div class="form-group">
                          <label for="">Designation<span class="text-danger"> *</span></label>
                          <select name="designation_id" id="designation_id" class="select2bs5" style="width: 100%">
                            <option value="" selected disabled>Select Relavent Designation</option>
                            <?php $__currentLoopData = $designations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $designation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($designation->id); ?>" <?php echo e($designation->id == old('designation_id') ? 'selected' : ''); ?>><?php echo e(ucfirst($designation->designation_name)); ?> - <?php echo e($designation->salary_code); ?>  <?php if($designation->display_name != ''): ?> - (<?php echo e($designation->display_name); ?>) <?php endif; ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                          </select>
                          <span class="text-danger"><?php $__errorArgs = ['designation_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                        </div>
                        </div><!-- col-md-6 -->

                        <div class="col col-md-6">
                            <div class="form-group">
                              <label for="">Designation Category<span class="text-danger"> *</span></label>
                              <select name="designation_category" id="designation_category" class="select2bs5" style="width: 100%">
                                <option value="" selected disabled>Select Relavent Designation Category</option>
                                <option value="138" <?php echo e(138 == old('designation_category') ? 'selected' : ''); ?>>Permanent</option>
                                <option value="140" <?php echo e(140 == old('designation_category') ? 'selected' : ''); ?>>Temporary</option>
                                <option value="141" <?php echo e(141 == old('designation_category')?'selected' : ''); ?>>Contract</option>
                                <option value="142" <?php echo e(142 == old('designation_category')?'selected' : ''); ?>>Assignment Basis</option>
                              </select>
                              <span class="text-danger"><?php $__errorArgs = ['designation_category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                            </div>
                            </div><!-- col-md-6 -->


                    </div>


                            <div class="row">
                              <div class="col col-md-4">
                                <div class="form-group">
                                  <label for="">Vacancy Visibility<span class="text-danger"> *</span></label>
                                  <select name="vacancy_visibility_status" id="vacancy_visibility_status" class="select2bs5" style="width: 100%">
                                    <option value="" selected disabled>Select Vancancy Visibility Type</option>
                                    <?php $__currentLoopData = $vacancyVisibility; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vacancyVisibility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($vacancyVisibility->id); ?>" <?php echo e($vacancyVisibility->id == old('vacancy_visibility_status') ? 'selected' : ''); ?>><?php echo e(ucfirst($vacancyVisibility->category_name)); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                  </select>
                                  <span class="text-danger"><?php $__errorArgs = ['vacancy_visibility_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                                </div>
                                </div><!-- col-md-6 -->
                              <div class="col col-md-4 col-sm-12">
                                <div class="form-group">
                                  <label>Vacancy Start Date<span class="text-danger"> *</span></label>
                                    <div class="input-group date" id="date_opened" data-target-input="nearest">
                                        <input type="text" class="form-control datetimepicker-input" data-target="#date_opened" name="date_opened" value="<?php echo e(old('date_opened')); ?>" placeholder="Ex:- 01-Jan-2000" data-target="#date_opened" data-toggle="datetimepicker">
                                        <div class="input-group-append" data-target="#date_opened" data-toggle="datetimepicker">
                                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                        </div>
                                    </div>
                                    <span class="text-danger"><?php $__errorArgs = ['date_opened'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                                </div>
                              </div>

                              <div class="col col-md-4 col-sm-12">
                                <div class="form-group">
                                  <label>Vacancy End Date<span class="text-danger"> *</span></label>
                                    <div class="input-group date" id="date_closed" data-target-input="nearest">
                                        <input type="text" class="form-control datetimepicker-input" data-target="#date_closed" name="date_closed" value="<?php echo e(old('date_closed')); ?>" placeholder="Ex:- 01-Jan-2000" data-target="#date_closed" data-toggle="datetimepicker">
                                        <div class="input-group-append" data-target="#date_closed" data-toggle="datetimepicker">
                                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                        </div>
                                    </div>
                                    <span class="text-danger"><?php $__errorArgs = ['date_closed'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                                </div>
                              </div>
                                  </div><!-- row -->

                              <div class="row">
                                <div class="col col-md-3">
                              <div class="form-group">
                                <label for="">Minimum Age Limit<span class="text-danger"> *</span></label>
                                <input type="number" class="form-control" id="min_age" name="min_age" value="18">
                                <span class="text-danger"><?php $__errorArgs = ['min_age'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                              </div>
                              </div><!-- col-md-6 -->
                              <div class="col col-md-3">
                                <div class="form-group">
                                  <label for="">Maximum Age Limit<span class="text-danger"> *</span></label>
                                  <input type="number" class="form-control" id="max_age" name="max_age" value="<?php echo e(old('max_age')); ?>">
                                  <span class="text-danger"><?php $__errorArgs = ['max_age'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                                </div>
                                </div><!-- col-md-6 -->
                              </div>

                              <div class="row">
                                <div class="col col-md-12 col-sm-12">
                                <div class="form-group">
                                <label>Vacancy Operators<span class="text-danger"> *</span></label>
                                    <select class="duallistbox" multiple="multiple" name="operator[]">
                                        <?php $__currentLoopData = $operators; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $operator): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($operator->employee_no); ?>" <?php echo e($operator->employee_no == old('operator[]') ? 'selected' : ''); ?>><?php echo e($operator->employee_no); ?> - <?php echo e($operator->initials); ?> <?php echo e($operator->last_name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <span class="text-danger"><?php $__errorArgs = ['operator'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                                  </div>
                                </div>
                              </div>

                                </div>
                                <!-- /.card-body -->
                                <div class="card-footer">
                                  <input type="submit" class="btn btn-primary" value="submit" name="submit">
                                </div>
                              </form>
                            </div>
                            <!-- /.card -->
                          </div>
                          <!--/.col (left) -->
                        </div>
                        <!-- /.row -->
                        </div><!-- /.container-fluid -->
                      </section>
                      <!-- /.content -->

                      <?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\University\HRSystem\resources\views/admin/nonacademic_vacancy/add.blade.php ENDPATH**/ ?>