<?php

namespace App\Http\Controllers\Backend\Employee;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\ContEmpoyeeRequest;
use App\Http\Requests\StoreEmployeeRequest;
use App\Http\Requests\TempEmpoyeeRequest;
use App\Http\Requests\UpdateConEmployeeRequest;
use App\Http\Requests\UpdatePermEmployeeRequest;
use App\Http\Requests\UpdateTempEmployeeRequest;
use App\Imports\LoginTempEmployeeImport;
use App\Models\BondEmp;
use App\Models\BondInstitute;
use App\Models\Category;
use App\Models\City;
use App\Models\Commendation;
use App\Models\ContractEmployee;
use App\Models\Department;
use App\Models\DepartmentHead;
use App\Models\DepartmentSub;
use App\Models\Designation;
use App\Models\DesignationMainGroup;
use App\Models\DesignationSubGroup;
use App\Models\District;
use App\Models\Employee;
use App\Models\EmployeeLockHistory;
use App\Models\EmployeeSalaryStatus;
use App\Models\ExternalTransfer;
use App\Models\Faculty;
use App\Models\FacultyDean;
use App\Models\Increment;
use App\Models\InternalTransfer;
use App\Models\Leave;
use App\Models\LoginTempEmployee;
use App\Models\PermanentEmployee;
use App\Models\Promotion;
use App\Models\Province;
use App\Models\salaryRevision2025;
use App\Models\TemporyEmployee;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Http;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\Permission\Models\Role;

class EmployeeController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc|email-admin|stat');
    }

    public function activeList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;

        if ($mainBranch == 51) {

            $permanentEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_status_id', 110)
                ->whereIn('employee_work_type', [138, 139])
                ->orderByDesc('created_at')
                ->get();

            $temporaryEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_status_id', 110)
                ->whereIn('employee_work_type', [140])
                ->orderByDesc('created_at')
                ->get();

            $contractEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_status_id', 110)
                ->whereIn('employee_work_type', [141])
                ->orderByDesc('created_at')
                ->get();

            $assignmentBasicEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_status_id', 110)
                ->whereIn('employee_work_type', [142])
                ->orderByDesc('created_at')
                ->get();

            return view('admin.employee.index', compact('permanentEmployees', 'temporaryEmployees', 'contractEmployees', 'assignmentBasicEmployees'));
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head', 'cc'])) {


                $permanentEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 52)
                    ->whereIn('employee_work_type', [138, 139])
                    ->orderByDesc('created_at')
                    ->get();

                $temporaryEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 52)
                    ->whereIn('employee_work_type', [140])
                    ->orderByDesc('created_at')
                    ->get();

                $contractEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 52)
                    ->whereIn('employee_work_type', [141])
                    ->orderByDesc('created_at')
                    ->get();

                $assignmentBasicEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 52)
                    ->whereIn('employee_work_type', [142])
                    ->orderByDesc('created_at')
                    ->get();
            } elseif (Auth()->user()->hasRole(['sc'])) {

                $permanentEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 52)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [138, 139])
                    ->orderByDesc('created_at')
                    ->get();

                $temporaryEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 52)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [140])
                    ->orderByDesc('created_at')
                    ->get();

                $contractEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 52)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [141])
                    ->orderByDesc('created_at')
                    ->get();

                $assignmentBasicEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 52)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [142])
                    ->orderByDesc('created_at')
                    ->get();
            }


            return view('admin.employee.index', compact('permanentEmployees', 'temporaryEmployees', 'contractEmployees', 'assignmentBasicEmployees'));
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head', 'cc'])) {

                $permanentEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 53)
                    ->whereIn('employee_work_type', [138, 139])
                    ->orderByDesc('created_at')
                    ->get();

                $temporaryEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 53)
                    ->whereIn('employee_work_type', [140])
                    ->orderByDesc('created_at')
                    ->get();

                $contractEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 53)
                    ->whereIn('employee_work_type', [141])
                    ->orderByDesc('created_at')
                    ->get();

                $assignmentBasicEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 53)
                    ->whereIn('employee_work_type', [142])
                    ->orderByDesc('created_at')
                    ->get();
            } elseif (Auth()->user()->hasRole(['sc'])) {

                $permanentEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 53)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [138, 139])
                    ->orderByDesc('created_at')
                    ->get();

                $temporaryEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 53)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [140])
                    ->orderByDesc('created_at')
                    ->get();

                $contractEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 53)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [141])
                    ->orderByDesc('created_at')
                    ->get();

                $assignmentBasicEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 110)
                    ->where('main_branch_id', 53)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [142])
                    ->orderByDesc('created_at')
                    ->get();
            }

            return view('admin.employee.index', compact('permanentEmployees', 'temporaryEmployees', 'contractEmployees', 'assignmentBasicEmployees'));
        }
    }

    public function inactiveList()
    {
        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;

        if ($mainBranch == 51) {

            $permanentEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_status_id', 111)
                ->whereIn('employee_work_type', [138, 139])
                ->orderByDesc('created_at')
                ->get();

            $temporaryEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_status_id', 111)
                ->whereIn('employee_work_type', [140])
                ->orderByDesc('created_at')
                ->get();

            $contractEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_status_id', 111)
                ->whereIn('employee_work_type', [141])
                ->orderByDesc('created_at')
                ->get();

            $assignmentBasicEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_status_id', 111)
                ->whereIn('employee_work_type', [142])
                ->orderByDesc('created_at')
                ->get();

            return view('admin.employee.inactive_list', compact('permanentEmployees', 'temporaryEmployees', 'contractEmployees', 'assignmentBasicEmployees'));
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head', 'cc'])) {


                $permanentEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 52)
                    ->whereIn('employee_work_type', [138, 139])
                    ->orderByDesc('created_at')
                    ->get();

                $temporaryEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 52)
                    ->whereIn('employee_work_type', [140])
                    ->orderByDesc('created_at')
                    ->get();

                $contractEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 52)
                    ->whereIn('employee_work_type', [141])
                    ->orderByDesc('created_at')
                    ->get();

                $assignmentBasicEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 52)
                    ->whereIn('employee_work_type', [142])
                    ->orderByDesc('created_at')
                    ->get();
            } elseif (Auth()->user()->hasRole(['sc'])) {

                $permanentEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 52)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [138, 139])
                    ->orderByDesc('created_at')
                    ->get();

                $temporaryEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 52)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [140])
                    ->orderByDesc('created_at')
                    ->get();

                $contractEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 52)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [141])
                    ->orderByDesc('created_at')
                    ->get();

                $assignmentBasicEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 52)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [142])
                    ->orderByDesc('created_at')
                    ->get();
            }


            return view('admin.employee.inactive_list', compact('permanentEmployees', 'temporaryEmployees', 'contractEmployees', 'assignmentBasicEmployees'));
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head', 'cc'])) {

                $permanentEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 53)
                    ->whereIn('employee_work_type', [138, 139])
                    ->orderByDesc('created_at')
                    ->get();

                $temporaryEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 53)
                    ->whereIn('employee_work_type', [140])
                    ->orderByDesc('created_at')
                    ->get();

                $contractEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 53)
                    ->whereIn('employee_work_type', [141])
                    ->orderByDesc('created_at')
                    ->get();

                $assignmentBasicEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 53)
                    ->whereIn('employee_work_type', [142])
                    ->orderByDesc('created_at')
                    ->get();
            } elseif (Auth()->user()->hasRole(['sc'])) {

                $permanentEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 53)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [138, 139])
                    ->orderByDesc('created_at')
                    ->get();

                $temporaryEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 53)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [140])
                    ->orderByDesc('created_at')
                    ->get();

                $contractEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 53)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [141])
                    ->orderByDesc('created_at')
                    ->get();

                $assignmentBasicEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.*', 'categories.category_name')
                    ->where('employee_status_id', 111)
                    ->where('main_branch_id', 53)
                    ->where('assign_ma_user_id', $empNo)
                    ->whereIn('employee_work_type', [142])
                    ->orderByDesc('created_at')
                    ->get();
            }

            return view('admin.employee.inactive_list', compact('permanentEmployees', 'temporaryEmployees', 'contractEmployees', 'assignmentBasicEmployees'));
        }
    }

    public function add()
    {
        Log::info('EmployeeController -> new employee add started');

        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        //$mainBranches = $categories->where('category_type_id', '13');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(138, 139));
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
        $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

        $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();
            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        $editData = LoginTempEmployee::find(session()->get('employee_number'));
        Log::info('EmployeeController -> new employee add ended');

        return view('admin.employee.add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'editData', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'CarderDepartment', 'CarderSubDepartment'));
    }

    public function tempAdd()
    {
        Log::info('EmployeeController -> new employee add started');

        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        //$mainBranches = $categories->where('category_type_id', '13');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(140));
        $salaryPaymentTypes = $categories->where('category_type_id', '48');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
        $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

        $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();
            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        $editData = LoginTempEmployee::find(session()->get('employee_number'));
        Log::info('EmployeeController -> new employee add ended');

        return view('admin.employee.temp_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'editData', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }


    public function ContAdd()
    {
        Log::info('EmployeeController -> new employee add started');

        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        //$mainBranches = $categories->where('category_type_id', '13');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(141));
        $salaryPaymentTypes = $categories->where('category_type_id', '48');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
        $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

        $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();
            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        $editData = LoginTempEmployee::find(session()->get('employee_number'));
        Log::info('EmployeeController -> new employee add ended');

        return view('admin.employee.cont_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'editData', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }



    public function checkView()
    {

        Log::info('EmployeeController -> new employee check view started');

        session()->get('employee_number');
        session()->forget('employee_number');
        $loginTempEmployees = LoginTempEmployee::where('employee_no', '!=', Auth()->user()->employee_no)->orderBy('employee_no', 'ASC')->get();

        Log::info('EmployeeController -> new employee check view ended');
        return view('admin.employee.add_main', compact('loginTempEmployees'));
    }

    public function check(Request $request)
    {

        Log::info('EmployeeController -> new employee check started');

        $validatedData = $request->validate([
            'employee_no' => 'required',
        ], [
            'employee_no.required' => 'you need select relavent employee'
        ]);

        $employeeCount = LoginTempEmployee::where('employee_no', '=', $request->employee_no)->where('status', 0)->get()->count();

        if ($employeeCount == 1) {

            $employeeCount = LoginTempEmployee::where('employee_no', '=', $request->employee_no)->get('id');
            $employeeCount = json_decode($employeeCount, true);
            $empTableId = $employeeCount[0]["id"];

            session()->get('employee_number');
            session()->forget('employee_number');
            Session::put('employee_number', $empTableId);


            if ($request->employee_type == 138) {

                return redirect()->route('employee.add');
            } elseif ($request->employee_type == 140) {

                return redirect()->route('employee.temp.add');
            } elseif ($request->employee_type == 141) {

                return redirect()->route('employee.cont.add');
            }

            //return redirect()->route('employee.add');
        } else {

            $notification = array(
                'message' => 'employee already in the system',
                'alert-type' => 'error'
            );

            Log::info('EmployeeController -> new employee check ended');

            return redirect()->route('employee.check.view')->with($notification);
        }
    }


    public function store(StoreEmployeeRequest $request)
    {

        Log::info('EmployeeController-> employee data submit get started');

        $employeeCount = Employee::where('employee_no', '=', $request->employee_no)->get()->count();

        if ($employeeCount == 1) {

            $notification = array(
                'message' => 'employee already in the system',
                'alert-type' => 'error'
            );

            return redirect()->route('employee.check.view')->with($notification);
        }

        if (!session()->get('employee_number')) {

            $notification = array(
                'message' => 'Your session has been expired',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        }

        //get next table id
        $maxnumber = DB::table('employees')
            ->select(DB::raw('MAX(id) as value'))
            ->get();

        $maxValue = json_decode($maxnumber, true);

        $nextId = $maxValue[0]["value"] + 1;

        //get nic information
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

        $nicData = json_decode($empDetails->body(), true);

        $employee_data = new Employee();
        $employee_data->id = $nextId;
        $employee_data->employee_no = $request->employee_no;
        $employee_data->file_reference_number = strtoupper($request->file_reference_number);
        $employee_data->main_branch_id = $request->main_branch_id;
        $employee_data->designation_id = $request->designation_id;
        $employee_data->faculty_id = $request->faculty_id;
        $employee_data->department_id = $request->department_id;
        $employee_data->sub_department_id = $request->sub_department_id;

        $employee_data->carder_faculty_id = $request->carder_faculty_id;
        $employee_data->carder_department_id = $request->carder_department_id;
        $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

        $employee_data->initials = strtoupper($request->initials);
        $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
        $employee_data->last_name = ucwords($request->last_name);
        $employee_data->civil_status_id = $request->civil_status_id;
        $employee_data->gender_id = $request->gender_id;
        $employee_data->race_id = $request->race_id;
        $employee_data->religion_id = $request->religion_id;
        $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
        $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
        $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
        $employee_data->permanent_city_id = $request->permanent_city_id;
        $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
        $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
        $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
        $employee_data->postal_city_id = $request->postal_city_id;
        $employee_data->email = $request->email;
        $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
        $employee_data->citizen_registration_no = $request->citizen_registration_no;
        $employee_data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
        $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
        $employee_data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
        $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
        $employee_data->salary_termination_date_2 = date("Y-m-d", strtotime($request->salary_termination_date_2));
        $employee_data->confirmation_date = date("Y-m-d", strtotime($request->confirmation_date));
        $employee_data->retirement_date = date("Y-m-d", strtotime($request->retirement_date));
        $employee_data->current_basic_salary = $request->current_basic_salary;
        $employee_data->etf_no = $request->etf_no;
        $employee_data->upf_no = strtoupper($request->upf_no);
        $employee_data->pension_reference_no = strtoupper($request->pension_reference_no);
        $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
        $employee_data->added_ma_user_id = Auth()->user()->employee_no;
        $employee_data->added_ma_date = date('Y-m-d');
        //        $employee_data->approved_ar_user_id=$request->approved_ar_user_id;
        //        $employee_data->approved_ar_date=$request->approved_ar_date;
        $employee_data->assign_ma_user_id = Auth()->user()->employee_no;
        $employee_data->assign_ma_date = date('Y-m-d');
        $employee_data->status_id = 1;
        $employee_data->employee_status_id = $request->employee_status_id;
        $employee_data->employee_status_type_id = $request->employee_status_type_id;
        $employee_data->employee_work_type = $request->employee_work_type;
        if ($request->increment_date == '') {
            $employee_data->increment_date = NULL;
        } else {
            $employee_data->increment_date = date("m-d", strtotime($request->increment_date));
        }
        $employee_data->emp_decision_id = 41;
        $employee_data->mobile_no = $request->mobile_no;
        $employee_data->telephone_no = $request->telephone_no;
        $employee_data->nic = strtoupper($request->nic);

        $employee_data->nic_old = $nicData['oldnic'] ?? null;
        $employee_data->nic_new = $nicData['newnic'] ?? null;
        $employee_data->active_nic = $nicData['activenic'] ?? null;
        $employee_data->dob_gen = $nicData['dob'] ?? null;

        $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
        $employee_data->title_id = $request->titel_id;
        $employee_data->salary_payment_type = 266;
        $employee_data->save();


        $data = LoginTempEmployee::find(session()->get('employee_number'));
        $data->status = 1;
        $data->updated_at = Carbon::now();
        $data->save();

        $statusRecord = new EmployeeSalaryStatus();
        $statusRecord->employee_no = session()->get('employee_number');
        $statusRecord->status = 0;
        $statusRecord->created_date = Carbon::now();
        $statusRecord->save();

        session()->get('employee_number');
        session()->forget('employee_number');

        Log::notice('EmployeeController -> Created new employee number - ' . $request->employee_no . ' created by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> new employee creation ended');

        $notification = array(
            'message' => 'New Permanent Employee Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('employee.check.view')->with($notification);
    }

    public function tempStore(TempEmpoyeeRequest $request)
    {

        Log::info('EmployeeController-> tempory employee data submit get started');

        $employeeCount = Employee::where('employee_no', '=', $request->employee_no)->get()->count();

        if ($employeeCount == 1) {

            $notification = array(
                'message' => 'employee already in the system',
                'alert-type' => 'error'
            );

            return redirect()->route('employee.check.view')->with($notification);
        }

        //get next table id
        $maxnumber = DB::table('employees')
            ->select(DB::raw('MAX(id) as value'))
            ->get();

        $maxValue = json_decode($maxnumber, true);

        $nextId = $maxValue[0]["value"] + 1;

        //get nic information
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

        $nicData = json_decode($empDetails->body(), true);

        $employee_data = new Employee();
        $employee_data->id = $nextId;
        $employee_data->employee_no = $request->employee_no;
        $employee_data->file_reference_number = strtoupper($request->file_reference_number);
        $employee_data->main_branch_id = $request->main_branch_id;
        $employee_data->designation_id = $request->designation_id;
        $employee_data->faculty_id = $request->faculty_id;
        $employee_data->department_id = $request->department_id;
        $employee_data->sub_department_id = $request->sub_department_id;

        $employee_data->carder_faculty_id = $request->carder_faculty_id;
        $employee_data->carder_department_id = $request->carder_department_id;
        $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

        $employee_data->initials = strtoupper($request->initials);
        $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
        $employee_data->last_name = ucwords($request->last_name);
        $employee_data->civil_status_id = $request->civil_status_id;
        $employee_data->gender_id = $request->gender_id;
        $employee_data->race_id = $request->race_id;
        $employee_data->religion_id = $request->religion_id;
        $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
        $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
        $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
        $employee_data->permanent_city_id = $request->permanent_city_id;
        $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
        $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
        $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
        $employee_data->postal_city_id = $request->postal_city_id;
        $employee_data->email = $request->email;
        $employee_data->personal_email = $request->personal_email;
        $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
        $employee_data->citizen_registration_no = $request->citizen_registration_no;
        //$employee_data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
        $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
        //$employee_data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
        $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
        //$employee_data->salary_termination_date_2 = date("Y-m-d", strtotime($request->salary_termination_date_2));
        //$employee_data->confirmation_date = date("Y-m-d", strtotime($request->confirmation_date));
        //$employee_data->retirement_date = date("Y-m-d", strtotime($request->retirement_date));
        $employee_data->current_basic_salary = $request->current_basic_salary;
        $employee_data->etf_no = $request->etf_no;
        $employee_data->upf_no = strtoupper($request->upf_no);
        //$employee_data->pension_reference_no = strtoupper($request->pension_reference_no);
        $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
        $employee_data->added_ma_user_id = Auth()->user()->employee_no;
        $employee_data->added_ma_date = date('Y-m-d');
        //        $employee_data->approved_ar_user_id=$request->approved_ar_user_id;
        //        $employee_data->approved_ar_date=$request->approved_ar_date;
        $employee_data->assign_ma_user_id = Auth()->user()->employee_no;
        $employee_data->assign_ma_date = date('Y-m-d');
        $employee_data->status_id = 1;
        $employee_data->employee_status_id = $request->employee_status_id;
        $employee_data->employee_status_type_id = $request->employee_status_type_id;
        $employee_data->employee_work_type = $request->employee_work_type;
        $employee_data->emp_decision_id = 41;
        $employee_data->mobile_no = $request->mobile_no;
        $employee_data->telephone_no = $request->telephone_no;
        $employee_data->nic = strtoupper($request->nic);

        $employee_data->nic_old = $nicData['oldnic'] ?? null;
        $employee_data->nic_new = $nicData['newnic'] ?? null;
        $employee_data->active_nic = $nicData['activenic'] ?? null;
        $employee_data->dob_gen = $nicData['dob'] ?? null;

        $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
        $employee_data->title_id = $request->titel_id;
        $employee_data->salary_payment_type = $request->salary_payment_type;
        $employee_data->save();

        $data = LoginTempEmployee::find(session()->get('employee_number'));
        $data->status = 1;
        $data->updated_at = Carbon::now();
        $data->save();

        $statusRecord = new EmployeeSalaryStatus();
        $statusRecord->employee_no = session()->get('employee_number');
        $statusRecord->status = 0;
        $statusRecord->created_date = Carbon::now();
        $statusRecord->save();

        session()->get('employee_number');
        session()->forget('employee_number');

        Log::notice('EmployeeController -> Created new employee number - ' . $request->employee_no . ' created by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> new employee creation ended');

        $notification = array(
            'message' => 'New Tempory Employee Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('employee.check.view')->with($notification);
    }

    public function contStore(ContEmpoyeeRequest $request)
    {

        Log::info('EmployeeController-> contract employee data submit get started');

        $employeeCount = Employee::where('employee_no', '=', $request->employee_no)->get()->count();

        if ($employeeCount == 1) {

            $notification = array(
                'message' => 'employee already in the system',
                'alert-type' => 'error'
            );

            return redirect()->route('employee.check.view')->with($notification);
        }

        //get next table id
        $maxnumber = DB::table('employees')
            ->select(DB::raw('MAX(id) as value'))
            ->get();

        $maxValue = json_decode($maxnumber, true);

        $nextId = $maxValue[0]["value"] + 1;

        //get nic information
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

        $nicData = json_decode($empDetails->body(), true);

        $employee_data = new Employee();
        $employee_data->id = $nextId;
        $employee_data->employee_no = $request->employee_no;
        $employee_data->file_reference_number = strtoupper($request->file_reference_number);
        $employee_data->main_branch_id = $request->main_branch_id;
        $employee_data->designation_id = $request->designation_id;
        $employee_data->faculty_id = $request->faculty_id;
        $employee_data->department_id = $request->department_id;
        $employee_data->sub_department_id = $request->sub_department_id;

        $employee_data->carder_faculty_id = $request->carder_faculty_id;
        $employee_data->carder_department_id = $request->carder_department_id;
        $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

        $employee_data->initials = strtoupper($request->initials);
        $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
        $employee_data->last_name = ucwords($request->last_name);
        $employee_data->civil_status_id = $request->civil_status_id;
        $employee_data->gender_id = $request->gender_id;
        $employee_data->race_id = $request->race_id;
        $employee_data->religion_id = $request->religion_id;
        $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
        $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
        $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
        $employee_data->permanent_city_id = $request->permanent_city_id;
        $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
        $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
        $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
        $employee_data->postal_city_id = $request->postal_city_id;
        $employee_data->email = $request->email;
        $employee_data->personal_email = $request->personal_email;
        $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
        $employee_data->citizen_registration_no = $request->citizen_registration_no;
        //$employee_data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
        $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
        //$employee_data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
        $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
        $employee_data->sabbatical_start = date("Y-m-d", strtotime($request->sabbatical_start));
        $employee_data->sabbatical_end = date("Y-m-d", strtotime($request->sabbatical_end));
        //$employee_data->retirement_date = date("Y-m-d", strtotime($request->retirement_date));
        $employee_data->current_basic_salary = $request->current_basic_salary;
        //$employee_data->etf_no = $request->etf_no;
        //$employee_data->upf_no = strtoupper($request->upf_no);
        //$employee_data->pension_reference_no = strtoupper($request->pension_reference_no);
        $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
        $employee_data->added_ma_user_id = Auth()->user()->employee_no;
        $employee_data->added_ma_date = date('Y-m-d');
        //        $employee_data->approved_ar_user_id=$request->approved_ar_user_id;
        //        $employee_data->approved_ar_date=$request->approved_ar_date;
        $employee_data->assign_ma_user_id = Auth()->user()->employee_no;
        $employee_data->assign_ma_date = date('Y-m-d');
        $employee_data->status_id = 1;
        $employee_data->employee_status_id = $request->employee_status_id;
        $employee_data->employee_status_type_id = $request->employee_status_type_id;
        $employee_data->employee_work_type = $request->employee_work_type;
        $employee_data->emp_decision_id = 41;
        $employee_data->mobile_no = $request->mobile_no;
        $employee_data->telephone_no = $request->telephone_no;
        $employee_data->nic = strtoupper($request->nic);

        $employee_data->nic_old = $nicData['oldnic'] ?? null;
        $employee_data->nic_new = $nicData['newnic'] ?? null;
        $employee_data->active_nic = $nicData['activenic'] ?? null;
        $employee_data->dob_gen = $nicData['dob'] ?? null;

        $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
        $employee_data->title_id = $request->titel_id;
        $employee_data->salary_payment_type = $request->salary_payment_type;
        $employee_data->save();

        $data = LoginTempEmployee::find(session()->get('employee_number'));
        $data->status = 1;
        $data->updated_at = Carbon::now();
        $data->save();

        $statusRecord = new EmployeeSalaryStatus();
        $statusRecord->employee_no = session()->get('employee_number');
        $statusRecord->status = 0;
        $statusRecord->created_date = Carbon::now();
        $statusRecord->save();

        session()->get('employee_number');
        session()->forget('employee_number');

        Log::notice('EmployeeController -> Created new employee number - ' . $request->employee_no . ' created by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> new employee creation ended');

        $notification = array(
            'message' => 'New Contract Employee Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('employee.check.view')->with($notification);
    }

    public function show($id)
    {
        Log::info('EmployeeController-> employee data show get started');

        if (Auth()->user()->hasRole(['super-admin'])) {

            $empNo = decrypt($id);
            $employee = Employee::find($empNo);
            $departments = Department::all();
            $subDepartments = DepartmentSub::all();

            $designations =  Designation::join('categories as staff_grade', 'designations.staff_grade', '=', 'staff_grade.id')
                ->join('categories as main_group', 'designations.main_group', '=', 'main_group.id')
                ->join('categories as ugc_mis', 'designations.ugc_mis', '=', 'ugc_mis.id')
                ->join('categories as ugc_finance', 'designations.ugc_finance', '=', 'ugc_finance.id')
                ->join('categories as grade_id', 'designations.grade_id', '=', 'grade_id.id')
                ->leftjoin('categories as service_category_id', 'designations.service_category_id', '=', 'service_category_id.id')
                ->select(
                    'designations.id',
                    'designations.designation_name',
                    'designations.salary_code',
                    'staff_grade.category_name as staff_grade',
                    'main_group.category_name as main_group',
                    'ugc_mis.category_name as ugc_mis',
                    'ugc_finance.category_name as ugc_finance',
                    'grade_id.category_name as grade_id',
                    'service_category_id.category_name as service_category'
                    //'salary_scales.salary_scale_txt',
                    //'salary_scale_versions.version_no'
                )
                ->get();

            $scales = Employee::select('salary_scales.salary_scale_txt', 'salary_scale_versions.id as version_id')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('salary_scales', 'salary_scales.salary_code', '=', 'designations.salary_code')
                ->join('salary_scale_versions', 'salary_scale_versions.id', '=', 'salary_scales.salary_scale_version_id')
                ->where('salary_scale_versions.status', 1)
                ->where('employees.employee_no', $empNo)
                ->first();

            $empBonds = BondEmp::where('emp_no', $empNo)->orderBy('effictive_date', 'ASC')->get();
            $empBondUniversities = BondInstitute::where('emp_no', $empNo)->orderBy('bond_effictive_date', 'ASC')->get();
            $increments = Increment::where('emp_no', $empNo)->orderBy('effective_date', 'DESC')->orderBy('basic_sal', 'DESC')->orderBy('decision', 'ASC')->get();
            $promotions = Promotion::where('employee_no', $empNo)->orderBy('duty_assumed_date', 'DESC')->orderBy('type_id', 'ASC')->get();
            $internalTransfers = InternalTransfer::where('emp_no', $empNo)->orderBy('transfer_date', 'DESC')->get();
            $externalTransfers = ExternalTransfer::where('emp_no', $empNo)->orderBy('effective_date', 'DESC')->get();
            $commendations = Commendation::where('emp_no', $empNo)->orderBy('letter_date', 'DESC')->get();
            $leaves = Leave::where('emp_no', $empNo)->orderBy('year', 'DESC')->get();

            $dhead = DepartmentHead::where('department_heads.emp_no', $empNo)->get();

            $fdean = FacultyDean::where('faculty_deans.emp_no', $empNo)->get();

            $users = User::where('employee_no', $empNo)->first();

            $salaryRevision = salaryRevision2025::where('emp_no', $empNo)->first();

            $bankData = $this->getBankData($empNo);

            $activSalaryData = $this->getActiveSalaryData($empNo);

            $allSalaryData = $this->getAllSalaryData($empNo);

            $usjnetDataEmp = $this->getUsjnetData($empNo);

            $usjnetDataEmail = $this->getUsjnetEmailData($employee->email);

            $getLoginStatusData = $this->getLoginStatusData($employee->email);

            if (App::environment(['local', 'production'])) {

                $ldapEmployeeDataEmp = $this->getLdapDataEmp($empNo);
            }

            if (App::environment(['local', 'production'])) {

                $ldapEmployeeDataEmail = $this->getLdapDataEmail($employee->email);
            }

            return view('admin.employee.admin_profile', compact('employee', 'designations', 'scales', 'departments', 'subDepartments', 'empBonds', 'empBondUniversities', 'increments', 'promotions', 'internalTransfers', 'externalTransfers', 'commendations', 'leaves', 'dhead', 'fdean', 'bankData', 'activSalaryData', 'allSalaryData', 'usjnetDataEmp', 'usjnetDataEmail', 'getLoginStatusData', 'ldapEmployeeDataEmp', 'ldapEmployeeDataEmail', 'users', 'salaryRevision'));
        } else {

            $empNo = decrypt($id);
            $employee = Employee::find($empNo);
            $departments = Department::all();
            $subDepartments = DepartmentSub::all();
            $designations =  Designation::join('categories as staff_grade', 'designations.staff_grade', '=', 'staff_grade.id')
                ->join('categories as main_group', 'designations.main_group', '=', 'main_group.id')
                ->join('categories as ugc_mis', 'designations.ugc_mis', '=', 'ugc_mis.id')
                ->join('categories as ugc_finance', 'designations.ugc_finance', '=', 'ugc_finance.id')
                ->join('categories as grade_id', 'designations.grade_id', '=', 'grade_id.id')
                ->select(
                    'designations.id',
                    'designations.designation_name',
                    'designations.salary_code',
                    'staff_grade.category_name as staff_grade',
                    'main_group.category_name as main_group',
                    'ugc_mis.category_name as ugc_mis',
                    'ugc_finance.category_name as ugc_finance',
                    'grade_id.category_name as grade_id',
                    //'salary_scales.salary_scale_txt',
                    //'salary_scale_versions.version_no'
                )
                ->get();
            $scales = Employee::select('salary_scales.salary_scale_txt', 'salary_scale_versions.id as version_id')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('salary_scales', 'salary_scales.salary_code', '=', 'designations.salary_code')
                ->join('salary_scale_versions', 'salary_scale_versions.id', '=', 'salary_scales.salary_scale_version_id')
                ->where('salary_scale_versions.status', 1)
                ->where('employees.employee_no', $empNo)
                ->first();

            $empBonds = BondEmp::where('emp_no', $empNo)->orderBy('effictive_date', 'ASC')->get();
            $empBondUniversities = BondInstitute::where('emp_no', $empNo)->orderBy('bond_effictive_date', 'ASC')->get();
            $increments = Increment::where('emp_no', $empNo)->orderBy('effective_date', 'DESC')->orderBy('basic_sal', 'DESC')->orderBy('decision', 'ASC')->get();
            $promotions = Promotion::where('employee_no', $empNo)->orderBy('duty_assumed_date', 'DESC')->orderBy('type_id', 'ASC')->get();
            $internalTransfers = InternalTransfer::where('emp_no', $empNo)->orderBy('transfer_date', 'DESC')->get();
            $externalTransfers = ExternalTransfer::where('emp_no', $empNo)->orderBy('effective_date', 'DESC')->get();
            $commendations = Commendation::where('emp_no', $empNo)->orderBy('letter_date', 'DESC')->get();
            $leaves = Leave::where('emp_no', $empNo)->orderBy('year', 'DESC')->get();

            $dhead = DepartmentHead::where('department_heads.emp_no', $empNo)->get();

            $fdean = FacultyDean::where('faculty_deans.emp_no', $empNo)->get();

            return view('admin.employee.profile', compact('employee', 'designations', 'scales', 'departments', 'subDepartments', 'empBonds', 'empBondUniversities', 'increments', 'promotions', 'internalTransfers', 'externalTransfers', 'commendations', 'leaves', 'dhead', 'fdean'));
        }
    }

    private function getBankData($empNo)
    {

        $response = Http::withHeaders([
            'Api-Key' => 'c3347824-1983-4e54-9e50-4c136c7392d5',
        ])->get('http://usjnetsso.sjp.ac.lk/api/usjnet/account_number_date_get.php', [
            'strEmpNo' => str_pad($empNo, 4, "0", STR_PAD_LEFT),
        ]);

        // Decode the response
        $bankData = json_decode($response->body(), true);

        if (!empty($bankData) && is_array($bankData) && isset($bankData[0])) {
            $bank_code = $bankData[0]['strBankCode'];
            $bank = $bankData[0]['strBankName'];
            $branch_code = $bankData[0]['strBankBranch'];
            $branch = $bankData[0]['strBranchLocation'];
            $acc_no = $bankData[0]['strEmAcctNumber'];
        } else {
            // Handle the empty or invalid response
            $bank_code = '';
            $bank = '';
            $branch_code = '';
            $branch = '';
            $acc_no = '';
        }

        return [
            'bank_code' => $bank_code,
            'bank' => $bank,
            'branch_code' => $branch_code,
            'branch' => $branch,
            'acc_no' => $acc_no,
        ];
    }

    private function getActiveSalaryData($empNo)
    {

        $response = Http::withHeaders([
            'Api-Key' => 'a2cfb422-ff4d-4ffc-8068-44389504264f',
        ])->get('http://usjnetsso.sjp.ac.lk/api/usjnet/active_employee_get.php', [
            'strEmpNo' => str_pad($empNo, 4, "0", STR_PAD_LEFT),
        ]);

        // Decode the response
        $activSalaryData = json_decode($response->body(), true);

        if (!empty($activSalaryData) && is_array($activSalaryData) && isset($activSalaryData[0])) {
            $name = $activSalaryData[0]['strEmTitle'] . ' ' . $activSalaryData[0]['strEmIni'] . ' ' . $activSalaryData[0]['strEmLastName'];
            $desig = $activSalaryData[0]['strEmDesignation'];
            $email = $activSalaryData[0]['strEmEmail'];
            $nic = $activSalaryData[0]['strEmIDNum'];
            $obsal = $activSalaryData[0]['nOriginalBasicSalary'];
            $bsal = $activSalaryData[0]['nBasicSalary'];
            $appDate = $activSalaryData[0]['dAppDate'];
            $terminateDate = $activSalaryData[0]['dTerminalDate'];
            $dept = $activSalaryData[0]['strDepName'];
        } else {
            // Handle the empty or invalid response
            $name = '';
            $desig = '';
            $email = '';
            $nic = '';
            $obsal = '';
            $bsal = '';
            $appDate = '';
            $terminateDate = '';
            $dept = '';
        }

        return [
            'name' => $name,
            'desig' => $desig,
            'email' => $email,
            'nic' => $nic,
            'obsal' => $obsal,
            'bsal' => $bsal,
            'appDate' => $appDate,
            'terminateDate' => $terminateDate,
            'dept' => $dept
        ];
    }

    private function getAllSalaryData($empNo)
    {

        $response = Http::withHeaders([
            'Api-Key' => '0aac6fcb-b850-4f48-a791-149d88c8344b',
        ])->get('http://usjnetsso.sjp.ac.lk/api/usjnet/all_employee_get.php', [
            'strEmpNo' => str_pad($empNo, 4, "0", STR_PAD_LEFT),
        ]);

        // Decode the response
        $allSalaryData = json_decode($response->body(), true);

        if (!empty($allSalaryData) && is_array($allSalaryData) && isset($allSalaryData[0])) {
            $name = $allSalaryData[0]['strEmTitle'] . ' ' . $allSalaryData[0]['strEmIni'] . ' ' . $allSalaryData[0]['strEmLastName'];
            $desig = $allSalaryData[0]['strEmDesignation'];
            $email = $allSalaryData[0]['strEmEmail'];
            $nic = $allSalaryData[0]['strEmIDNum'];
            $obsal = $allSalaryData[0]['nOriginalBasicSalary'];
            $bsal = $allSalaryData[0]['nBasicSalary'];
            $appDate = $allSalaryData[0]['dAppDate'];
            $terminateDate = $allSalaryData[0]['dTerminalDate'];
            $strUpfNo = $allSalaryData[0]['strUpfNo'];
            $nETFNo = $allSalaryData[0]['nETFNo'];
            $strPentionNumber = $allSalaryData[0]['strPentionNumber'];
        } else {
            // Handle the empty or invalid response
            $name = '';
            $desig = '';
            $email = '';
            $nic = '';
            $obsal = '';
            $bsal = '';
            $appDate = '';
            $terminateDate = '';
            $strUpfNo = '';
            $nETFNo = '';
            $strPentionNumber = '';
        }

        return [
            'name' => $name,
            'desig' => $desig,
            'email' => $email,
            'nic' => $nic,
            'obsal' => $obsal,
            'bsal' => $bsal,
            'appDate' => $appDate,
            'terminateDate' => $terminateDate,
            'strUpfNo' => $strUpfNo,
            'nETFNo' => $nETFNo,
            'strPentionNumber' => $strPentionNumber
        ];
    }

    private function getUsjnetData($empNo)
    {

        $response = Http::withHeaders([
            'Api-Key' => '3ddfb0d6-45c4-4097-84f8-f2dce4e4e276',
        ])->get('http://usjnetsso.sjp.ac.lk/api/usjnet/active_employee_empno_get.php', [
            'reg_no' => str_pad($empNo, 4, "0", STR_PAD_LEFT),
        ]);

        // Decode the response
        $UsjnetData = json_decode($response->body(), true);

        if (!empty($UsjnetData) && is_array($UsjnetData)) {
            $namewithinitials = $UsjnetData['name'];
            $altmail = $UsjnetData['alternate_mail'];
            $username = $UsjnetData['userName'];
            $fname = $UsjnetData['fname'] . ' ' . $UsjnetData['lname'];
            $lectureOrNot = $UsjnetData['lectureOrNot'];
            $terminateDate = $UsjnetData['terminateDate'];
            $faculty = $UsjnetData['faculty'];
            $department = $UsjnetData['department'];
            $tp = $UsjnetData['tp'];
            $sjpmail = $UsjnetData['sjp_mail'];
            $updatepw = $UsjnetData['updatepw'];
            $expirepwupdated = $UsjnetData['expire_pw_updated'];
            $designation = $UsjnetData['designation'];
            $block_time = $UsjnetData['block_time'];
            $user_profileName = $UsjnetData['user_profileName'];
            $accState = $UsjnetData['accState'];
            $isCompleted = $UsjnetData['isCompleted'];
            $isldapUpdated = $UsjnetData['isldapUpdated'];
            $nic = $UsjnetData['nic'];
        } else {
            // Handle the empty or invalid response
            $namewithinitials = '';
            $altmail = '';
            $username = '';
            $fname = '';
            $lectureOrNot = '';
            $terminateDate = '';
            $faculty = '';
            $department = '';
            $tp = '';
            $sjpmail = '';
            $updatepw = '';
            $updatepw = '';
            $expirepwupdated = '';
            $designation = '';
            $block_time = '';
            $user_profileName = '';
            $accState = '';
            $isCompleted = '';
            $isldapUpdated = '';
            $nic = '';
        }

        return [
            'namewithinitials' => $namewithinitials,
            'altmail' => $altmail,
            'username' => $username,
            'fname' => $fname,
            'lectureOrNot' => $lectureOrNot,
            'terminateDate' => $terminateDate,
            'faculty' => $faculty,
            'department' => $department,
            'tp' => $tp,
            'sjpmail' => $sjpmail,
            'updatepw' => $updatepw,
            'expirepwupdated' => $expirepwupdated,
            'designation' => $designation,
            'block_time' => $block_time,
            'user_profileName' => $user_profileName,
            'accState' => $accState,
            'isCompleted' => $isCompleted,
            'isldapUpdated' => $isldapUpdated,
            'nic' => $nic
        ];
    }

    private function getUsjnetEmailData($email)
    {

        $response = Http::withHeaders([
            'Api-Key' => '8e4a4250-f900-425e-979b-b7d98470ba82',
        ])->get('http://usjnetsso.sjp.ac.lk/api/usjnet/active_employee_email_get.php', [
            'sjp_mail' => $email,
        ]);

        // Decode the response
        $UsjnetData = json_decode($response->body(), true);

        if (!empty($UsjnetData) && is_array($UsjnetData)) {
            $namewithinitials = $UsjnetData['name'];
            $altmail = $UsjnetData['alternate_mail'];
            $username = $UsjnetData['userName'];
            $fname = $UsjnetData['fname'] . ' ' . $UsjnetData['lname'];
            $lectureOrNot = $UsjnetData['lectureOrNot'];
            $terminateDate = $UsjnetData['terminateDate'];
            $faculty = $UsjnetData['faculty'];
            $department = $UsjnetData['department'];
            $tp = $UsjnetData['tp'];
            $reg_no = $UsjnetData['reg_no'];
            $updatepw = $UsjnetData['updatepw'];
            $expirepwupdated = $UsjnetData['expire_pw_updated'];
            $designation = $UsjnetData['designation'];
            $block_time = $UsjnetData['block_time'];
            $user_profileName = $UsjnetData['user_profileName'];
            $accState = $UsjnetData['accState'];
            $isCompleted = $UsjnetData['isCompleted'];
            $isldapUpdated = $UsjnetData['isldapUpdated'];
            $nic = $UsjnetData['nic'];
        } else {
            // Handle the empty or invalid response
            $namewithinitials = '';
            $altmail = '';
            $username = '';
            $fname = '';
            $lectureOrNot = '';
            $terminateDate = '';
            $faculty = '';
            $department = '';
            $tp = '';
            $reg_no = '';
            $updatepw = '';
            $updatepw = '';
            $expirepwupdated = '';
            $designation = '';
            $block_time = '';
            $user_profileName = '';
            $accState = '';
            $isCompleted = '';
            $isldapUpdated = '';
            $nic = '';
        }

        return [
            'namewithinitials' => $namewithinitials,
            'altmail' => $altmail,
            'username' => $username,
            'fname' => $fname,
            'lectureOrNot' => $lectureOrNot,
            'terminateDate' => $terminateDate,
            'faculty' => $faculty,
            'department' => $department,
            'tp' => $tp,
            'reg_no' => $reg_no,
            'updatepw' => $updatepw,
            'expirepwupdated' => $expirepwupdated,
            'designation' => $designation,
            'block_time' => $block_time,
            'user_profileName' => $user_profileName,
            'accState' => $accState,
            'isCompleted' => $isCompleted,
            'isldapUpdated' => $isldapUpdated,
            'nic' => $nic
        ];
    }

    private function getLoginStatusData($email)
    {

        $response = Http::withHeaders([
            'Api-Key' => '2e4a4250-f900-425e-979b-b7d98470ba82',
        ])->get('http://usjnetsso.sjp.ac.lk/api/usjnet/user_login_status.php', [
            'sjp_mail' => $email,
        ]);

        // Decode the response
        $UsjnetData = json_decode($response->body(), true);

        if (!empty($UsjnetData) && is_array($UsjnetData)) {
            $id = $UsjnetData['id'];
            $name = $UsjnetData['name'];
            $username = $UsjnetData['username'];
            $email = $UsjnetData['email'];
            $created_at = $UsjnetData['created_at'];
            $updated_at = $UsjnetData['updated_at'];
        } else {
            // Handle the empty or invalid response
            $id = '';
            $name = '';
            $username = '';
            $email = '';
            $created_at = '';
            $updated_at = '';
        }

        return [
            'id' => $id,
            'name' => $name,
            'username' => $username,
            'email' => $email,
            'created_at' => $created_at,
            'updated_at' => $updated_at
        ];
    }

    private function getLdapDataEmp($empNo)
    {

        $response = Http::withHeaders([
            'X-API-KEY' => 'e28c73d6-8c4a-4f15-a43a-97187259412c' // Ensure this matches your original API's expected key
        ])->get('https://usjnetsso.sjp.ac.lk/sso/api/ldap/emp/' . $empNo);

        if ($response->successful()) {
            return $response->json();
        } else {
            return [];
        }
    }

    private function getLdapDataEmail($email)
    {

        $response = Http::withHeaders([
            'X-API-KEY' => 'e28c73d6-8c4a-4f15-a43a-97187259412c' // Ensure this matches your original API's expected key
        ])->get('https://usjnetsso.sjp.ac.lk/sso/api/ldap/email/' . $email);

        if ($response->successful()) {
            return $response->json();
        } else {
            return [];
        }
    }

    public function showNotification($employee_no, $notification_id)
    {
        Log::info('EmployeeController-> employee data show get started');

        auth()->user()->notifications()->where('id', $notification_id)->update(['read_at' => now()]);

        $empNo = decrypt($employee_no);
        $employee = Employee::find($empNo);
        $categories = $this->getCategories([5]);
        $titles = $categories->where('category_type_id', '5');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::all();
        $subDepartments = DepartmentSub::all();
        $designations =  DB::table('designations')
            ->join('categories as staff_grade', 'designations.staff_grade', '=', 'staff_grade.id')
            ->join('categories as main_group', 'designations.main_group', '=', 'main_group.id')
            ->join('categories as ugc_mis', 'designations.ugc_mis', '=', 'ugc_mis.id')
            ->join('categories as ugc_finance', 'designations.ugc_finance', '=', 'ugc_finance.id')
            ->join('categories as grade_id', 'designations.grade_id', '=', 'grade_id.id')
            ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'staff_grade.category_name as staff_grade', 'main_group.category_name as main_group', 'ugc_mis.category_name as ugc_mis', 'ugc_finance.category_name as ugc_finance', 'grade_id.category_name as grade_id')
            ->get();
        $empBonds = BondEmp::where('emp_no', $empNo)->orderBy('effictive_date', 'ASC')->get();
        $empBondUniversities = BondInstitute::where('emp_no', $empNo)->orderBy('bond_effictive_date', 'ASC')->get();
        $increments = Increment::where('emp_no', $empNo)->orderBy('effective_date', 'DESC')->get();
        $promotions = Promotion::where('employee_no', $empNo)->orderBy('duty_assumed_date', 'DESC')->orderBy('type_id', 'ASC')->get();
        $internalTransfers = InternalTransfer::where('emp_no', $empNo)->orderBy('transfer_date', 'DESC')->get();
        $externalTransfers = ExternalTransfer::where('emp_no', $empNo)->orderBy('effective_date', 'DESC')->get();
        $commendations = Commendation::where('emp_no', $empNo)->orderBy('letter_date', 'DESC')->get();
        $leaves = Leave::where('emp_no', $empNo)->orderBy('year', 'DESC')->get();

        $dhead = DepartmentHead::where('department_heads.emp_no', $empNo)->get();

        $fdean = FacultyDean::where('faculty_deans.emp_no', $empNo)->get();

        Log::info('EmployeeController-> employee data show get ended');

        return view('admin.employee.profile', compact('employee', 'titles', 'faculties', 'departments', 'designations', 'cities', 'subDepartments', 'designations', 'empBonds', 'empBondUniversities', 'increments', 'promotions', 'internalTransfers', 'externalTransfers', 'commendations', 'leaves', 'dhead', 'fdean'));
    }

    public function edit($id)
    {
        Log::info('EmployeeController -> employee edit started');
        $empNo = decrypt($id);
        $employee = Employee::find($empNo);
        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $mainBranches = $categories->where('category_type_id', '13');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');
        $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', $employee->employee_status_id);
        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(138, 139));
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', $employee->faculty_id)->get();
        $subDepartments = DepartmentSub::where('department_code', '=', $employee->department_id)->get();

        $CarderDepartment = Department::where('faculty_code', '=', $employee->carder_faculty_id)->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', $employee->carder_department_id)->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();
            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        $employeeTypes = $categories->where('category_type_id', '23');


        Log::notice('EmployeeController -> edit employee number - ' . $employee->employee_no . ' edited by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> employee edit ended');

        return view('admin.employee.edit', compact('employee', 'titles', 'genders', 'races', 'religions', 'civilStatuses', 'cities', 'citizenships', 'mainBranches', 'designations', 'faculties', 'departments', 'subDepartments', 'educationLevels', 'employeeStatusIds', 'employeeStatusTypes', 'employeeTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }

    public function tempEdit($id)
    {
        Log::info('EmployeeController -> employee edit started');
        $empNo = decrypt($id);
        $employee = Employee::find($empNo);
        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $mainBranches = $categories->where('category_type_id', '13');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');
        $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', $employee->employee_status_id);
        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(140));
        $salaryPaymentTypes = $categories->where('category_type_id', '48');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', $employee->faculty_id)->get();
        $subDepartments = DepartmentSub::where('department_code', '=', $employee->department_id)->get();

        $CarderDepartment = Department::where('faculty_code', '=', $employee->carder_faculty_id)->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', $employee->carder_department_id)->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();
            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        $employeeTypes = $categories->where('category_type_id', '23');


        Log::notice('EmployeeController -> edit employee number - ' . $employee->employee_no . ' edited by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> employee edit ended');

        return view('admin.employee.temp_edit', compact('employee', 'titles', 'genders', 'races', 'religions', 'civilStatuses', 'cities', 'citizenships', 'mainBranches', 'designations', 'faculties', 'departments', 'subDepartments', 'educationLevels', 'employeeStatusIds', 'employeeStatusTypes', 'employeeTypes', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }

    public function contEdit($id)
    {
        Log::info('EmployeeController -> employee edit started');
        $empNo = decrypt($id);
        $employee = Employee::find($empNo);
        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $mainBranches = $categories->where('category_type_id', '13');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');
        $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', $employee->employee_status_id);
        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(141));
        $salaryPaymentTypes = $categories->where('category_type_id', '48');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', $employee->faculty_id)->get();
        $subDepartments = DepartmentSub::where('department_code', '=', $employee->department_id)->get();

        $CarderDepartment = Department::where('faculty_code', '=', $employee->carder_faculty_id)->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', $employee->carder_department_id)->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();
            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        $employeeTypes = $categories->where('category_type_id', '23');


        Log::notice('EmployeeController -> edit employee number - ' . $employee->employee_no . ' edited by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> employee edit ended');

        return view('admin.employee.cont_edit', compact('employee', 'titles', 'genders', 'races', 'religions', 'civilStatuses', 'cities', 'citizenships', 'mainBranches', 'designations', 'faculties', 'departments', 'subDepartments', 'educationLevels', 'employeeStatusIds', 'employeeStatusTypes', 'employeeTypes', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }

    public function assignBasisEdit($id)
    {
        Log::info('EmployeeController -> employee edit started');
        $empNo = decrypt($id);
        $employee = Employee::find($empNo);
        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $mainBranches = $categories->where('category_type_id', '13');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');
        $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', $employee->employee_status_id);
        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(140));
        $salaryPaymentTypes = $categories->where('category_type_id', '48');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', $employee->faculty_id)->get();
        $subDepartments = DepartmentSub::where('department_code', '=', $employee->department_id)->get();

        $CarderDepartment = Department::where('faculty_code', '=', $employee->carder_faculty_id)->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', $employee->carder_department_id)->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  DB::table('designations')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();
            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        $employeeTypes = $categories->where('category_type_id', '23');


        Log::notice('EmployeeController -> edit employee number - ' . $employee->employee_no . ' edited by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> employee edit ended');

        return view('admin.employee.assign_basis_edit', compact('employee', 'titles', 'genders', 'races', 'religions', 'civilStatuses', 'cities', 'citizenships', 'mainBranches', 'designations', 'faculties', 'departments', 'subDepartments', 'educationLevels', 'employeeStatusIds', 'employeeStatusTypes', 'employeeTypes', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }

    private function checkSimilarNIC($nic)
    {
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post('https://hrms.sjp.ac.lk/api/nic/data', [
            'nic' => strtoupper($nic)
        ]);

        $nicData = json_decode($empDetails->body(), true);

        $exists = Employee::where(function ($query) use ($nicData) {
            $query->where('nic_old', $nicData['oldnic'])
                ->orWhere('nic_new', $nicData['newnic']);
        })->where('employee_status_id', 110)->exists();

        return $exists;
    }


    private function activeStatusChangeType($previous, $current)
    {
        if ($previous == 111 && $current != 111) {
            return 'inactive_to_other';
        }

        if ($previous == 110 && $current != 110) {
            return 'active_to_other';
        }

        return 'no_change';
    }


    public function update(UpdatePermEmployeeRequest $request, $id)
    {
        Log::info('EmployeeController -> employee update get started');

        //get nic information
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

        $nicData = json_decode($empDetails->body(), true);

        $data = Employee::findOrFail($id);
        $data->id = $request->id;
        $data->employee_no = $request->employee_no;
        $data->file_reference_number = strtoupper($request->file_reference_number);
        $data->main_branch_id = $request->main_branch_id;
        $data->designation_id = $request->designation_id;
        $data->faculty_id = $request->faculty_id;
        $data->department_id = $request->department_id;
        $data->sub_department_id = $request->sub_department_id;

        $data->carder_faculty_id = $request->carder_faculty_id;
        $data->carder_department_id = $request->carder_department_id;
        $data->carder_sub_department_id = $request->carder_sub_department_id;

        $data->initials = strtoupper($request->initials);
        $data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
        $data->last_name = ucwords($request->last_name);
        $data->civil_status_id = $request->civil_status_id;
        $data->gender_id = $request->gender_id;
        $data->race_id = $request->race_id;
        $data->religion_id = $request->religion_id;
        $data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
        $data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
        $data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
        $data->permanent_city_id = $request->permanent_city_id;
        $data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
        $data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
        $data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
        $data->postal_city_id = $request->postal_city_id;
        $data->email = $request->email;
        $data->personal_email = $request->personal_email;
        $data->state_of_citizenship_id = $request->state_of_citizenship_id;
        $data->citizen_registration_no = $request->citizen_registration_no;
        $data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
        $data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
        $data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
        $data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
        $data->salary_termination_date_2 = date("Y-m-d", strtotime($request->salary_termination_date_2));
        $data->confirmation_date = date("Y-m-d", strtotime($request->confirmation_date));
        $data->retirement_date = date("Y-m-d", strtotime($request->retirement_date));
        $data->current_basic_salary = $request->current_basic_salary;
        $data->etf_no = $request->etf_no;
        $data->upf_no = strtoupper($request->upf_no);
        $data->pension_reference_no = strtoupper($request->pension_reference_no);
        $data->emp_highest_edu_level = $request->emp_highest_edu_level;
        $data->updated_ma_user_id = Auth()->user()->employee_no;
        $data->updated_ma_date = date('Y-m-d');
        $data->status_id = 1;

        $statusChange = $this->activeStatusChangeType(
            $data->employee_status_id,
            $request->employee_status_id
        );

        if ($statusChange === 'inactive_to_other') {

            if ($this->checkSimilarNIC($request->nic)) {
                return redirect()->back()->withErrors([
                    'nic' => 'An active employee with this NIC already exists in the system.'
                ]);
            }

            $data->employee_status_id = $request->employee_status_id;



        } elseif ($statusChange === 'active_to_other') {

           $data->employee_status_id = $request->employee_status_id;
           $data->lock = 1;

        } else {

            $data->employee_status_id = $request->employee_status_id;
        }

        $data->employee_status_type_id = $request->employee_status_type_id;
        $data->employee_work_type = $request->employee_work_type;
        if ($request->increment_date == '') {
            $data->increment_date = NULL;
        } else {
            $data->increment_date = date("m-d", strtotime($request->increment_date));
        }
        $data->emp_decision_id = 41;
        $data->mobile_no = $request->mobile_no;
        $data->telephone_no = $request->phone_no;
        $data->nic = strtoupper($request->nic);

        $data->nic_old = $nicData['oldnic'] ?? null;
        $data->nic_new = $nicData['newnic'] ?? null;
        $data->active_nic = $nicData['activenic'] ?? null;
        $data->dob_gen = $nicData['dob'] ?? null;

        $data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
        $data->title_id = $request->titel_id;
        $data->salary_payment_type = 266;
        $data->save();

        Log::warning('EmployeeController -> update employee number - ' . $request->employee_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> employee update ended');



        if (Auth()->user()->id == 2) {

            echo '<script type="text/javascript">', 'history.go(-2);', '</script>';
        } else {

            $notification = array(
                'message' => 'Employee Updated Successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.index')->with($notification);
        }
    }

    public function tempUpdate(UpdateTempEmployeeRequest $request, $id)
    {
        Log::info('EmployeeController -> tempory employee update get started');

        //get nic information
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

        $nicData = json_decode($empDetails->body(), true);

        $data = Employee::find($id);
        $data->id = $request->id;
        $data->employee_no = $request->employee_no;
        $data->file_reference_number = strtoupper($request->file_reference_number);
        $data->main_branch_id = $request->main_branch_id;
        $data->designation_id = $request->designation_id;
        $data->faculty_id = $request->faculty_id;
        $data->department_id = $request->department_id;
        $data->sub_department_id = $request->sub_department_id;

        $data->carder_faculty_id = $request->carder_faculty_id;
        $data->carder_department_id = $request->carder_department_id;
        $data->carder_sub_department_id = $request->carder_sub_department_id;

        $data->initials = strtoupper($request->initials);
        $data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
        $data->last_name = ucwords($request->last_name);
        $data->civil_status_id = $request->civil_status_id;
        $data->gender_id = $request->gender_id;
        $data->race_id = $request->race_id;
        $data->religion_id = $request->religion_id;
        $data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
        $data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
        $data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
        $data->permanent_city_id = $request->permanent_city_id;
        $data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
        $data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
        $data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
        $data->postal_city_id = $request->postal_city_id;
        $data->email = $request->email;
        $data->personal_email = $request->personal_email;
        $data->state_of_citizenship_id = $request->state_of_citizenship_id;
        $data->citizen_registration_no = $request->citizen_registration_no;

        $data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));

        $data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));

        $data->current_basic_salary = $request->current_basic_salary;
        $data->etf_no = $request->etf_no;
        $data->upf_no = strtoupper($request->upf_no);

        $data->emp_highest_edu_level = $request->emp_highest_edu_level;
        $data->updated_ma_user_id = Auth()->user()->employee_no;
        $data->updated_ma_date = date('Y-m-d');

        $data->status_id = 1;

        $statusChange = $this->activeStatusChangeType(
            $data->employee_status_id,
            $request->employee_status_id
        );

        if ($statusChange === 'inactive_to_other') {

            if ($this->checkSimilarNIC($request->nic)) {
                return redirect()->back()->withErrors([
                    'nic' => 'An active employee with this NIC already exists in the system.'
                ]);
            }

            $data->employee_status_id = $request->employee_status_id;

        } else {

            $data->employee_status_id = $request->employee_status_id;
        }


        $data->employee_status_type_id = $request->employee_status_type_id;
        $data->emp_decision_id = 41;
        $data->mobile_no = $request->mobile_no;
        $data->telephone_no = $request->phone_no;
        $data->nic = strtoupper($request->nic);

        $data->nic_old = $nicData['oldnic'] ?? null;
        $data->nic_new = $nicData['newnic'] ?? null;
        $data->active_nic = $nicData['activenic'] ?? null;
        $data->dob_gen = $nicData['dob'] ?? null;

        $data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
        $data->title_id = $request->titel_id;
        $data->salary_payment_type = $request->salary_payment_type;
        $data->save();

        Log::warning('EmployeeController -> update tempory employee number - ' . $request->employee_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> tempory employee update ended');

        $notification = array(
            'message' => 'Employee Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('employee.index')->with($notification);
    }

    public function contUpdate(UpdateConEmployeeRequest $request, $id)
    {
        Log::info('EmployeeController -> contract employee update get started');

        //get nic information
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

        $nicData = json_decode($empDetails->body(), true);

        $data = Employee::find($id);
        $data->id = $request->id;
        $data->employee_no = $request->employee_no;
        $data->file_reference_number = strtoupper($request->file_reference_number);
        $data->main_branch_id = $request->main_branch_id;
        $data->designation_id = $request->designation_id;
        $data->faculty_id = $request->faculty_id;
        $data->department_id = $request->department_id;
        $data->sub_department_id = $request->sub_department_id;

        $data->carder_faculty_id = $request->carder_faculty_id;
        $data->carder_department_id = $request->carder_department_id;
        $data->carder_sub_department_id = $request->carder_sub_department_id;

        $data->initials = strtoupper($request->initials);
        $data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
        $data->last_name = ucwords($request->last_name);
        $data->civil_status_id = $request->civil_status_id;
        $data->gender_id = $request->gender_id;
        $data->race_id = $request->race_id;
        $data->religion_id = $request->religion_id;
        $data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
        $data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
        $data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
        $data->permanent_city_id = $request->permanent_city_id;
        $data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
        $data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
        $data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
        $data->postal_city_id = $request->postal_city_id;
        $data->email = $request->email;
        $data->personal_email = $request->personal_email;
        $data->state_of_citizenship_id = $request->state_of_citizenship_id;
        $data->citizen_registration_no = $request->citizen_registration_no;

        $data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));

        $data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
        $data->sabbatical_start = date("Y-m-d", strtotime($request->sabbatical_start));
        $data->sabbatical_end = date("Y-m-d", strtotime($request->sabbatical_end));
        $data->current_basic_salary = $request->current_basic_salary;

        $data->emp_highest_edu_level = $request->emp_highest_edu_level;
        $data->updated_ma_user_id = Auth()->user()->employee_no;
        $data->updated_ma_date = date('Y-m-d');

        $data->status_id = 1;

        $statusChange = $this->activeStatusChangeType(
            $data->employee_status_id,
            $request->employee_status_id
        );

        if ($statusChange === 'inactive_to_other') {

            if ($this->checkSimilarNIC($request->nic)) {
                return redirect()->back()->withErrors([
                    'nic' => 'An active employee with this NIC already exists in the system.'
                ]);
            }

            $data->employee_status_id = $request->employee_status_id;

        } else {

            $data->employee_status_id = $request->employee_status_id;
        }

        $data->employee_status_type_id = $request->employee_status_type_id;
        $data->emp_decision_id = 41;
        $data->mobile_no = $request->mobile_no;
        $data->telephone_no = $request->phone_no;
        $data->nic = strtoupper($request->nic);

        $data->nic_old = $nicData['oldnic'] ?? null;
        $data->nic_new = $nicData['newnic'] ?? null;
        $data->active_nic = $nicData['activenic'] ?? null;
        $data->dob_gen = $nicData['dob'] ?? null;

        $data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
        $data->title_id = $request->titel_id;
        $data->salary_payment_type = $request->salary_payment_type;
        $data->save();

        Log::warning('EmployeeController -> update contract employee number - ' . $request->employee_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> contract employee update ended');

        $notification = array(
            'message' => 'Employee Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('employee.index')->with($notification);
    }

    public function assignBasisUpdate(UpdateTempEmployeeRequest $request, $id)
    {
        Log::info('EmployeeController -> assignment basis employee update get started');

        //get nic information
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

        $nicData = json_decode($empDetails->body(), true);

        $data = Employee::find($id);
        $data->id = $request->id;
        $data->employee_no = $request->employee_no;
        $data->file_reference_number = strtoupper($request->file_reference_number);
        $data->main_branch_id = $request->main_branch_id;
        $data->designation_id = $request->designation_id;
        $data->faculty_id = $request->faculty_id;
        $data->department_id = $request->department_id;
        $data->sub_department_id = $request->sub_department_id;

        $data->carder_faculty_id = $request->carder_faculty_id;
        $data->carder_department_id = $request->carder_department_id;
        $data->carder_sub_department_id = $request->carder_sub_department_id;

        $data->initials = strtoupper($request->initials);
        $data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
        $data->last_name = ucwords($request->last_name);
        $data->civil_status_id = $request->civil_status_id;
        $data->gender_id = $request->gender_id;
        $data->race_id = $request->race_id;
        $data->religion_id = $request->religion_id;
        $data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
        $data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
        $data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
        $data->permanent_city_id = $request->permanent_city_id;
        $data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
        $data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
        $data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
        $data->postal_city_id = $request->postal_city_id;
        $data->email = $request->email;
        $data->personal_email = $request->personal_email;
        $data->state_of_citizenship_id = $request->state_of_citizenship_id;
        $data->citizen_registration_no = $request->citizen_registration_no;

        $data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
        $data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));

        $data->current_basic_salary = $request->current_basic_salary;
        $data->etf_no = $request->etf_no;
        $data->upf_no = strtoupper($request->upf_no);

        $data->emp_highest_edu_level = $request->emp_highest_edu_level;
        $data->updated_ma_user_id = Auth()->user()->employee_no;
        $data->updated_ma_date = date('Y-m-d');

        $data->status_id = 1;

        $statusChange = $this->activeStatusChangeType(
            $data->employee_status_id,
            $request->employee_status_id
        );

        if ($statusChange === 'inactive_to_other') {

            if ($this->checkSimilarNIC($request->nic)) {
                return redirect()->back()->withErrors([
                    'nic' => 'An active employee with this NIC already exists in the system.'
                ]);
            }

            $data->employee_status_id = $request->employee_status_id;

        } else {

            $data->employee_status_id = $request->employee_status_id;
        }

        $data->employee_status_type_id = $request->employee_status_type_id;
        $data->emp_decision_id = 41;
        $data->mobile_no = $request->mobile_no;
        $data->telephone_no = $request->phone_no;
        $data->nic = strtoupper($request->nic);

        $data->nic_old = $nicData['oldnic'] ?? null;
        $data->nic_new = $nicData['newnic'] ?? null;
        $data->active_nic = $nicData['activenic'] ?? null;
        $data->dob_gen = $nicData['dob'] ?? null;

        $data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
        $data->title_id = $request->titel_id;
        $data->salary_payment_type = $request->salary_payment_type;
        $data->save();

        Log::warning('EmployeeController -> update assignment basis employee number - ' . $request->employee_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> assignment basis employee update ended');

        $notification = array(
            'message' => 'Employee Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('employee.index')->with($notification);
    }


    public function softDelete($id)
    {
        Log::info('EmployeeController -> employee soft delete started');
        $empNo = decrypt($id);
        $employee = Employee::find($empNo);
        $employee->delete();

        $notification = array(
            'message' => 'Employee Deleted Successfully',
            'alert-type' => 'warning'
        );

        Log::notice('EmployeeController -> soft delete employee number - ' . $employee->employee_no . ' soft deleted by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> Employee soft delete ended');

        return redirect()->route('employee.index')->with($notification);
    }

    public function restore($id)
    {

        Log::info('EmployeeController -> employee restore started');
        $empNo = decrypt($id);
        $employee = Employee::withTrashed()->find($empNo);
        $employee->restore();

        $notification = array(
            'message' => 'Employee Restore Successfully',
            'alert-type' => 'success'
        );

        Log::notice('EmployeeController -> restore employee number - ' . $employee->employee_no . ' restore by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> employee restore ended');
        return redirect()->route('employee.index')->with($notification);
    }

    public function delete($id)
    {

        Log::info('EmployeeController -> employee delete started');
        $empNo = decrypt($id);
        $employee = Employee::onlyTrashed()->find($empNo);
        $employee->forceDelete();

        $notification = array(
            'message' => 'Employee Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        Log::emergency('EmployeeController -> delete employee number - ' . $employee->employee_no . ' deleted by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> employee permanetly delete ended');

        return redirect()->route('employee.index')->with($notification);
    }

    public function emailCreate()
    {

        Log::info('EmployeeController -> employee email add started');

        // $loginTempEmployees = LoginTempEmployee::where('status', 0)
        //                       ->where('email', NULL)
        //                       ->orwhere('email', '')
        //                       ->orderBy('employee_no', 'ASC')->get();

        $employees = Employee::where('email', NULL)->orwhere('email', '')->orderBy('employee_no', 'ASC')->get();

        $pemployees = PermanentEmployee::where('email', NULL)->where('completion_status', 0)->orderBy('id', 'ASC')->get();
        $cemployees = ContractEmployee::where('email', NULL)->where('completion_status', 0)->orderBy('id', 'ASC')->get();
        $temployees = TemporyEmployee::where('email', NULL)->where('completion_status', 0)->orderBy('id', 'ASC')->get();

        Log::info('EmployeeController -> employee email add ended');
        return view('admin.employee.email_add', compact('employees', 'pemployees', 'cemployees', 'temployees'));
    }

    public function emailStore(Request $request)
    {

        Log::info('EmployeeController -> employee email store started');

        $validatedData = $request->validate([
            'employee_no' => 'required',
            'email' => 'required|email:rfc,dns|ends_with:sjp.ac.lk',
        ], [
            'employee_no.required' => 'select relavent employee to make user account',
            'email.required' => 'please enter the valid university email',
            'email.ends_with' => 'accept personal university email only',
        ]);


        //dd($request->email);

        if (Employee::where('employee_no', $request->employee_no)->exists()) {

            $data = Employee::find($request->employee_no);
            $data->email = $request->email;
            $data->save();
        } else if (PermanentEmployee::where('id', $request->employee_no)->exists()) {

            $data = PermanentEmployee::find($request->employee_no);
            $data->email = $request->email;
            $data->save();
        } else if (ContractEmployee::where('id', $request->employee_no)->exists()) {

            $data = ContractEmployee::find($request->employee_no);
            $data->email = $request->email;
            $data->save();
        } else if (TemporyEmployee::where('id', $request->employee_no)->exists()) {

            $data = TemporyEmployee::find($request->employee_no);
            $data->email = $request->email;
            $data->save();
        }



        $notification = array(
            'message' => $request->employee_no . ' Email Set Up Successfully',
            'alert-type' => 'success'
        );
        Log::notice('EmployeeController -> email store employee number - ' . $request->employee_no . ' created by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> employee email store  ended');

        return redirect()->route('email.create')->with($notification);
    }

    public function addressView()
    {

        Log::info('EmployeeController -> employee address view get started');

        $employees = Employee::orderByDesc('updated_at')->get();
        $cities = City::all();

        Log::info('EmployeeController -> employee address view ended');
        return view('admin.employee.address_view', compact('employees', 'cities'));
    }

    public function bulkDataUploadView()
    {

        Log::info('EmployeeController -> employee bulk data upload get started');

        return view('admin.employee.bulk_employee');

        Log::info('EmployeeController -> employee bulk data upload ended');
    }

    public function import(Request $request)
    {

        if ($request->hasFile('file')) {

            $request->validate([
                'file' => 'required|mimes:xlsx,xlx,csv',
            ]);

            try {
                Excel::import(new LoginTempEmployeeImport, $request->file('file'));

                $notification = array(
                    'message' => 'employee data Uploaded Successfully',
                    'alert-type' => 'success'
                );

                return redirect()->route('employee.bulk.index')->with($notification);
            } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
                $failures = $e->failures();
                return view('admin.employee.bulk_employee', compact('failures'));
            }
        }


        $notification = array(
            'message' => 'please select excel file',
            'alert-type' => 'error'
        );

        return redirect()->route('employee.bulk.index')->with($notification);
    }

    public function lock($id)
    {

        Log::info('EmployeeController -> employee profile lock started');
        $empNo = decrypt($id);
        $employee = Employee::find($empNo);
        $employee->lock = 1;
        $employee->save();

        $data = new EmployeeLockHistory();
        $data->emp_no =  $empNo;
        $data->user_id = auth()->user()->employee_no;
        $data->date = date("Y-m-d");
        $data->created_at = Carbon::now();
        $data->lock_status = 1;
        $data->save();

        $notification = array(
            'message' => 'Employee Profile successfully Locked',
            'alert-type' => 'success'
        );

        Log::notice('EmployeeController -> employee profile lock employee number - ' . $empNo . ' locked by ' . auth()->user()->employee_no);
        Log::info('EmployeeController ->employee profile lock ended');

        return redirect()->back()->with($notification);
    } //end method

    public function unlock($id)
    {

        Log::info('EmployeeController -> employee profile unlock started');
        $empNo = decrypt($id);
        $employee = Employee::find($empNo);
        $employee->lock = 2;
        $employee->save();

        $data = new EmployeeLockHistory();
        $data->emp_no =  $empNo;
        $data->user_id = auth()->user()->employee_no;
        $data->date = date("Y-m-d");
        $data->created_at = Carbon::now();
        $data->lock_status = 2;
        $data->save();

        $notification = array(
            'message' => 'Employee Profile successfully Unlocked',
            'alert-type' => 'success'
        );

        Log::critical('EmployeeController -> employee profile Unlock employee number - ' . $empNo . ' unlocked by ' . auth()->user()->employee_no);
        Log::info('EmployeeController -> employee profile lock ended');


        return redirect()->back()->with($notification);
    } //end method

    public function lockLiisView()
    {

        Log::info('EmployeeController -> employee lock list view get started');

        $employees = Employee::orderByDesc('updated_at')->whereIn('lock', array(1, 2))->get();
        //$cities = City::all();

        Log::info('EmployeeController -> employee address view ended');
        return view('admin.employee.lock_view', compact('employees'));
    }

    public function lockHistoryShow($id)
    {

        Log::info('EmployeeController -> employee lock history view get started');

        $empNo = decrypt($id);
        $employeeLockHistory = EmployeeLockHistory::where('emp_no', $empNo)->orderByDesc('updated_at')->get();

        Log::info('EmployeeController -> employee lock history view get started');

        return view('admin.employee.lock_history_view', compact('employeeLockHistory'));
    }

    public function filterView()
    {

        //Log::info('EmployeeController -> employee filter view get started');

        return view('admin.employee.filter_view');
    }

    public function opratorFilterView()
    {

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                ->whereIn('roles.id', [5, 6])
                ->distinct()
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->whereIn('roles.id', [5, 6])
                    ->where('users.main_branch_id', 52)
                    ->distinct()
                    ->get();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->whereIn('roles.id', [5, 6])
                    ->where('users.main_branch_id', 53)
                    ->distinct()
                    ->get();
            }
        }

        $employees = array();

        $currentOperator = "";
        $ActiveStatus = "";
        $fullName = "";
        $branchName = "";
        $email = "";
        $mobile = "";
        $totalCount = 0;
        $ActiveCount = 0;
        $InactiveCount = 0;

        return view('admin.employee.filter.operator_filter_view', compact('operatorNames', 'employees', 'currentOperator', 'ActiveStatus', 'fullName', 'branchName', 'email', 'mobile', 'totalCount', 'ActiveCount', 'InactiveCount'));
    }

    public function operatorSearch(Request $request)
    {

        if ($request->operator_id != NULL && $request->employee_status_id != NULL) {

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->whereIn('roles.id', [5, 6])
                        ->where('users.main_branch_id', 52)
                        ->distinct()
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->whereIn('roles.id', [5, 6])
                        ->where('users.main_branch_id', 53)
                        ->distinct()
                        ->get();
                }
            }

            $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)->where('assign_ma_user_id', $request->operator_id)->get();

            $currentOperator = $request->operator_id;
            $ActiveStatus = $request->employee_status_id;

            //get oprator name
            $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                ->join('users', 'users.employee_no', '=', 'employees.employee_no')
                ->join('categories as main_branch', 'main_branch.id', '=', 'users.main_branch_id')
                ->where('employees.employee_no', '=', $currentOperator)
                ->select('title.category_name as title_name', 'employees.initials', 'employees.last_name', 'main_branch.category_name as main_branch_name', 'employees.mobile_no', 'employees.email')
                ->first();


            $fullName = $empData->title_name . ' ' . $empData->initials . ' ' . $empData->last_name;
            $branchName = $empData->main_branch_name;
            $email = $empData->email;
            $mobile = $empData->mobile_no;

            $totalCount = Employee::orderBy('employee_no')->where('assign_ma_user_id', $request->operator_id)->count();

            $ActiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 110)->where('assign_ma_user_id', $request->operator_id)->count();

            $InactiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 111)->where('assign_ma_user_id', $request->operator_id)->count();

            return view('admin.employee.filter.operator_filter_view', compact('operatorNames', 'employees', 'currentOperator', 'ActiveStatus', 'fullName', 'branchName', 'email', 'mobile', 'totalCount', 'ActiveCount', 'InactiveCount'));
        } elseif ($request->operator_id != NULL && $request->employee_status_id == NULL) {


            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->whereIn('roles.id', [5, 6])
                        ->where('users.main_branch_id', 52)
                        ->distinct()
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->whereIn('roles.id', [5, 6])
                        ->where('users.main_branch_id', 53)
                        ->distinct()
                        ->get();
                }
            }

            $employees = Employee::orderBy('employee_no')->where('assign_ma_user_id', $request->operator_id)->get();

            $currentOperator = $request->operator_id;
            $ActiveStatus = "";

            //get oprator name
            $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                ->join('users', 'users.employee_no', '=', 'employees.employee_no')
                ->join('categories as main_branch', 'main_branch.id', '=', 'users.main_branch_id')
                ->where('employees.employee_no', '=', $currentOperator)
                ->select('title.category_name as title_name', 'employees.initials', 'employees.last_name', 'main_branch.category_name as main_branch_name', 'employees.mobile_no', 'employees.email')
                ->first();


            $fullName = $empData->title_name . ' ' . $empData->initials . ' ' . $empData->last_name;
            $branchName = $empData->main_branch_name;
            $email = $empData->email;
            $mobile = $empData->mobile_no;

            $totalCount = Employee::orderBy('employee_no')->where('assign_ma_user_id', $request->operator_id)->count();

            $ActiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 110)->where('assign_ma_user_id', $request->operator_id)->count();

            $InactiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 111)->where('assign_ma_user_id', $request->operator_id)->count();

            return view('admin.employee.filter.operator_filter_view', compact('operatorNames', 'employees', 'currentOperator', 'ActiveStatus', 'fullName', 'branchName', 'email', 'mobile', 'totalCount', 'ActiveCount', 'InactiveCount'));
        } elseif ($request->operator_id == NULL && $request->employee_status_id != NULL) {

            $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)->get();

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->whereIn('roles.id', [5, 6])
                        ->where('users.main_branch_id', 52)
                        ->distinct()
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->whereIn('roles.id', [5, 6])
                        ->where('users.main_branch_id', 53)
                        ->distinct()
                        ->get();
                }
            }

            $currentOperator = "";
            $ActiveStatus = $request->employee_status_id;
            $fullName = "";
            $branchName = "";
            $email = "";
            $mobile = "";
            $totalCount = 0;
            $ActiveCount = 0;
            $InactiveCount = 0;

            return view('admin.employee.filter.operator_filter_view', compact('operatorNames', 'employees', 'currentOperator', 'ActiveStatus', 'fullName', 'branchName', 'email', 'mobile', 'totalCount', 'ActiveCount', 'InactiveCount'));
        } else {

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->whereIn('roles.id', [5, 6])
                        ->where('users.main_branch_id', 52)
                        ->distinct()
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $operatorNames = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->whereIn('roles.id', [5, 6])
                        ->where('users.main_branch_id', 53)
                        ->distinct()
                        ->get();
                }
            }

            $employees = array();

            $currentOperator = "";
            $ActiveStatus = "";
            $fullName = "";
            $branchName = "";
            $email = "";
            $mobile = "";
            $totalCount = 0;
            $ActiveCount = 0;
            $InactiveCount = 0;

            return view('admin.employee.filter.operator_filter_view', compact('operatorNames', 'employees', 'currentOperator', 'ActiveStatus', 'fullName', 'branchName', 'email', 'mobile', 'totalCount', 'ActiveCount', 'InactiveCount'));
        }
    }

    public function designationFilterView()
    {

        $employees = array();
        $designationMainGroups = DesignationMainGroup::all();
        $currentMainGroup = "";
        $currentDesignation = "";
        $designations = array();
        $fullDesignation = "";
        $mainBranch = "";
        $mis = "";
        $finance = "";
        $maingroup = "";
        $subgroup = "";
        $designationBranch = "";
        $researchStatus = "";
        $salaryScale = "";

        return view('admin.employee.filter.designation_filter_view', compact('designationMainGroups', 'employees', 'currentMainGroup', 'currentDesignation', 'designations', 'fullDesignation', 'mainBranch', 'mis', 'finance', 'maingroup', 'subgroup', 'designationBranch', 'researchStatus', 'salaryScale'));
    }

    public function designationSearch(Request $request)
    {

        if ($request->main_designation_id != NULL && $request->designation_id != NULL) {

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $employees = Employee::orderBy('employee_no')->where('designation_id', $request->designation_id)->get();
            } elseif ($mainBranch == 52) {

                $employees = Employee::orderBy('employee_no')->where('designation_id', $request->designation_id)->where('main_branch_id', 52)->get();
            } elseif ($mainBranch == 53) {
                # code...
                $employees = Employee::orderBy('employee_no')->where('designation_id', $request->designation_id)->where('main_branch_id', 53)->get();
            }

            $designationMainGroups = DesignationMainGroup::all();
            $currentMainGroup = $request->main_designation_id;
            $currentDesignation = $request->designation_id;
            $designations = Designation::where('designation_main_id', $request->main_designation_id)->get();

            //get designation full name
            $desigData = Designation::join('categories as staff_grade', 'staff_grade.id', '=', 'designations.staff_grade')
                ->join('categories as main_group', 'main_group.id', '=', 'designations.main_group')
                ->join('categories as ugc_mis', 'ugc_mis.id', '=', 'designations.ugc_mis')
                ->join('categories as ugc_finance', 'ugc_finance.id', '=', 'designations.ugc_finance')
                ->join('designation_main_groups', 'designation_main_groups.id', '=', 'designations.designation_main_id')
                ->join('designation_sub_groups', 'designation_sub_groups.id', '=', 'designations.designation_sub_id')
                //->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
                ->where('designations.id', '=', $currentDesignation)
                ->select(
                    'staff_grade.category_name as staff_grade_name',
                    'designation_name',
                    'salary_code',
                    'main_group.category_name as main_group_name',
                    'ugc_mis.category_name as ugc_mis_name',
                    'ugc_finance.category_name as ugc_finance_name',
                    'designation_main_groups.name as designation_main_group_name',
                    'designation_sub_groups.name as designation_sub_group_name',
                    'research_allowance_status',
                    'designation_division',
                    'salary_scale'
                )
                ->first();



            $fullDesignation = $desigData->designation_name . ' ( ' . $desigData->salary_code . ' ) ' . $desigData->staff_grade_name;
            $mainBranch = $desigData->main_group_name;
            $mis = $desigData->ugc_mis_name;
            $finance = $desigData->ugc_finance_name;
            $maingroup = $desigData->designation_main_group_name;
            $subgroup = $desigData->designation_sub_group_name;
            $designationBranch = $desigData->designation_sub_group_name == 52 ? 'Academic' : 'NonAcademic';
            $researchStatus = $desigData->research_allowance_status == 1 ? 'Yes' : 'No';
            $salaryScale = $desigData->salary_scale;
            //dd($designationBranch);
            return view('admin.employee.filter.designation_filter_view', compact('designationMainGroups', 'employees', 'currentMainGroup', 'currentDesignation', 'designations', 'fullDesignation', 'mainBranch', 'mis', 'finance', 'maingroup', 'subgroup', 'designationBranch', 'researchStatus', 'salaryScale'));
        } else {

            $employees = array();
            $designationMainGroups = DesignationMainGroup::all();
            $currentMainGroup = "";
            $currentDesignation = "";
            $designations = array();
            $fullDesignation = "";
            $mainBranch = "";
            $mis = "";
            $finance = "";
            $maingroup = "";
            $subgroup = "";
            $designationBranch = "";
            $researchStatus = "";
            $salaryScale = "";

            return view('admin.employee.filter.designation_filter_view', compact('designationMainGroups', 'employees', 'currentMainGroup', 'currentDesignation', 'designations', 'fullDesignation', 'mainBranch', 'mis', 'finance', 'maingroup', 'subgroup', 'designationBranch', 'researchStatus', 'salaryScale'));
        }
    }

    public function designationSubGroupFilterView()
    {

        $employees = array();
        $designationMainGroups = DesignationMainGroup::all();
        $currentMainGroup = "";
        $currentSubGroup = "";
        $subDesignations =  array();
        $subgroup = "";


        return view('admin.employee.filter.designation_sub_group_filter_view', compact('designationMainGroups', 'employees', 'currentMainGroup', 'currentSubGroup', 'subDesignations', 'subgroup'));
    }

    public function designationSubGroupSearch(Request $request)
    {


        if ($request->sub_designation_id != NULL && $request->sub_designation_id != NULL) {

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('designation_sub_groups', 'designation_sub_groups.id', '=', 'designations.designation_sub_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->where('designation_sub_groups.id', '=', $request->sub_designation_id)
                    ->select('employees.*', 'departments.department_name', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            } elseif ($mainBranch == 52) {

                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('designation_sub_groups', 'designation_sub_groups.id', '=', 'designations.designation_sub_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->where('designation_sub_groups.id', '=', $request->sub_designation_id)
                    ->where('employees.main_branch_id', 52)
                    ->select('employees.*', 'departments.department_name', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            } elseif ($mainBranch == 53) {
                # code...
                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('designation_sub_groups', 'designation_sub_groups.id', '=', 'designations.designation_sub_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->where('designation_sub_groups.id', '=', $request->sub_designation_id)
                    ->where('employees.main_branch_id', 53)
                    ->select('employees.*', 'departments.department_name', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            }


            //dd($employees);

            $designationMainGroups = DesignationMainGroup::all();
            $currentMainGroup = $request->main_designation_id;
            $currentSubGroup = $request->sub_designation_id;
            $subDesignations = DesignationSubGroup::where('designation_main_group_id', $request->main_designation_id)->get();

            //get sub group
            $subgroup = Designation::join('designation_sub_groups', 'designation_sub_groups.id', '=', 'designations.designation_sub_id')
                ->where('designation_sub_groups.id', '=', $request->sub_designation_id)
                ->select('name as value')
                ->get();

            $subgroup = json_decode($subgroup, true);
            $subgroup = $subgroup[0]["value"];


            return view('admin.employee.filter.designation_sub_group_filter_view', compact('designationMainGroups', 'employees', 'currentMainGroup', 'currentSubGroup', 'subDesignations', 'subgroup'));
        } else {

            $employees = array();
            $designationMainGroups = DesignationMainGroup::all();
            $currentMainGroup = "";
            $currentSubGroup = "";
            $subDesignations =  array();
            $subgroup = "";


            return view('admin.employee.filter.designation_sub_group_filter_view', compact('designationMainGroups', 'employees', 'currentMainGroup', 'currentSubGroup', 'subDesignations', 'subgroup'));
        }
    }

    public function designationMainGroupFilterView()
    {

        $employees = array();
        $designationMainGroups = DesignationMainGroup::all();
        $currentMainGroup = "";
        $maingroup = "";


        return view('admin.employee.filter.designation_main_group_filter_view', compact('designationMainGroups', 'employees', 'currentMainGroup', 'maingroup'));
    }

    public function designationMainGroupSearch(Request $request)
    {


        if ($request->main_designation_id != NULL) {

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('designation_main_groups', 'designation_main_groups.id', '=', 'designations.designation_main_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->where('designation_main_groups.id', '=', $request->main_designation_id)
                    ->select('employees.*', 'departments.department_name', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            } elseif ($mainBranch == 52) {

                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('designation_main_groups', 'designation_main_groups.id', '=', 'designations.designation_main_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->where('designation_main_groups.id', '=', $request->main_designation_id)
                    ->where('employees.main_branch_id', 52)
                    ->select('employees.*', 'departments.department_name', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            } elseif ($mainBranch == 53) {
                # code...
                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('designation_main_groups', 'designation_main_groups.id', '=', 'designations.designation_main_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->where('designation_main_groups.id', '=', $request->main_designation_id)
                    ->where('employees.main_branch_id', 53)
                    ->select('employees.*', 'departments.department_name', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            }



            $designationMainGroups = DesignationMainGroup::all();
            $currentMainGroup = $request->main_designation_id;


            //get sub group
            $maingroup = Designation::join('designation_main_groups', 'designation_main_groups.id', '=', 'designations.designation_main_id')
                ->where('designation_main_groups.id', '=', $request->main_designation_id)
                ->select('name as value')
                ->get();

            $maingroup = json_decode($maingroup, true);
            $maingroup = $maingroup[0]["value"];


            return view('admin.employee.filter.designation_main_group_filter_view', compact('designationMainGroups', 'employees', 'currentMainGroup', 'maingroup'));
        } else {

            $employees = array();
            $designationMainGroups = DesignationMainGroup::all();
            $currentMainGroup = "";
            $maingroup = "";


            return view('admin.employee.filter.designation_main_group_filter_view', compact('designationMainGroups', 'employees', 'currentMainGroup', 'maingroup'));
        }
    }

    public function facultyFilterView()
    {

        $employees = array();
        $faculties = Faculty::all();
        $department = array();
        $facultyDetials = array();
        $currentfaculty = "";
        $activeStatus = "";
        $totalCount = 0;
        $ActiveCount = 0;
        $InactiveCount = 0;

        //dd($activeStatus);

        return view('admin.employee.filter.faculty_filter_view', compact('faculties', 'employees', 'currentfaculty', 'activeStatus', 'totalCount', 'ActiveCount', 'InactiveCount', 'facultyDetials', 'department'));
    }

    public function facultySearch(Request $request)
    {

        if ($request->faculty_id != NULL && $request->employee_status_id != NULL) {

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                    ->where('faculty_id', $request->faculty_id)->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('faculty_id', $request->faculty_id)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('faculty_id', $request->faculty_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('faculty_id', $request->faculty_id)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('faculty_id', $request->faculty_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                }
            }


            $currentfaculty = $request->faculty_id;
            $activeStatus = $request->employee_status_id;
            $faculties = Faculty::all();
            $department = Department::where('faculty_code', $request->faculty_id)->get();

            $facultyDetials = Faculty::join('faculty_deans', 'faculty_deans.faculty_id', '=', 'faculties.id')
                ->join('employees', 'employees.employee_no', '=', 'faculty_deans.emp_no')
                ->where('faculties.id', '=', $request->faculty_id)
                ->select('employees.*', 'faculties.faculty_name', 'faculty_deans.appointmemt_type', 'faculty_deans.start_date', 'faculty_deans.end_date', 'faculty_deans.active_status')
                ->get();

            $totalCount = Employee::orderBy('employee_no')->where('faculty_id', $request->faculty_id)->count();

            $ActiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 110)->where('faculty_id', $request->faculty_id)->count();

            $InactiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 111)->where('faculty_id', $request->faculty_id)->count();

            return view('admin.employee.filter.faculty_filter_view', compact('faculties', 'employees', 'currentfaculty', 'activeStatus', 'totalCount', 'ActiveCount', 'InactiveCount', 'facultyDetials', 'department'));
        } elseif ($request->faculty_id != NULL && $request->employee_status_id == NULL) {

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees = Employee::orderBy('employee_no')->where('faculty_id', $request->faculty_id)->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')
                        ->where('faculty_id', $request->faculty_id)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')
                        ->where('faculty_id', $request->faculty_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')
                        ->where('faculty_id', $request->faculty_id)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')
                        ->where('faculty_id', $request->faculty_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                }
            }


            $currentfaculty = $request->faculty_id;
            $activeStatus = "";
            $faculties = Faculty::all();
            $department = Department::where('faculty_code', $request->faculty_id)->get();

            $facultyDetials = Faculty::join('faculty_deans', 'faculty_deans.faculty_id', '=', 'faculties.id')
                ->join('employees', 'employees.employee_no', '=', 'faculty_deans.emp_no')
                ->where('faculties.id', '=', $request->faculty_id)
                ->select('employees.*', 'faculties.faculty_name', 'faculty_deans.appointmemt_type', 'faculty_deans.start_date', 'faculty_deans.end_date', 'faculty_deans.active_status')
                ->get();

            $totalCount = Employee::orderBy('employee_no')->where('faculty_id', $request->faculty_id)->count();

            $ActiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 110)->where('faculty_id', $request->faculty_id)->count();

            $InactiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 111)->where('faculty_id', $request->faculty_id)->count();

            return view('admin.employee.filter.faculty_filter_view', compact('faculties', 'employees', 'currentfaculty', 'activeStatus', 'totalCount', 'ActiveCount', 'InactiveCount', 'facultyDetials', 'department'));
        } elseif ($request->faculty_id == NULL && $request->employee_status_id != NULL) {

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                }
            }

            $currentfaculty = "";
            $activeStatus = $request->employee_status_id;
            $faculties = Faculty::all();
            $department = array();
            $facultyDetials = array();
            $totalCount = 0;
            $ActiveCount = 0;
            $InactiveCount = 0;

            return view('admin.employee.filter.faculty_filter_view', compact('faculties', 'employees', 'currentfaculty', 'activeStatus', 'totalCount', 'ActiveCount', 'InactiveCount', 'facultyDetials', 'department'));
        } else {

            $employees = array();
            $faculties = Faculty::all();
            $department = array();
            $facultyDetials = array();
            $currentfaculty = "";
            $activeStatus = "";
            $totalCount = 0;
            $ActiveCount = 0;
            $InactiveCount = 0;

            return view('admin.employee.filter.faculty_filter_view', compact('faculties', 'employees', 'currentfaculty', 'activeStatus', 'totalCount', 'ActiveCount', 'InactiveCount', 'facultyDetials', 'department'));
        }
    }

    public function departmentFilterView()
    {

        $employees = array();
        $faculties = Faculty::all();
        $departments = array();
        $departmentDetials = array();
        $currentDepartment = "";
        $currentFaculty = "";
        $activeStatus = "";
        $totalCount = 0;
        $ActiveCount = 0;
        $InactiveCount = 0;

        return view('admin.employee.filter.department_filter_view', compact('faculties', 'employees', 'currentDepartment', 'activeStatus', 'totalCount', 'ActiveCount', 'InactiveCount', 'departmentDetials', 'currentFaculty', 'departments'));
    }

    public function departmentSearch(Request $request)
    {

        if ($request->department_id != NULL && $request->employee_status_id != NULL) {

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)->where('department_id', $request->department_id)->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)->where('department_id', $request->department_id)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)->where('department_id', $request->department_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)->where('department_id', $request->department_id)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)->where('department_id', $request->department_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                }
            }


            $currentDepartment = $request->department_id;
            $currentFaculty = $request->faculty_id;
            $activeStatus = $request->employee_status_id;
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', $request->faculty_id)->get();

            $departmentDetials = Department::join('department_heads', 'department_heads.department_id', '=', 'departments.id')
                ->join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
                ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
                ->where('departments.id', '=', $request->department_id)
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'employees.mobile_no', 'employees.email', 'departments.department_name', 'department_heads.appointmemt_type', 'department_heads.start_date', 'department_heads.end_date', 'department_heads.active_status', 'faculties.faculty_name')
                ->get();

            $totalCount = Employee::orderBy('employee_no')->where('department_id', $request->department_id)->count();

            $ActiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 110)->where('department_id', $request->department_id)->count();

            $InactiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 111)->where('department_id', $request->department_id)->count();

            return view('admin.employee.filter.department_filter_view', compact('faculties', 'employees', 'currentDepartment', 'activeStatus', 'totalCount', 'ActiveCount', 'InactiveCount', 'departmentDetials', 'currentFaculty', 'departments'));
        } elseif ($request->department_id != NULL && $request->employee_status_id == NULL) {

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees = Employee::orderBy('employee_no')->where('department_id', $request->department_id)->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('department_id', $request->department_id)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('department_id', $request->department_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('department_id', $request->department_id)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('department_id', $request->department_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                }
            }

            $currentDepartment = $request->department_id;
            $currentFaculty = $request->faculty_id;
            $activeStatus = "";
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', $request->faculty_id)->get();

            $departmentDetials = Department::join('department_heads', 'department_heads.department_id', '=', 'departments.id')
                ->join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
                ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
                ->where('departments.id', '=', $request->department_id)
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'employees.mobile_no', 'employees.email', 'departments.department_name', 'department_heads.appointmemt_type', 'department_heads.start_date', 'department_heads.end_date', 'department_heads.active_status', 'faculties.faculty_name')
                ->get();

            $totalCount = Employee::orderBy('employee_no')->where('department_id', $request->department_id)->count();

            $ActiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 110)->where('department_id', $request->department_id)->count();

            $InactiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 111)->where('department_id', $request->department_id)->count();

            return view('admin.employee.filter.department_filter_view', compact('faculties', 'employees', 'currentDepartment', 'activeStatus', 'totalCount', 'ActiveCount', 'InactiveCount', 'departmentDetials', 'currentFaculty', 'departments'));
        } elseif ($request->department_id == NULL && $request->employee_status_id != NULL) {

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 52)
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_status_id', $request->employee_status_id)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                }
            }


            $faculties = Faculty::all();
            $departments =  array();
            $departmentDetials = array();
            $currentFaculty = "";
            $currentDepartment = "";
            $activeStatus = "";
            $totalCount = 0;
            $ActiveCount = 0;
            $InactiveCount = 0;

            return view('admin.employee.filter.department_filter_view', compact('faculties', 'employees', 'currentDepartment', 'activeStatus', 'totalCount', 'ActiveCount', 'InactiveCount', 'departmentDetials', 'currentFaculty', 'departments'));
        } else {

            $employees = array();
            $faculties = Faculty::all();
            $departments = array();
            $departmentDetials = array();
            $currentFaculty = "";
            $currentDepartment = "";
            $activeStatus = "";
            $totalCount = 0;
            $ActiveCount = 0;
            $InactiveCount = 0;

            return view('admin.employee.filter.department_filter_view', compact('faculties', 'employees', 'currentDepartment', 'activeStatus', 'totalCount', 'ActiveCount', 'InactiveCount', 'departmentDetials', 'currentFaculty', 'departments'));
        }
    }

    public function mainBranchFilterView()
    {
        $categories = $this->getCategories([23]);
        $employees = array();
        $employeeGroups = array();
        $employeeWorkingTypes = $categories->where('category_type_id', '23');
        $currentMainBranch = "";
        $currentEmployeeGroup = "";
        $currentEmployeeWorkingType = "";
        $activeStatus = "";

        return view('admin.employee.filter.main_branch_filter_view', compact('employees', 'employeeGroups', 'employeeWorkingTypes', 'currentMainBranch', 'currentEmployeeGroup', 'currentEmployeeWorkingType', 'activeStatus'));
    }

    public function mainBranchSearch(Request $request)
    {

        if ($request->main_branch_id != NULL && $request->employee_group != NULL && $request->employee_work_type != NULL && $request->employee_status_id != NULL) {

            $categories = $this->getCategories([19, 23]);
            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_group', 'emp_group.id', '=', 'designations.ugc_mis')
                ->join('categories as emp_work_type', 'emp_work_type.id', '=', 'employees.employee_work_type')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('employees.main_branch_id', $request->main_branch_id)
                ->where('employees.employee_status_id', $request->employee_status_id)
                ->where('emp_group.id', $request->employee_group)
                ->where('emp_work_type.id', $request->employee_work_type)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $employeeGroups = $categories->where('category_type_id', '19')->where('category_code', $request->main_branch_id);
            $employeeWorkingTypes = $categories->where('category_type_id', '23');
            $currentMainBranch = $request->main_branch_id;
            $currentEmployeeGroup = $request->employee_group;
            $currentEmployeeWorkingType = $request->employee_work_type;
            $activeStatus = $request->employee_status_id;


            return view('admin.employee.filter.main_branch_filter_view', compact('employees', 'employeeGroups', 'employeeWorkingTypes', 'currentMainBranch', 'currentEmployeeGroup', 'currentEmployeeWorkingType', 'activeStatus'));
        } else if ($request->main_branch_id != NULL && $request->employee_group != NULL && $request->employee_work_type != NULL && $request->employee_status_id == NULL) {

            $categories = $this->getCategories([19, 23]);
            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_group', 'emp_group.id', '=', 'designations.ugc_mis')
                ->join('categories as emp_work_type', 'emp_work_type.id', '=', 'employees.employee_work_type')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('employees.main_branch_id', $request->main_branch_id)
                ->where('emp_group.id', $request->employee_group)
                ->where('emp_work_type.id', $request->employee_work_type)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $employeeGroups = $categories->where('category_type_id', '19')->where('category_code', $request->main_branch_id);
            $employeeWorkingTypes = $categories->where('category_type_id', '23');
            $currentMainBranch = $request->main_branch_id;
            $currentEmployeeGroup = $request->employee_group;
            $currentEmployeeWorkingType = $request->employee_work_type;
            $activeStatus = "";

            return view('admin.employee.filter.main_branch_filter_view', compact('employees', 'employeeGroups', 'employeeWorkingTypes', 'currentMainBranch', 'currentEmployeeGroup', 'currentEmployeeWorkingType', 'activeStatus'));
        } else if ($request->main_branch_id != NULL && $request->employee_group != NULL && $request->employee_work_type == NULL && $request->employee_status_id == NULL) {

            $categories = $this->getCategories([19, 23]);
            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_group', 'emp_group.id', '=', 'designations.ugc_mis')
                ->join('categories as emp_work_type', 'emp_work_type.id', '=', 'employees.employee_work_type')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('employees.main_branch_id', $request->main_branch_id)
                ->where('emp_group.id', $request->employee_group)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $employeeGroups = $categories->where('category_type_id', '19')->where('category_code', $request->main_branch_id);
            $employeeWorkingTypes = $categories->where('category_type_id', '23');
            $currentMainBranch = $request->main_branch_id;
            $currentEmployeeGroup = $request->employee_group;
            $currentEmployeeWorkingType = "";
            $activeStatus = "";

            return view('admin.employee.filter.main_branch_filter_view', compact('employees', 'employeeGroups', 'employeeWorkingTypes', 'currentMainBranch', 'currentEmployeeGroup', 'currentEmployeeWorkingType', 'activeStatus'));
        } else if ($request->main_branch_id != NULL && $request->employee_group == NULL && $request->employee_work_type == NULL && $request->employee_status_id == NULL) {

            $categories = $this->getCategories([19, 23]);
            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_group', 'emp_group.id', '=', 'designations.ugc_mis')
                ->join('categories as emp_work_type', 'emp_work_type.id', '=', 'employees.employee_work_type')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('employees.main_branch_id', $request->main_branch_id)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $employeeGroups = $categories->where('category_type_id', '19')->where('category_code', $request->main_branch_id);
            $employeeWorkingTypes = $categories->where('category_type_id', '23');
            $currentMainBranch = $request->main_branch_id;
            $currentEmployeeGroup = "";
            $currentEmployeeWorkingType = "";
            $activeStatus = "";

            return view('admin.employee.filter.main_branch_filter_view', compact('employees', 'employeeGroups', 'employeeWorkingTypes', 'currentMainBranch', 'currentEmployeeGroup', 'currentEmployeeWorkingType', 'activeStatus'));
        } else if ($request->main_branch_id != NULL && $request->employee_group == NULL && $request->employee_work_type == NULL && $request->employee_status_id != NULL) {

            $categories = $this->getCategories([19, 23]);
            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_group', 'emp_group.id', '=', 'designations.ugc_mis')
                ->join('categories as emp_work_type', 'emp_work_type.id', '=', 'employees.employee_work_type')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('employees.main_branch_id', $request->main_branch_id)
                ->where('employees.employee_status_id', $request->employee_status_id)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $employeeGroups = $categories->where('category_type_id', '19')->where('category_code', $request->main_branch_id);
            $employeeWorkingTypes = $categories->where('category_type_id', '23');
            $currentMainBranch = $request->main_branch_id;
            $currentEmployeeGroup = "";
            $currentEmployeeWorkingType = "";
            $activeStatus = $request->employee_status_id;

            return view('admin.employee.filter.main_branch_filter_view', compact('employees', 'employeeGroups', 'employeeWorkingTypes', 'currentMainBranch', 'currentEmployeeGroup', 'currentEmployeeWorkingType', 'activeStatus'));
        } else if ($request->main_branch_id != NULL && $request->employee_group == NULL && $request->employee_work_type != NULL && $request->employee_status_id != NULL) {

            $categories = $this->getCategories([19, 23]);
            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_group', 'emp_group.id', '=', 'designations.ugc_mis')
                ->join('categories as emp_work_type', 'emp_work_type.id', '=', 'employees.employee_work_type')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('employees.main_branch_id', $request->main_branch_id)
                ->where('employees.employee_status_id', $request->employee_status_id)
                ->where('emp_work_type.id', $request->employee_work_type)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $employeeGroups = $categories->where('category_type_id', '19')->where('category_code', $request->main_branch_id);
            $employeeWorkingTypes = $categories->where('category_type_id', '23');
            $currentMainBranch = $request->main_branch_id;
            $currentEmployeeGroup = "";
            $currentEmployeeWorkingType = $request->employee_work_type;
            $activeStatus = $request->employee_status_id;

            return view('admin.employee.filter.main_branch_filter_view', compact('employees', 'employeeGroups', 'employeeWorkingTypes', 'currentMainBranch', 'currentEmployeeGroup', 'currentEmployeeWorkingType', 'activeStatus'));
        } else if ($request->main_branch_id != NULL && $request->employee_group != NULL && $request->employee_work_type == NULL && $request->employee_status_id != NULL) {

            $categories = $this->getCategories([19, 23]);
            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_group', 'emp_group.id', '=', 'designations.ugc_mis')
                ->join('categories as emp_work_type', 'emp_work_type.id', '=', 'employees.employee_work_type')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('employees.main_branch_id', $request->main_branch_id)
                ->where('employees.employee_status_id', $request->employee_status_id)
                ->where('emp_group.id', $request->employee_group)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $employeeGroups = $categories->where('category_type_id', '19')->where('category_code', $request->main_branch_id);
            $employeeWorkingTypes = $categories->where('category_type_id', '23');
            $currentMainBranch = $request->main_branch_id;
            $currentEmployeeGroup = $request->employee_group;
            $currentEmployeeWorkingType = "";
            $activeStatus = $request->employee_status_id;

            return view('admin.employee.filter.main_branch_filter_view', compact('employees', 'employeeGroups', 'employeeWorkingTypes', 'currentMainBranch', 'currentEmployeeGroup', 'currentEmployeeWorkingType', 'activeStatus'));
        } else if ($request->main_branch_id != NULL && $request->employee_group == NULL && $request->employee_work_type != NULL && $request->employee_status_id == NULL) {

            $categories = $this->getCategories([19, 23]);
            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_group', 'emp_group.id', '=', 'designations.ugc_mis')
                ->join('categories as emp_work_type', 'emp_work_type.id', '=', 'employees.employee_work_type')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('employees.main_branch_id', $request->main_branch_id)
                ->where('emp_work_type.id', $request->employee_work_type)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $employeeGroups = $categories->where('category_type_id', '19')->where('category_code', $request->main_branch_id);
            $employeeWorkingTypes = $categories->where('category_type_id', '23');
            $currentMainBranch = $request->main_branch_id;
            $currentEmployeeGroup = "";
            $currentEmployeeWorkingType = $request->employee_work_type;
            $activeStatus = "";

            return view('admin.employee.filter.main_branch_filter_view', compact('employees', 'employeeGroups', 'employeeWorkingTypes', 'currentMainBranch', 'currentEmployeeGroup', 'currentEmployeeWorkingType', 'activeStatus'));
        } else if ($request->main_branch_id == NULL && $request->employee_group == NULL && $request->employee_work_type != NULL && $request->employee_status_id == NULL) {

            $categories = $this->getCategories([19, 23]);
            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_group', 'emp_group.id', '=', 'designations.ugc_mis')
                ->join('categories as emp_work_type', 'emp_work_type.id', '=', 'employees.employee_work_type')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('emp_work_type.id', $request->employee_work_type)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $employeeGroups = $categories->where('category_type_id', '19')->where('category_code', $request->main_branch_id);
            $employeeWorkingTypes = $categories->where('category_type_id', '23');
            $currentMainBranch = "";
            $currentEmployeeGroup = "";
            $currentEmployeeWorkingType = $request->employee_work_type;
            $activeStatus = "";

            return view('admin.employee.filter.main_branch_filter_view', compact('employees', 'employeeGroups', 'employeeWorkingTypes', 'currentMainBranch', 'currentEmployeeGroup', 'currentEmployeeWorkingType', 'activeStatus'));
        } else {

            $categories = $this->getCategories([19, 23]);
            $employees = array();
            $employeeGroups = array();
            $employeeWorkingTypes = $categories->where('category_type_id', '23');
            $currentMainBranch = "";
            $currentEmployeeGroup = "";
            $currentEmployeeWorkingType = "";
            $activeStatus = "";


            return view('admin.employee.filter.main_branch_filter_view', compact('employees', 'employeeGroups', 'employeeWorkingTypes', 'currentMainBranch', 'currentEmployeeGroup', 'currentEmployeeWorkingType', 'activeStatus'));
        }
    }

    public function mainGroupFilterView()
    {
        $categories = $this->getCategories([18, 20]);
        $employees = array();
        $mainGroup = $categories->where('category_type_id', '18');
        $ugcFinance = $categories->where('category_type_id', '20');
        $currentMainGroup = "";
        $currentUgcFinace = "";

        return view('admin.employee.filter.main_group_filter_view', compact('employees', 'mainGroup', 'ugcFinance', 'currentMainGroup', 'currentUgcFinace'));
    }

    public function mainGroupSearch(Request $request)
    {

        if ($request->main_group != NULL && $request->ugc_finance != NULL) {

            $categories = $this->getCategories([18, 20]);

            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_main_group', 'emp_main_group.id', '=', 'designations.main_group')
                ->join('categories as emp_ugc_finance', 'emp_ugc_finance.id', '=', 'designations.ugc_finance')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('emp_main_group.id', $request->main_group)
                ->where('emp_ugc_finance.id', $request->ugc_finance)
                ->where('employees.employee_status_id', 110)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $mainGroup = $categories->where('category_type_id', '18');
            $ugcFinance = $categories->where('category_type_id', '20');
            $currentMainGroup = $request->main_group;
            $currentUgcFinace = $request->ugc_finance;

            return view('admin.employee.filter.main_group_filter_view', compact('employees', 'mainGroup', 'ugcFinance', 'currentMainGroup', 'currentUgcFinace'));
        } elseif ($request->main_group != NULL && $request->ugc_finance == NULL) {

            $categories = $this->getCategories([18, 20]);

            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_main_group', 'emp_main_group.id', '=', 'designations.main_group')
                ->join('categories as emp_ugc_finance', 'emp_ugc_finance.id', '=', 'designations.ugc_finance')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('emp_main_group.id', $request->main_group)
                ->where('employees.employee_status_id', 110)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $mainGroup = $categories->where('category_type_id', '18');
            $ugcFinance = $categories->where('category_type_id', '20');
            $currentMainGroup = $request->main_group;
            $currentUgcFinace = $request->ugc_finance;

            return view('admin.employee.filter.main_group_filter_view', compact('employees', 'mainGroup', 'ugcFinance', 'currentMainGroup', 'currentUgcFinace'));
        } elseif ($request->main_group == NULL && $request->ugc_finance != NULL) {

            $categories = $this->getCategories([18, 20]);

            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories as emp_main_group', 'emp_main_group.id', '=', 'designations.main_group')
                ->join('categories as emp_ugc_finance', 'emp_ugc_finance.id', '=', 'designations.ugc_finance')
                ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                ->where('employees.employee_status_id', 110)
                ->where('emp_ugc_finance.id', $request->ugc_finance)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $mainGroup = $categories->where('category_type_id', '18');
            $ugcFinance = $categories->where('category_type_id', '20');
            $currentMainGroup = $request->main_group;
            $currentUgcFinace = $request->ugc_finance;

            return view('admin.employee.filter.main_group_filter_view', compact('employees', 'mainGroup', 'ugcFinance', 'currentMainGroup', 'currentUgcFinace'));
        } else {

            $categories = $this->getCategories([18, 20]);
            $employees = array();
            $mainGroup = $categories->where('category_type_id', '18');
            $ugcFinance = $categories->where('category_type_id', '20');
            $currentMainGroup = "";
            $currentUgcFinace = "";

            return view('admin.employee.filter.main_group_filter_view', compact('employees', 'mainGroup', 'ugcFinance', 'currentMainGroup', 'currentUgcFinace'));
        }
    }

    public function statusFilterView()
    {
        $categories = $this->getCategories([21, 22]);
        $employees = array();
        $status = $categories->where('category_type_id', '21');
        $statusType = array();
        $currentStatus = "";
        $currentStatusType = "";
        $currentStatusTypeName = "";


        return view('admin.employee.filter.status_filter_view', compact('employees', 'status', 'statusType', 'currentStatus', 'currentStatusType', 'currentStatusTypeName'));
    }

    public function statusSearch(Request $request)
    {

        if ($request->status != NULL && $request->status_type != NULL) {

            $categories = $this->getCategories([21, 22]);

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                    ->join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                    ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                    ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                    ->where('emp_status.id', $request->status)
                    ->where('emp_status_type.id', $request->status_type)
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('employees.main_branch_id', 52)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 52)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('employees.main_branch_id', 53)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 53)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            }

            $status = $categories->where('category_type_id', '21');
            $statusType = $categories->where('category_type_id', '22')->where('category_code', $request->status);
            $currentStatus = $request->status;
            $currentStatusType = $request->status_type;
            //get current status type name
            $currentStatusTypeData = Category::where('categories.id', '=', $request->status_type)
                ->select('category_name')
                ->first();

            $currentStatusTypeName = $currentStatusTypeData->category_name;

            return view('admin.employee.filter.status_filter_view', compact('employees', 'status', 'statusType', 'currentStatus', 'currentStatusType', 'currentStatusTypeName'));
        } elseif ($request->status != NULL && $request->status_type == NULL) {

            $categories = $this->getCategories([21, 22]);

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                    ->join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                    ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                    ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                    ->where('emp_status.id', $request->status)
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                        ->where('emp_status.id', $request->status)
                        ->where('employees.main_branch_id', 52)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                        ->where('emp_status.id', $request->status)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 52)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                        ->where('emp_status.id', $request->status)
                        ->where('employees.main_branch_id', 53)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
                        ->where('emp_status.id', $request->status)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 53)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            }

            $status = $categories->where('category_type_id', '21');
            $statusType = $categories->where('category_type_id', '22')->where('category_code', $request->status);
            $currentStatus = $request->status;
            $currentStatusType = "";
            $currentStatusTypeName = "";

            return view('admin.employee.filter.status_filter_view', compact('employees', 'status', 'statusType', 'currentStatus', 'currentStatusType', 'currentStatusTypeName'));
        } else {

            $categories = $this->getCategories([21, 22]);
            $employees = array();
            $status = $categories->where('category_type_id', '21');
            $statusType = array();
            $currentStatus = "";
            $currentStatusType = "";
            $currentStatusTypeName = "";

            return view('admin.employee.filter.status_filter_view', compact('employees', 'status', 'statusType', 'currentStatus', 'currentStatusType', 'currentStatusTypeName'));
        }
    }

    public function workingTypeFilterView()
    {
        $categories = $this->getCategories([21, 22, 23]);
        $employees = array();
        $status = $categories->where('category_type_id', '21');
        $workingType = $categories->where('category_type_id', '23');
        $statusType = array();
        $currentStatus = "";
        $currentStatusType = "";
        $currentWorkingType = "";
        $currentStatusTypeName = "";
        $currentWorkingTypeName = "";

        return view('admin.employee.filter.working_type_filter_view', compact('employees', 'status', 'statusType', 'workingType', 'currentStatus', 'currentStatusType', 'currentWorkingType', 'currentStatusTypeName', 'currentWorkingTypeName'));
    }

    public function workingTypeSearch(Request $request)
    {

        if ($request->status != NULL && $request->status_type != NULL && $request->working_type != NULL) {

            $categories = $this->getCategories([21, 22, 23]);

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                    ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                    ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                    ->select('employees.*')
                    ->where('emp_status.id', $request->status)
                    ->where('emp_status_type.id', $request->status_type)
                    ->where('working_type.id', $request->working_type)
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 52)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 53)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            }

            $status = $categories->where('category_type_id', '21');
            $statusType = $categories->where('category_type_id', '22')->where('category_code', $request->status);
            $workingType = $categories->where('category_type_id', '23');
            $currentStatus = $request->status;
            $currentStatusType = $request->status_type;
            $currentWorkingType = $request->working_type;
            //get current status type name
            $currentStatusTypeData = Category::where('categories.id', '=', $request->status_type)
                ->select('category_name')
                ->first();

            $currentStatusTypeName = $currentStatusTypeData->category_name;

            //get current working type name
            $currentWorkingTypeName = Category::where('categories.id', '=', $request->working_type)
                ->select('category_name')
                ->first();

            $currentWorkingTypeName = $currentWorkingTypeName->category_name;

            return view('admin.employee.filter.working_type_filter_view', compact('employees', 'status', 'statusType', 'workingType', 'currentStatus', 'currentStatusType', 'currentWorkingType', 'currentStatusTypeName', 'currentWorkingTypeName'));
        } elseif ($request->status != NULL && $request->status_type == NULL && $request->working_type != NULL) {

            $categories = $this->getCategories([21, 22, 23]);

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                    ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                    ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                    ->select('employees.*')
                    ->where('emp_status.id', $request->status)
                    ->where('working_type.id', $request->working_type)
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 52)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 53)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            }

            $status = $categories->where('category_type_id', '21');
            $statusType = $categories->where('category_type_id', '22')->where('category_code', $request->status);
            $workingType = $categories->where('category_type_id', '23');
            $currentStatus = $request->status;
            $currentStatusType = "";
            $currentWorkingType = $request->working_type;
            //get current status type name
            $currentStatusTypeName = "";

            //get current working type name
            $currentWorkingTypeName = Category::where('categories.id', '=', $request->working_type)
                ->select('category_name')
                ->first();

            $currentWorkingTypeName = $currentWorkingTypeName->category_name;

            return view('admin.employee.filter.working_type_filter_view', compact('employees', 'status', 'statusType', 'workingType', 'currentStatus', 'currentStatusType', 'currentWorkingType', 'currentStatusTypeName', 'currentWorkingTypeName'));
        } elseif ($request->status != NULL && $request->status_type != NULL && $request->working_type == NULL) {

            $categories = $this->getCategories([21, 22, 23]);

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                    ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                    ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                    ->select('employees.*')
                    ->where('emp_status.id', $request->status)
                    ->where('emp_status_type.id', $request->status_type)
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('employees.main_branch_id', 52)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('employees.main_branch_id', 53)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('emp_status_type.id', $request->status_type)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            }

            $status = $categories->where('category_type_id', '21');
            $statusType = $categories->where('category_type_id', '22')->where('category_code', $request->status);
            $workingType = $categories->where('category_type_id', '23');
            $currentStatus = $request->status;
            $currentStatusType = $request->status_type;
            $currentWorkingType = "";
            //get current status type name
            $currentStatusTypeData = Category::where('categories.id', '=', $request->status_type)
                ->select('category_name')
                ->first();

            $currentStatusTypeName = $currentStatusTypeData->category_name;

            //get current working type name
            $currentWorkingTypeName = "";

            return view('admin.employee.filter.working_type_filter_view', compact('employees', 'status', 'statusType', 'workingType', 'currentStatus', 'currentStatusType', 'currentWorkingType', 'currentStatusTypeName', 'currentWorkingTypeName'));
        } elseif ($request->status == NULL && $request->status_type == NULL && $request->working_type != NULL) {

            $categories = $this->getCategories([21, 22, 23]);

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                    ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                    ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                    ->select('employees.*')
                    ->where('working_type.id', $request->working_type)
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 52)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 53)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('working_type.id', $request->working_type)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            }

            $status = $categories->where('category_type_id', '21');
            $statusType = $categories->where('category_type_id', '22')->where('category_code', $request->status);
            $workingType = $categories->where('category_type_id', '23');
            $currentStatus = "";
            $currentStatusType = "";
            $currentWorkingType = $request->working_type;
            //get current status type name

            $currentStatusTypeName = "";

            //get current wrking type name
            $currentWorkingTypeName = Category::where('categories.id', '=', $request->working_type)
                ->select('category_name')
                ->first();

            $currentWorkingTypeName = $currentWorkingTypeName->category_name;

            return view('admin.employee.filter.working_type_filter_view', compact('employees', 'status', 'statusType', 'workingType', 'currentStatus', 'currentStatusType', 'currentWorkingType', 'currentStatusTypeName', 'currentWorkingTypeName'));
        } elseif ($request->status != NULL && $request->status_type == NULL && $request->working_type == NULL) {

            $categories = $this->getCategories([21, 22, 23]);

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                    ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                    ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                    ->select('employees.*')
                    ->where('emp_status.id', $request->status)
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('employees.main_branch_id', 52)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('employees.main_branch_id', 53)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                        ->join('categories as emp_status_type', 'emp_status_type.id', '=', 'employees.employee_status_type_id')
                        ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                        ->select('employees.*')
                        ->where('emp_status.id', $request->status)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                }
            }

            $status = $categories->where('category_type_id', '21');
            $statusType = $categories->where('category_type_id', '22')->where('category_code', $request->status);
            $workingType = $categories->where('category_type_id', '23');
            $currentStatus = $request->status;
            $currentStatusType = "";
            $currentWorkingType = "";
            $currentStatusTypeName = "";
            $currentWorkingTypeName = "";

            return view('admin.employee.filter.working_type_filter_view', compact('employees', 'status', 'statusType', 'workingType', 'currentStatus', 'currentStatusType', 'currentWorkingType', 'currentStatusTypeName', 'currentWorkingTypeName'));
        } else {

            $categories = $this->getCategories([21, 22, 23]);
            $employees = array();
            $status = $categories->where('category_type_id', '21');
            $workingType = $categories->where('category_type_id', '23');
            $statusType = array();
            $currentStatus = "";
            $currentStatusType = "";
            $currentWorkingType = "";
            $currentStatusTypeName = "";
            $currentWorkingTypeName = "";

            return view('admin.employee.filter.working_type_filter_view', compact('employees', 'status', 'statusType', 'workingType', 'currentStatus', 'currentStatusType', 'currentWorkingType', 'currentStatusTypeName', 'currentWorkingTypeName'));
        }
    }

    public function systemUserFilterView()
    {

        $employees = array();
        $roles = Role::all();
        $currentRole = "";
        $currentStatus = "";
        $currentRoleName = "";

        return view('admin.employee.filter.system_user_filter_view', compact('employees', 'roles', 'currentRole', 'currentStatus', 'currentRoleName'));
    }

    public function systemUserSearch(Request $request)
    {

        if ($request->role != NULL && $request->status != NULL) {

            $employees =  Employee::join('users', 'users.employee_no', '=', 'employees.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('employees.*', 'users.id', 'users.main_branch_id')
                ->where('users.status_id', $request->status)
                ->where('roles.id', $request->role)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $roles = Role::all();
            $currentRole = $request->role;
            $currentStatus = $request->status;
            $currentRoleName = DB::table('roles')
                ->where('roles.id', '=', $request->role)
                ->select('name as value')
                ->get();

            $currentRoleName = json_decode($currentRoleName, true);
            $currentRoleName = $currentRoleName[0]["value"];


            return view('admin.employee.filter.system_user_filter_view', compact('employees', 'roles', 'currentRole', 'currentStatus', 'currentRoleName'));
        } elseif ($request->role != NULL && $request->status == NULL) {

            $employees =  Employee::join('users', 'users.employee_no', '=', 'employees.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('employees.*', 'users.id', 'users.main_branch_id')
                ->where('roles.id', $request->role)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $roles = Role::all();
            $currentRole = $request->role;
            $currentStatus = "";
            $currentRoleName = DB::table('roles')
                ->where('roles.id', '=', $request->role)
                ->select('name as value')
                ->get();

            $currentRoleName = json_decode($currentRoleName, true);
            $currentRoleName = $currentRoleName[0]["value"];


            return view('admin.employee.filter.system_user_filter_view', compact('employees', 'roles', 'currentRole', 'currentStatus', 'currentRoleName'));
        } elseif ($request->role == NULL && $request->status != NULL) {

            $employees =  Employee::join('users', 'users.employee_no', '=', 'employees.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('employees.*', 'users.id', 'users.main_branch_id')
                ->where('users.status_id', $request->status)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $roles = Role::all();
            $currentRole = "";
            $currentStatus = $request->status;
            $currentRoleName = "";


            return view('admin.employee.filter.system_user_filter_view', compact('employees', 'roles', 'currentRole', 'currentStatus', 'currentRoleName'));
        } else {

            $employees = array();
            $roles = Role::all();
            $currentRole = "";
            $currentStatus = "";
            $currentRoleName = "";

            return view('admin.employee.filter.system_user_filter_view', compact('employees', 'roles', 'currentRole', 'currentStatus', 'currentRoleName'));
        }
    }

    public function educationFilterView()
    {
        $categories = $this->getCategories([16, 21]);
        $employees = array();
        $status = $categories->where('category_type_id', '21');
        $educations = $categories->where('category_type_id', '16');
        $currentStatus = "";
        $currentEducation = "";
        $currentEducationName = "";

        return view('admin.employee.filter.education_filter_view', compact('employees', 'status', 'educations', 'currentStatus', 'currentEducation', 'currentEducationName'));
    }

    public function educationSearch(Request $request)
    {

        if ($request->status != NULL && $request->education != NULL) {

            $categories = $this->getCategories([16, 21]);
            $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                ->join('categories as emp_edu', 'emp_edu.id', '=', 'employees.emp_highest_edu_level')
                ->select('employees.*')
                ->where('emp_status.id', $request->status)
                ->where('emp_edu.id', $request->education)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $status = $categories->where('category_type_id', '21');
            $educations = $categories->where('category_type_id', '16');
            $currentStatus = $request->status;
            $currentEducation = $request->education;
            //get current status type name
            $currentEducationName = Category::where('categories.id', '=', $request->education)
                ->select('category_name as value')
                ->get();

            $currentEducationName = json_decode($currentEducationName, true);
            $currentEducationName = $currentEducationName[0]["value"];


            return view('admin.employee.filter.education_filter_view', compact('employees', 'status', 'educations', 'currentStatus', 'currentEducation', 'currentEducationName'));
        } elseif ($request->education != NULL && $request->status == NULL) {

            $categories = $this->getCategories([16, 21]);
            $employees =  Employee::join('categories as emp_status', 'emp_status.id', '=', 'employees.employee_status_id')
                ->join('categories as emp_edu', 'emp_edu.id', '=', 'employees.emp_highest_edu_level')
                ->select('employees.*')
                ->where('emp_edu.id', $request->education)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $status = $categories->where('category_type_id', '21');
            $educations = $categories->where('category_type_id', '16');
            $currentStatus = "";
            $currentEducation = $request->education;
            //get current status type name
            $currentEducationName = Category::where('categories.id', '=', $request->education)
                ->select('category_name as value')
                ->get();

            $currentEducationName = json_decode($currentEducationName, true);
            $currentEducationName = $currentEducationName[0]["value"];


            return view('admin.employee.filter.education_filter_view', compact('employees', 'status', 'educations', 'currentStatus', 'currentEducation', 'currentEducationName'));
        } else {

            $categories = $this->getCategories([16, 21]);
            $employees = array();
            $status = $categories->where('category_type_id', '21');
            $educations = $categories->where('category_type_id', '16');
            $currentStatus = "";
            $currentEducation = "";
            $currentEducationName = "";

            return view('admin.employee.filter.education_filter_view', compact('employees', 'status', 'educations', 'currentStatus', 'currentEducation', 'currentEducationName'));
        }
    }

    public function genderFilterView()
    {
        $categories = $this->getCategories([1, 4]);
        $employees = array();
        $genders = $categories->where('category_type_id', '1');
        $civils = $categories->where('category_type_id', '4');
        $currentGender = "";
        $currentCivil = "";

        return view('admin.employee.filter.gender_filter_view', compact('employees', 'genders', 'civils', 'currentGender', 'currentCivil'));
    }

    public function genderSearch(Request $request)
    {

        if ($request->gender != NULL && $request->civil != NULL) {

            $categories = $this->getCategories([1, 4]);
            $employees =  Employee::join('categories as gender', 'gender.id', '=', 'employees.gender_id')
                ->join('categories as civil', 'civil.id', '=', 'employees.civil_status_id')
                ->select('employees.*')
                ->where('gender.id', $request->gender)
                ->where('civil.id', $request->civil)
                ->where('employees.employee_status_id', 110)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $genders = $categories->where('category_type_id', '1');
            $civils = $categories->where('category_type_id', '4');
            $currentGender = $request->gender;
            $currentCivil = $request->civil;

            return view('admin.employee.filter.gender_filter_view', compact('employees', 'genders', 'civils', 'currentGender', 'currentCivil'));
        } elseif ($request->gender != NULL && $request->civil == NULL) {

            $categories = $this->getCategories([1, 4]);
            $employees =  Employee::join('categories as gender', 'gender.id', '=', 'employees.gender_id')
                ->join('categories as civil', 'civil.id', '=', 'employees.civil_status_id')
                ->select('employees.*')
                ->where('gender.id', $request->gender)
                ->where('employees.employee_status_id', 110)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $genders = $categories->where('category_type_id', '1');
            $civils = $categories->where('category_type_id', '4');
            $currentGender = $request->gender;
            $currentCivil = "";

            return view('admin.employee.filter.gender_filter_view', compact('employees', 'genders', 'civils', 'currentGender', 'currentCivil'));
        } elseif ($request->gender == NULL && $request->civil != NULL) {

            $categories = $this->getCategories([1, 4]);
            $employees =  Employee::join('categories as gender', 'gender.id', '=', 'employees.gender_id')
                ->join('categories as civil', 'civil.id', '=', 'employees.civil_status_id')
                ->select('employees.*')
                ->where('employees.employee_status_id', 110)
                ->where('civil.id', $request->civil)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $genders = $categories->where('category_type_id', '1');
            $civils = $categories->where('category_type_id', '4');
            $currentGender = "";
            $currentCivil = $request->civil;

            return view('admin.employee.filter.gender_filter_view', compact('employees', 'genders', 'civils', 'currentGender', 'currentCivil'));
        } else {

            $categories = $this->getCategories([1, 4]);
            $employees = array();
            $genders = $categories->where('category_type_id', '1');
            $civils = $categories->where('category_type_id', '4');
            $currentGender = "";
            $currentCivil = "";

            return view('admin.employee.filter.gender_filter_view', compact('employees', 'genders', 'civils', 'currentGender', 'currentCivil'));
        }
    }

    public function raceFilterView()
    {
        $categories = $this->getCategories([2, 3]);
        $employees = array();
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $currentRace = "";
        $currentReligion = "";

        return view('admin.employee.filter.race_filter_view', compact('employees', 'races', 'religions', 'currentRace', 'currentReligion'));
    }

    public function raceSearch(Request $request)
    {

        if ($request->race != NULL && $request->religion != NULL) {

            $categories = $this->getCategories([2, 3]);
            $employees =  Employee::join('categories as race', 'race.id', '=', 'employees.race_id')
                ->join('categories as religion', 'religion.id', '=', 'employees.religion_id')
                ->select('employees.*')
                ->where('employees.employee_status_id', 110)
                ->where('race.id', $request->race)
                ->where('religion.id', $request->religion)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $currentRace = $request->race;
            $currentReligion = $request->religion;

            return view('admin.employee.filter.race_filter_view', compact('employees', 'races', 'religions', 'currentRace', 'currentReligion'));
        } elseif ($request->race != NULL && $request->religion == NULL) {

            $categories = $this->getCategories([2, 3]);
            $employees =  Employee::join('categories as race', 'race.id', '=', 'employees.race_id')
                ->join('categories as religion', 'religion.id', '=', 'employees.religion_id')
                ->select('employees.*')
                ->where('employees.employee_status_id', 110)
                ->where('race.id', $request->race)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $currentRace = $request->race;
            $currentReligion = "";

            return view('admin.employee.filter.race_filter_view', compact('employees', 'races', 'religions', 'currentRace', 'currentReligion'));
        } elseif ($request->race == NULL && $request->religion != NULL) {

            $categories = $this->getCategories([2, 3]);
            $employees =  Employee::join('categories as race', 'race.id', '=', 'employees.race_id')
                ->join('categories as religion', 'religion.id', '=', 'employees.religion_id')
                ->select('employees.*')
                ->where('employees.employee_status_id', 110)
                ->where('religion.id', $request->religion)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $currentRace = "";
            $currentReligion = $request->religion;

            return view('admin.employee.filter.race_filter_view', compact('employees', 'races', 'religions', 'currentRace', 'currentReligion'));
        } else {

            $categories = $this->getCategories([2, 3]);
            $employees = array();
            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $currentRace = "";
            $currentReligion = "";

            return view('admin.employee.filter.race_filter_view', compact('employees', 'races', 'religions', 'currentRace', 'currentReligion'));
        }
    }

    public function plocationFilterView()
    {
        $employees = array();
        $provinces = Province::all();
        $districts = array();
        $cities = array();
        $currentProvince = "";
        $currentDistrict = "";
        $currentCity = "";
        $currentProvinceName = "";
        $currentDistrictName = "";
        $currentCityName = "";

        return view('admin.employee.filter.plocation_filter_view', compact('employees', 'provinces', 'districts', 'cities', 'currentProvince', 'currentDistrict', 'currentCity', 'currentProvinceName', 'currentDistrictName', 'currentCityName'));
    }

    public function plocationSearch(Request $request)
    {

        if ($request->province != NULL && $request->district != NULL && $request->city != NULL) {

            $employees =  Employee::join('cities', 'cities.id', '=', 'employees.permanent_city_id')
                ->select('employees.*')
                ->where('cities.id', $request->city)
                ->where('employees.employee_status_id', 110)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $provinces = Province::all();
            $districts = District::where('province_id', $request->province)->get();
            $cities = City::where('district_id', $request->district)->get();
            $currentProvince = $request->province;
            $currentDistrict = $request->district;
            $currentCity = $request->city;

            $currentProvinceName = Province::where('provinces.id', '=', $request->province)
                ->select('name_en as value')
                ->get();

            $currentProvinceName = json_decode($currentProvinceName, true);
            $currentProvinceName = $currentProvinceName[0]["value"];

            $currentDistrictName = District::where('districts.id', '=', $request->district)
                ->select('name_en as value')
                ->get();

            $currentDistrictName = json_decode($currentDistrictName, true);
            $currentDistrictName = $currentDistrictName[0]["value"];

            $currentCityName = City::where('cities.id', '=', $request->city)
                ->select('name_en as value')
                ->get();

            $currentCityName = json_decode($currentCityName, true);
            $currentCityName =  $currentCityName[0]["value"];

            return view('admin.employee.filter.plocation_filter_view', compact('employees', 'provinces', 'districts', 'cities', 'currentProvince', 'currentDistrict', 'currentCity', 'currentProvinceName', 'currentDistrictName', 'currentCityName'));
        } elseif ($request->province != NULL && $request->district != NULL && $request->city == NULL) {

            $employees =  Employee::join('cities', 'cities.id', '=', 'employees.permanent_city_id')
                ->join('districts', 'districts.id', '=', 'cities.district_id')
                ->select('employees.*')
                ->where('districts.id', $request->district)
                ->where('employees.employee_status_id', 110)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $provinces = Province::all();
            $districts = District::where('province_id', $request->province)->get();
            $cities = array();
            $currentProvince = $request->province;
            $currentDistrict = $request->district;
            $currentCity = "";

            $currentProvinceName = Province::where('provinces.id', '=', $request->province)
                ->select('name_en as value')
                ->get();

            $currentProvinceName = json_decode($currentProvinceName, true);
            $currentProvinceName = $currentProvinceName[0]["value"];

            $currentDistrictName = District::where('districts.id', '=', $request->district)
                ->select('name_en as value')
                ->get();

            $currentDistrictName = json_decode($currentDistrictName, true);
            $currentDistrictName = $currentDistrictName[0]["value"];

            $currentCityName = "";

            return view('admin.employee.filter.plocation_filter_view', compact('employees', 'provinces', 'districts', 'cities', 'currentProvince', 'currentDistrict', 'currentCity', 'currentProvinceName', 'currentDistrictName', 'currentCityName'));
        } elseif ($request->province != NULL && $request->district == NULL && $request->city == NULL) {

            $employees =  Employee::join('cities', 'cities.id', '=', 'employees.permanent_city_id')
                ->join('districts', 'districts.id', '=', 'cities.district_id')
                ->join('provinces', 'provinces.id', '=', 'districts.province_id')
                ->select('employees.*')
                ->where('provinces.id', $request->province)
                ->where('employees.employee_status_id', 110)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $provinces = Province::all();
            $districts = array();
            $cities = array();
            $currentProvince = $request->province;
            $currentDistrict = "";
            $currentCity = "";

            $currentProvinceName = Province::where('provinces.id', '=', $request->province)
                ->select('name_en as value')
                ->get();

            $currentProvinceName = json_decode($currentProvinceName, true);
            $currentProvinceName = $currentProvinceName[0]["value"];

            $currentDistrictName = "";

            $currentCityName = "";

            return view('admin.employee.filter.plocation_filter_view', compact('employees', 'provinces', 'districts', 'cities', 'currentProvince', 'currentDistrict', 'currentCity', 'currentProvinceName', 'currentDistrictName', 'currentCityName'));
        } else {

            $employees = array();
            $provinces = Province::all();
            $districts = array();
            $cities = array();
            $currentProvince = "";
            $currentDistrict = "";
            $currentCity = "";
            $currentProvinceName = "";
            $currentDistrictName = "";
            $currentCityName = "";

            return view('admin.employee.filter.plocation_filter_view', compact('employees', 'provinces', 'districts', 'cities', 'currentProvince', 'currentDistrict', 'currentCity', 'currentProvinceName', 'currentDistrictName', 'currentCityName'));
        }
    }

    public function clocationFilterView()
    {
        $employees = array();
        $provinces = Province::all();
        $districts = array();
        $cities = array();
        $currentProvince = "";
        $currentDistrict = "";
        $currentCity = "";
        $currentProvinceName = "";
        $currentDistrictName = "";
        $currentCityName = "";

        return view('admin.employee.filter.clocation_filter_view', compact('employees', 'provinces', 'districts', 'cities', 'currentProvince', 'currentDistrict', 'currentCity', 'currentProvinceName', 'currentDistrictName', 'currentCityName'));
    }

    public function clocationSearch(Request $request)
    {

        if ($request->province != NULL && $request->district != NULL && $request->city != NULL) {

            $employees =  Employee::join('cities', 'cities.id', '=', 'employees.postal_city_id')
                ->select('employees.*')
                ->where('employees.employee_status_id', 110)
                ->where('cities.id', $request->city)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $provinces = Province::all();
            $districts = District::where('province_id', $request->province)->get();
            $cities = City::where('district_id', $request->district)->get();
            $currentProvince = $request->province;
            $currentDistrict = $request->district;
            $currentCity = $request->city;

            $currentProvinceName = Province::where('provinces.id', '=', $request->province)
                ->select('name_en as value')
                ->get();

            $currentProvinceName = json_decode($currentProvinceName, true);
            $currentProvinceName = $currentProvinceName[0]["value"];

            $currentDistrictName = District::where('districts.id', '=', $request->district)
                ->select('name_en as value')
                ->get();

            $currentDistrictName = json_decode($currentDistrictName, true);
            $currentDistrictName = $currentDistrictName[0]["value"];

            $currentCityName = City::where('cities.id', '=', $request->city)
                ->select('name_en as value')
                ->get();

            $currentCityName = json_decode($currentCityName, true);
            $currentCityName =  $currentCityName[0]["value"];


            return view('admin.employee.filter.clocation_filter_view', compact('employees', 'provinces', 'districts', 'cities', 'currentProvince', 'currentDistrict', 'currentCity', 'currentProvinceName', 'currentDistrictName', 'currentCityName'));
        } elseif ($request->province != NULL && $request->district != NULL && $request->city == NULL) {

            $employees =  Employee::join('cities', 'cities.id', '=', 'employees.postal_city_id')
                ->join('districts', 'districts.id', '=', 'cities.district_id')
                ->select('employees.*')
                ->where('employees.employee_status_id', 110)
                ->where('districts.id', $request->district)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $provinces = Province::all();
            $districts = District::where('province_id', $request->province)->get();
            $cities = array();
            $currentProvince = $request->province;
            $currentDistrict = $request->district;
            $currentCity = "";

            $currentProvinceName = Province::where('provinces.id', '=', $request->province)
                ->select('name_en as value')
                ->get();

            $currentProvinceName = json_decode($currentProvinceName, true);
            $currentProvinceName = $currentProvinceName[0]["value"];

            $currentDistrictName = District::where('districts.id', '=', $request->district)
                ->select('name_en as value')
                ->get();

            $currentDistrictName = json_decode($currentDistrictName, true);
            $currentDistrictName = $currentDistrictName[0]["value"];

            $currentCityName = "";

            return view('admin.employee.filter.clocation_filter_view', compact('employees', 'provinces', 'districts', 'cities', 'currentProvince', 'currentDistrict', 'currentCity', 'currentProvinceName', 'currentDistrictName', 'currentCityName'));
        } elseif ($request->province != NULL && $request->district == NULL && $request->city == NULL) {

            $employees =  Employee::join('cities', 'cities.id', '=', 'employees.postal_city_id')
                ->join('districts', 'districts.id', '=', 'cities.district_id')
                ->join('provinces', 'provinces.id', '=', 'districts.province_id')
                ->select('employees.*')
                ->where('employees.employee_status_id', 110)
                ->where('provinces.id', $request->province)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

            $provinces = Province::all();
            $districts = array();
            $cities = array();
            $currentProvince = $request->province;
            $currentDistrict = "";
            $currentCity = "";

            $currentProvinceName = Province::where('provinces.id', '=', $request->province)
                ->select('name_en as value')
                ->get();

            $currentProvinceName = json_decode($currentProvinceName, true);
            $currentProvinceName = $currentProvinceName[0]["value"];

            $currentDistrictName = "";

            $currentCityName = "";

            return view('admin.employee.filter.clocation_filter_view', compact('employees', 'provinces', 'districts', 'cities', 'currentProvince', 'currentDistrict', 'currentCity', 'currentProvinceName', 'currentDistrictName', 'currentCityName'));
        } else {

            $employees = array();
            $provinces = Province::all();
            $districts = array();
            $cities = array();
            $currentProvince = "";
            $currentDistrict = "";
            $currentCity = "";
            $currentProvinceName = "";
            $currentDistrictName = "";
            $currentCityName = "";

            return view('admin.employee.filter.clocation_filter_view', compact('employees', 'provinces', 'districts', 'cities', 'currentProvince', 'currentDistrict', 'currentCity', 'currentProvinceName', 'currentDistrictName', 'currentCityName'));
        }
    }

    public function typeFilterView()
    {

        $employees = array();
        $faculties = Faculty::all();
        $departments = array();
        $departmentDetials = array();
        $currentDepartment = "";
        $currentFaculty = "";
        $activeWorkingType = "";
        $totalCount = 0;
        $ActiveCount = 0;
        $InactiveCount = 0;

        return view('admin.employee.filter.type_filter_view', compact('faculties', 'employees', 'currentDepartment', 'activeWorkingType', 'totalCount', 'ActiveCount', 'InactiveCount', 'departmentDetials', 'currentFaculty', 'departments'));
    }

    public function typeSearch(Request $request)
    {

        if ($request->department_id != NULL && $request->employee_work_type != NULL) {

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;
            $empType = [];

            if ($request->employee_work_type == 138) {
                $empType = [138, 139];
            } elseif ($request->employee_work_type == 140) {
                $empType = [140];
            } elseif ($request->employee_work_type == 141) {
                $empType = [141];
            } elseif ($request->employee_work_type == 142) {
                $empType = [142];
            }

            if ($mainBranch == 51) {

                $employees = Employee::orderBy('employee_no')->whereIn('employee_work_type', $empType)->where('department_id', $request->department_id)->where('employee_status_id', 110)->with('designation.category')->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->whereIn('employee_work_type', $empType)->where('department_id', $request->department_id)->where('employee_status_id', 110)
                        ->where('employees.main_branch_id', 52)
                        ->with('designation.category')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->whereIn('employee_work_type', $empType)->where('department_id', $request->department_id)->where('employee_status_id', 110)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 52)
                        ->with('designation.category')
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_work_type', $empType)->where('department_id', $request->department_id)->where('employee_status_id', 110)
                        ->where('employees.main_branch_id', 53)
                        ->with('designation.category')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_work_type', $empType)->where('department_id', $request->department_id)->where('employee_status_id', 110)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 53)
                        ->with('designation.category')
                        ->get();
                }
            }


            $currentDepartment = $request->department_id;
            $currentFaculty = $request->faculty_id;
            $activeWorkingType = $request->employee_work_type;
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', $request->faculty_id)->get();

            $departmentDetials = Department::join('department_heads', 'department_heads.department_id', '=', 'departments.id')
                ->join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
                ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
                ->where('departments.id', '=', $request->department_id)
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'employees.mobile_no', 'employees.email', 'departments.department_name', 'department_heads.appointmemt_type', 'department_heads.start_date', 'department_heads.end_date', 'department_heads.active_status', 'faculties.faculty_name')
                ->get();

            $totalCount = Employee::orderBy('employee_no')->where('department_id', $request->department_id)->whereIn('employee_work_type', $empType)->count();

            $ActiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 110)->where('department_id', $request->department_id)->whereIn('employee_work_type', $empType)->count();

            $InactiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 111)->where('department_id', $request->department_id)->whereIn('employee_work_type', $empType)->count();

            //dd($employees);

            return view('admin.employee.filter.type_filter_view', compact('faculties', 'employees', 'currentDepartment', 'activeWorkingType', 'totalCount', 'ActiveCount', 'InactiveCount', 'departmentDetials', 'currentFaculty', 'departments'));
        } elseif ($request->department_id != NULL && $request->employee_work_type == NULL) {

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

            if ($mainBranch == 51) {

                $employees = Employee::orderBy('employee_no')->where('department_id', $request->department_id)->where('employee_status_id', 110)->with('designation.category')->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('department_id', $request->department_id)->where('employee_status_id', 110)
                        ->where('employees.main_branch_id', 52)
                        ->with('designation.category')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('department_id', $request->department_id)->where('employee_status_id', 110)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 52)
                        ->with('designation.category')
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('department_id', $request->department_id)->where('employee_status_id', 110)
                        ->where('employees.main_branch_id', 53)
                        ->with('designation.category')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('department_id', $request->department_id)->where('employee_status_id', 110)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 53)
                        ->with('designation.category')
                        ->get();
                }
            }

            $currentDepartment = $request->department_id;
            $currentFaculty = $request->faculty_id;
            $activeWorkingType = "";
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', $request->faculty_id)->get();

            $departmentDetials = Department::join('department_heads', 'department_heads.department_id', '=', 'departments.id')
                ->join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
                ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
                ->where('departments.id', '=', $request->department_id)
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'employees.mobile_no', 'employees.email', 'departments.department_name', 'department_heads.appointmemt_type', 'department_heads.start_date', 'department_heads.end_date', 'department_heads.active_status', 'faculties.faculty_name')
                ->get();

            $totalCount = Employee::orderBy('employee_no')->where('department_id', $request->department_id)->count();

            $ActiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 110)->where('department_id', $request->department_id)->count();

            $InactiveCount = Employee::orderBy('employee_no')->where('employee_status_id', 111)->where('department_id', $request->department_id)->count();

            return view('admin.employee.filter.type_filter_view', compact('faculties', 'employees', 'currentDepartment', 'activeWorkingType', 'totalCount', 'ActiveCount', 'InactiveCount', 'departmentDetials', 'currentFaculty', 'departments'));
        } elseif ($request->department_id == NULL && $request->employee_work_type != NULL) {

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;
            $empType = [];

            if ($request->employee_work_type == 138) {
                $empType = [138, 139];
            } elseif ($request->employee_work_type == 140) {
                $empType = [140];
            } elseif ($request->employee_work_type == 141) {
                $empType = [141];
            } elseif ($request->employee_work_type == 142) {
                $empType = [142];
            }

            if ($mainBranch == 51) {

                $employees = Employee::orderBy('employee_no')->whereIn('employee_work_type', $empType)->where('employee_status_id', 110)->with('designation.category')->get();
            } elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_work_type', $empType)->where('employee_status_id', 110)
                        ->where('employees.main_branch_id', 52)
                        ->with('designation.category')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_work_type', $empType)->where('employee_status_id', 110)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 52)
                        ->with('designation.category')
                        ->get();
                }
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_work_type', $empType)->where('employee_status_id', 110)
                        ->where('employees.main_branch_id', 53)
                        ->with('designation.category')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $employees = Employee::orderBy('employee_no')->where('employee_work_type', $empType)->where('employee_status_id', 110)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->where('employees.main_branch_id', 53)
                        ->with('designation.category')
                        ->get();
                }
            }


            $faculties = Faculty::all();
            $departments =  array();
            $departmentDetials = array();
            $currentFaculty = "";
            $currentDepartment = "";
            $activeWorkingType = $request->employee_work_type;
            $totalCount = Employee::orderBy('employee_no')->whereIn('employee_work_type', $empType)->count();
            $ActiveCount = 0;
            $InactiveCount = 0;

            //dd($activeWorkingType);

            return view('admin.employee.filter.type_filter_view', compact('faculties', 'employees', 'currentDepartment', 'activeWorkingType', 'totalCount', 'ActiveCount', 'InactiveCount', 'departmentDetials', 'currentFaculty', 'departments'));
        } else {

            $employees = array();
            $faculties = Faculty::all();
            $departments = array();
            $departmentDetials = array();
            $currentFaculty = "";
            $currentDepartment = "";
            $activeWorkingType = "";
            $totalCount = 0;
            $ActiveCount = 0;
            $InactiveCount = 0;

            return view('admin.employee.filter.type_filter_view', compact('faculties', 'employees', 'currentDepartment', 'activeWorkingType', 'totalCount', 'ActiveCount', 'InactiveCount', 'departmentDetials', 'currentFaculty', 'departments'));
        }
    }

    public function customFilterView()
    {

        return view('admin.employee.filter.index');
    }

    public function customReport1()
    {

        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select('employees.*', 'categories.category_name', 'designations.ugc_mis', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status')
            ->where('employees.main_branch_id', 53)
            ->where('employees.employee_status_id', 110)
            ->orderBy('employees.employee_no')
            ->get();

        return view('admin.employee.filter.custom_filter_view', compact('employees'));
    }

    public function customReport2()
    {

        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('employees.*', 'categories.category_name')
            ->where('gender_id', 2)
            ->where('main_branch_id', 53)
            ->whereIn(DB::raw('YEAR(date_of_birth)'), [1978, 1988])
            ->get();

        return view('admin.employee.filter.custom_filter_view_2', compact('employees'));
    }

    public function customReport3()
    {

        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select('employees.*', 'categories.category_name', 'designations.ugc_mis', 'title.category_name as title_name', 'departments.name_status')
            ->where('employees.main_branch_id', 53)
            ->where('employees.employee_status_id', 110)
            ->orderBy('employees.employee_no')
            ->get();

        return view('admin.employee.filter.custom_filter_view_3', compact('employees'));
    }

    public function customReport4()
    {

        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select('employees.*', 'categories.category_name', 'designations.ugc_mis', 'title.category_name as title_name', 'departments.name_status')
            ->where('employees.main_branch_id', 52)
            ->where('employees.employee_status_id', 110)
            ->whereIn('employees.employee_work_type', [141])
            ->orderBy('employees.employee_no')
            ->get();

        return view('admin.employee.filter.custom_filter_view_4', compact('employees'));
    }

    public function customReport5()
    {

        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select('employees.*', 'categories.category_name', 'designations.ugc_mis', 'title.category_name as title_name', 'departments.name_status')
            ->whereIN('designations.id', [78, 454, 478, 479, 512, 513, 514, 521, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 709])
            ->where('employees.employee_status_id', 110)
            ->orderBy('employees.employee_no')
            ->get();

        return view('admin.employee.filter.custom_filter_view_5', compact('employees'));
    }

    public function customReport6()
    {

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $employees = Employee::where('employees.employee_status_id', 110)
                ->orderBy('employees.employee_no')
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::where('employees.main_branch_id', 52)
                    ->where('employees.employee_status_id', 110)
                    ->orderBy('employees.employee_no')
                    ->get();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::where('employees.main_branch_id', 53)
                    ->where('employees.employee_status_id', 110)
                    ->orderBy('employees.employee_no')
                    ->get();
            }
        }



        return view('admin.employee.filter.custom_filter_view_6', compact('employees'));
    }

    public function customReport7()
    {

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $employees = Employee::where('employees.employee_status_id', 110)
                ->orderBy('employees.employee_no')
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::where('employees.main_branch_id', 52)
                    ->where('employees.employee_status_id', 110)
                    ->orderBy('employees.employee_no')
                    ->get();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::where('employees.main_branch_id', 53)
                    ->where('employees.employee_status_id', 110)
                    ->orderBy('employees.employee_no')
                    ->get();
            }
        }



        return view('admin.employee.filter.custom_filter_view_7', compact('employees'));
    }

    public function customReport8()
    {

        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status')
            ->where('employees.employee_status_id', 110)
            ->orderBy('employees.employee_no')
            ->get();

        return view('admin.employee.filter.custom_filter_view_8', compact('employees'));
    }

    public function customReport9()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $nextYear = now()->addYear()->year;

        if ($mainBranch == 51) {

            $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status')
                ->where('employees.employee_status_id', 110)
                ->whereIN('employees.employee_work_type', [138, 139])
                ->whereYear('employees.retirement_date', $nextYear)
                ->orderBy('employees.employee_no')
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as title', 'employees.title_id', '=', 'title.id')
                    ->join('departments', 'employees.department_id', '=', 'departments.id')
                    ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status')
                    ->where('employees.employee_status_id', 110)
                    ->where('employees.main_branch_id', 52)
                    ->whereIN('employees.employee_work_type', [138, 139])
                    ->whereYear('employees.retirement_date', $nextYear)
                    ->orderBy('employees.employee_no')
                    ->get();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as title', 'employees.title_id', '=', 'title.id')
                    ->join('departments', 'employees.department_id', '=', 'departments.id')
                    ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status')
                    ->where('employees.employee_status_id', 110)
                    ->where('employees.main_branch_id', 53)
                    ->whereIN('employees.employee_work_type', [138, 139])
                    ->whereYear('employees.retirement_date', $nextYear)
                    ->orderBy('employees.employee_no')
                    ->get();
            }
        }



        return view('admin.employee.filter.custom_filter_view_9', compact('employees'));
    }

    public function customReport10()
    {

        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status')
            ->where('employees.employee_status_id', 110)
            ->orderBy('employees.employee_no')
            ->get();

        return view('admin.employee.filter.custom_filter_view_10', compact('employees'));
    }

    public function customReport11()
    {

        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status')
            ->where('employees.employee_status_id', 110)
            ->whereIN('employees.employee_work_type', [138, 139])
            ->orderBy('employees.employee_no')
            ->get();

        return view('admin.employee.filter.custom_filter_view_11', compact('employees'));
    }

    public function customReport12()
    {

        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as main_group', 'designations.main_group', '=', 'main_group.id')
            ->join('categories as ugc_mis', 'designations.ugc_mis', '=', 'ugc_mis.id')
            ->join('categories as ugc_finance', 'designations.ugc_finance', '=', 'ugc_finance.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select('employees.*', 'categories.category_name', 'designations.ugc_mis', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status', 'main_group.category_name as main_group_name', 'ugc_mis.category_name as ugc_mis_name', 'ugc_finance.category_name as ugc_finance_name')
            //->where('employees.main_branch_id',53)
            ->where('employees.employee_status_id', 110)
            ->orderBy('employees.employee_no')
            ->get();

        return view('admin.employee.filter.custom_filter_view_12', compact('employees'));
    }

    public function customReport13()
    {

        $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->join('categories as emp_group', 'emp_group.id', '=', 'designations.ugc_mis')
            ->join('categories as emp_work_type', 'emp_work_type.id', '=', 'employees.employee_work_type')
            ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
            ->where('employees.main_branch_id', 52)
            ->where('employees.employee_status_id', 110)
            ->where('emp_group.id', 135)
            ->whereIN('employees.employee_work_type', [138, 139])
            ->where('employees.current_appointment_date', '<', '2024-01-01')
            ->orderBy('employees.employee_no', 'ASC')
            ->get();

        return view('admin.employee.filter.custom_filter_view_13', compact('employees'));
    }

    public function customReport14()
    {
        $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->join('categories as emp_group', 'emp_group.id', '=', 'designations.ugc_mis')
            ->join('categories as emp_work_type', 'emp_work_type.id', '=', 'employees.employee_work_type')
            ->select('employees.*', 'designations.designation_name', 'departments.department_name', 'faculties.faculty_name')
            //->where('employees.main_branch_id', 52)
            ->where('employees.employee_status_id', 110)
            ->whereIN('employees.employee_work_type', [138, 139])
            ->orderBy('employees.employee_no', 'ASC')
            ->get();

        return view('admin.employee.filter.custom_filter_view_14', compact('employees'));
    }

    public function customReport15()
    {
        $mainBranch = Auth()->user()->main_branch_id;
        if ($mainBranch == 51) {

            $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status')
                ->where('employees.employee_status_id', 110)
                ->whereIN('employees.employee_work_type', [138, 139, 140])
                ->where('employees.main_branch_id', 52)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as title', 'employees.title_id', '=', 'title.id')
                    ->join('departments', 'employees.department_id', '=', 'departments.id')
                    ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status')
                    ->where('employees.employee_status_id', 110)
                    ->whereIN('employees.employee_work_type', [138, 139, 140])
                    ->where('employees.main_branch_id', 52)
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as title', 'employees.title_id', '=', 'title.id')
                    ->join('departments', 'employees.department_id', '=', 'departments.id')
                    ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status')
                    ->where('employees.employee_status_id', 110)
                    ->whereIN('employees.employee_work_type', [138, 139])
                    ->where('employees.main_branch_id', 53)
                    ->orderBy('employees.employee_no', 'ASC')
                    ->get();
            }
        }


        return view('admin.employee.filter.custom_filter_view_15', compact('employees'));
    }

    public function customReport16()
    {

        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status')
            ->where('employees.employee_status_id', 110)
            ->whereIN('employees.employee_work_type', [140, 141, 142])
            ->where('employees.main_branch_id', 52)
            ->orderBy('employees.employee_no', 'ASC')
            ->get();

        return view('admin.employee.filter.custom_filter_view_16', compact('employees'));
    }

    public function customReport17()
    {

        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->join('categories as ugs_mis', 'designations.ugc_mis', '=', 'ugs_mis.id')
            ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status', 'ugs_mis.category_name as ugs_mis_name')
            ->where('employees.employee_status_id', 110)
            ->whereIN('designations.ugc_mis', [100, 102, 103, 135, 136, 137])
            ->where('employees.main_branch_id', 52)
            ->orderBy('employees.employee_no', 'ASC')
            ->get();

        return view('admin.employee.filter.custom_filter_view_17', compact('employees'));
    }


    public function commonReport1()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;

        if ($mainBranch == 51) {

            $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as main_group', 'designations.main_group', '=', 'main_group.id')
                ->join('categories as ugc_mis', 'designations.ugc_mis', '=', 'ugc_mis.id')
                ->join('categories as ugc_finance', 'designations.ugc_finance', '=', 'ugc_finance.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->select('employees.*', 'categories.category_name', 'designations.ugc_mis', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status', 'main_group.category_name as main_group_name', 'ugc_mis.category_name as ugc_mis_name', 'ugc_finance.category_name as ugc_finance_name')
                //->where('employees.main_branch_id',53)
                ->where('employees.employee_status_id', 110)
                ->orderBy('employees.employee_no')
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as main_group', 'designations.main_group', '=', 'main_group.id')
                    ->join('categories as ugc_mis', 'designations.ugc_mis', '=', 'ugc_mis.id')
                    ->join('categories as ugc_finance', 'designations.ugc_finance', '=', 'ugc_finance.id')
                    ->join('categories as title', 'employees.title_id', '=', 'title.id')
                    ->join('departments', 'employees.department_id', '=', 'departments.id')
                    ->select('employees.*', 'categories.category_name', 'designations.ugc_mis', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status', 'main_group.category_name as main_group_name', 'ugc_mis.category_name as ugc_mis_name', 'ugc_finance.category_name as ugc_finance_name')
                    ->where('employees.main_branch_id', 52)
                    ->where('employees.employee_status_id', 110)
                    ->orderBy('employees.employee_no')
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as main_group', 'designations.main_group', '=', 'main_group.id')
                    ->join('categories as ugc_mis', 'designations.ugc_mis', '=', 'ugc_mis.id')
                    ->join('categories as ugc_finance', 'designations.ugc_finance', '=', 'ugc_finance.id')
                    ->join('categories as title', 'employees.title_id', '=', 'title.id')
                    ->join('departments', 'employees.department_id', '=', 'departments.id')
                    ->select('employees.*', 'categories.category_name', 'designations.ugc_mis', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status', 'main_group.category_name as main_group_name', 'ugc_mis.category_name as ugc_mis_name', 'ugc_finance.category_name as ugc_finance_name')
                    ->where('employees.main_branch_id', 52)
                    ->where('employees.employee_status_id', 110)
                    ->where('employees.assign_ma_user_id', $empNo)
                    ->orderBy('employees.employee_no')
                    ->get();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as main_group', 'designations.main_group', '=', 'main_group.id')
                    ->join('categories as ugc_mis', 'designations.ugc_mis', '=', 'ugc_mis.id')
                    ->join('categories as ugc_finance', 'designations.ugc_finance', '=', 'ugc_finance.id')
                    ->join('categories as title', 'employees.title_id', '=', 'title.id')
                    ->join('departments', 'employees.department_id', '=', 'departments.id')
                    ->select('employees.*', 'categories.category_name', 'designations.ugc_mis', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status', 'main_group.category_name as main_group_name', 'ugc_mis.category_name as ugc_mis_name', 'ugc_finance.category_name as ugc_finance_name')
                    ->where('employees.main_branch_id', 53)
                    ->where('employees.employee_status_id', 110)
                    ->orderBy('employees.employee_no')
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as main_group', 'designations.main_group', '=', 'main_group.id')
                    ->join('categories as ugc_mis', 'designations.ugc_mis', '=', 'ugc_mis.id')
                    ->join('categories as ugc_finance', 'designations.ugc_finance', '=', 'ugc_finance.id')
                    ->join('categories as title', 'employees.title_id', '=', 'title.id')
                    ->join('departments', 'employees.department_id', '=', 'departments.id')
                    ->select('employees.*', 'categories.category_name', 'designations.ugc_mis', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status', 'main_group.category_name as main_group_name', 'ugc_mis.category_name as ugc_mis_name', 'ugc_finance.category_name as ugc_finance_name')
                    ->where('employees.main_branch_id', 53)
                    ->where('employees.employee_status_id', 110)
                    ->where('employees.assign_ma_user_id', $empNo)
                    ->orderBy('employees.employee_no')
                    ->get();
            }
        }



        return view('admin.employee.filter.common_view', compact('employees'));
    }

    public function employeeSearch()
    {

        return view('admin.employee.search');
    }

    public function employeeSearchResult(Request $request)
    {
        $mainBranch = Auth()->user()->main_branch_id;
        $field = $request->input('field');
        $value = $request->input('value');
        $order = $request->input('order', 'ASC');

        if ($mainBranch == 51) {

            $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('categories as title', 'title.id', '=', 'employees.title_id')
                ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
                ->join('categories as status_type', 'status_type.id', '=', 'employees.employee_status_type_id')
                ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                ->join('categories as main_branch', 'main_branch.id', '=', 'employees.main_branch_id')
                ->join('categories as main_category', 'main_category.id', '=', 'designations.main_group')
                ->join('categories as mis_category', 'mis_category.id', '=', 'designations.ugc_mis')
                ->join('categories as staff_grade', 'staff_grade.id', '=', 'designations.staff_grade')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                //->where('employees.main_branch_id',53)
                ->where($field, 'LIKE', "{$value}")
                ->orderBy($field, $order)
                ->select(
                    'employees.*',
                    'designations.designation_name',
                    'title.category_name as title',
                    'departments.department_name',
                    'faculties.faculty_name',
                    'status.category_name as status',
                    'status_type.category_name as status_type',
                    'working_type.category_name as working_type',
                    'main_branch.category_name as main_branch',
                    'main_category.category_name as main_category',
                    'mis_category.category_name as mis_category',
                    'staff_grade.category_name as staff_grade',
                    'designations.salary_code'
                )
                ->get()
                ->map(function ($employee) {
                    $employee->encrypted_id = Crypt::encrypt($employee->employee_no);
                    $employee->can_edit = 1;
                    return $employee;
                });
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
                    ->join('categories as status_type', 'status_type.id', '=', 'employees.employee_status_type_id')
                    ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                    ->join('categories as main_branch', 'main_branch.id', '=', 'employees.main_branch_id')
                    ->join('categories as main_category', 'main_category.id', '=', 'designations.main_group')
                    ->join('categories as mis_category', 'mis_category.id', '=', 'designations.ugc_mis')
                    ->join('categories as staff_grade', 'staff_grade.id', '=', 'designations.staff_grade')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                    ->where('employees.main_branch_id', 52)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->where($field, 'LIKE', "{$value}")
                    ->orderBy($field, $order)
                    ->select(
                        'employees.*',
                        'designations.designation_name',
                        'title.category_name as title',
                        'departments.department_name',
                        'faculties.faculty_name',
                        'status.category_name as status',
                        'status_type.category_name as status_type',
                        'working_type.category_name as working_type',
                        'main_branch.category_name as main_branch',
                        'main_category.category_name as main_category',
                        'mis_category.category_name as mis_category',
                        'staff_grade.category_name as staff_grade',
                        'designations.salary_code'
                    )
                    ->get()
                    ->map(function ($employee) {
                        $employee->encrypted_id = Crypt::encrypt($employee->employee_no);
                        $employee->can_edit = 1;
                        return $employee;
                    });
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {
                # code...
                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
                    ->join('categories as status_type', 'status_type.id', '=', 'employees.employee_status_type_id')
                    ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                    ->join('categories as main_branch', 'main_branch.id', '=', 'employees.main_branch_id')
                    ->join('categories as main_category', 'main_category.id', '=', 'designations.main_group')
                    ->join('categories as mis_category', 'mis_category.id', '=', 'designations.ugc_mis')
                    ->join('categories as staff_grade', 'staff_grade.id', '=', 'designations.staff_grade')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                    ->where('employees.main_branch_id', 52)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->where($field, 'LIKE', "{$value}")
                    ->orderBy($field, $order)
                    ->select(
                        'employees.*',
                        'designations.designation_name',
                        'title.category_name as title',
                        'departments.department_name',
                        'faculties.faculty_name',
                        'status.category_name as status',
                        'status_type.category_name as status_type',
                        'working_type.category_name as working_type',
                        'main_branch.category_name as main_branch',
                        'main_category.category_name as main_category',
                        'mis_category.category_name as mis_category',
                        'staff_grade.category_name as staff_grade',
                        'designations.salary_code'
                    )
                    ->get()
                    ->map(function ($employee) {
                        $employee->encrypted_id = Crypt::encrypt($employee->employee_no);
                        return $employee;
                    });
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
                    ->join('categories as status_type', 'status_type.id', '=', 'employees.employee_status_type_id')
                    ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                    ->join('categories as main_branch', 'main_branch.id', '=', 'employees.main_branch_id')
                    ->join('categories as main_category', 'main_category.id', '=', 'designations.main_group')
                    ->join('categories as mis_category', 'mis_category.id', '=', 'designations.ugc_mis')
                    ->join('categories as staff_grade', 'staff_grade.id', '=', 'designations.staff_grade')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                    ->where('employees.main_branch_id', 53)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->where($field, 'LIKE', "{$value}")
                    ->orderBy($field, $order)
                    ->select(
                        'employees.*',
                        'designations.designation_name',
                        'title.category_name as title',
                        'departments.department_name',
                        'faculties.faculty_name',
                        'status.category_name as status',
                        'status_type.category_name as status_type',
                        'working_type.category_name as working_type',
                        'main_branch.category_name as main_branch',
                        'main_category.category_name as main_category',
                        'mis_category.category_name as mis_category',
                        'staff_grade.category_name as staff_grade',
                        'designations.salary_code'
                    )
                    ->get()
                    ->map(function ($employee) {
                        $employee->encrypted_id = Crypt::encrypt($employee->employee_no);
                        $employee->can_edit = 1;
                        return $employee;
                    });
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {
                # code...
                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
                    ->join('categories as status_type', 'status_type.id', '=', 'employees.employee_status_type_id')
                    ->join('categories as working_type', 'working_type.id', '=', 'employees.employee_work_type')
                    ->join('categories as main_branch', 'main_branch.id', '=', 'employees.main_branch_id')
                    ->join('categories as main_category', 'main_category.id', '=', 'designations.main_group')
                    ->join('categories as mis_category', 'mis_category.id', '=', 'designations.ugc_mis')
                    ->join('categories as staff_grade', 'staff_grade.id', '=', 'designations.staff_grade')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                    ->where('employees.main_branch_id', 53)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->where($field, 'LIKE', "{$value}")
                    ->orderBy($field, $order)
                    ->select(
                        'employees.*',
                        'designations.designation_name',
                        'title.category_name as title',
                        'departments.department_name',
                        'faculties.faculty_name',
                        'status.category_name as status',
                        'status_type.category_name as status_type',
                        'working_type.category_name as working_type',
                        'main_branch.category_name as main_branch',
                        'main_category.category_name as main_category',
                        'mis_category.category_name as mis_category',
                        'staff_grade.category_name as staff_grade',
                        'designations.salary_code'
                    )
                    ->get()
                    ->map(function ($employee) {
                        $employee->encrypted_id = Crypt::encrypt($employee->employee_no);
                        return $employee;
                    });
            }
        }



        return response()->json($employees);
    }

    public function employeeSuggestion(Request $request)
    {
        $mainBranch = Auth()->user()->main_branch_id;
        $field = $request->input('field');
        $value = $request->input('value');

        if (strlen($value) < 3) {
            return response()->json([]);
        }
        if ($mainBranch == 51) {

            $employees = Employee::where($field, 'like', '%' . $value . '%')->take(5)->get();
        } elseif ($mainBranch == 52) {
            # code...
            $employees = Employee::where($field, 'like', '%' . $value . '%')->where('main_branch_id', 52)->take(5)->get();
        } elseif ($mainBranch == 53) {
            # code...
            $employees = Employee::where($field, 'like', '%' . $value . '%')->where('main_branch_id', 53)->take(5)->get();
        }


        return response()->json($employees);
    }
}
