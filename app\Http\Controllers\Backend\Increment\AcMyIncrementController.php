<?php

namespace App\Http\Controllers\Backend\Increment;

use App\Http\Controllers\Controller;
use App\Mail\acIncreForwardToHodMail;
use App\Models\DepartmentHead;
use App\Models\Employee;
use App\Models\increAcSerMain;
use App\Models\increAcSerOther;
use App\Models\increAsSelfAssessment;
use App\Models\Increment;
use App\Models\incrementsProcessAc;
use App\Models\leaveAcademicSummery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;

class AcMyIncrementController extends Controller
{
    public function acUsj1stPageOpen(Request $request)
    {
        if (isset($request->empNo)) {
            session()->put('special_value', true);
            session()->forget('special_callback_url');
            $empNo = $request->empNo; //auth()->user()->employee_no;
            // $empNo = 9679;

            $desig_group_text = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->select('designations.ugc_mis')
                ->where('employees.employee_no', $empNo)
                ->get();

            if (count($desig_group_text) > 0) {
                foreach ($desig_group_text as $desig_group_texts) {
                    $desig_group_id = $desig_group_texts->ugc_mis;
                }
            } else {
                $desig_group_id = 0;
            }

            if ($desig_group_id == 135 || $desig_group_id == 99) {
                //ac and as
                $incre_text = incrementsProcessAc::where('emp_no', '=', $empNo)
                    ->orderBy('year', 'desc')
                    ->first();

                if ($incre_text !== null) {

                    $has_record = 1;
                    $incre_year = $incre_text->year;
                    $incre_date = $incre_text->incre_date;
                    $incre_amount = $incre_text->incre_amount;
                    $sal_step = $incre_text->present_sal_step;
                    $new_sal_step = $incre_text->new_sal_step;

                    $check_date = $incre_text->check_date;
                    $ex_status = $incre_text->ex_forward_status;
                    $ex_date = $incre_text->ex_forward_date;
                    $head_forward_status = $incre_text->head_forward_status;
                    $head_forward_date = $incre_text->head_forward_date;
                    $self_eval_status = $incre_text->self_eval_status;
                    $self_eval_date = $incre_text->self_eval_date;
                    $head_status = $incre_text->head_status;
                    $head_date = $incre_text->head_date;
                    $dean_status = $incre_text->dean_status;
                    $dean_date = $incre_text->dean_date;
                    $vc_status = $incre_text->vc_status;
                    $vc_date = $incre_text->vc_date;

                    $final_status = $incre_text->status;
                    $completed_date = $incre_text->completed_date;

                    $appID = $incre_text->id;
                    $refNo = $incre_text->ref_no;

                    $emp_type = $incre_text->emp_category;
                } else {
                    $has_record = 0;
                    $incre_year = '';
                    $incre_date = '';
                    $incre_amount = '';
                    $sal_step = '';
                    $new_sal_step = '';
                    $check_date = '';
                    $ex_status = 0;
                    $ex_date = '';
                    $head_forward_status = '';
                    $head_forward_date = '';
                    $self_eval_status = 0;
                    $self_eval_date = '';
                    $head_status = 0;
                    $head_date = '';
                    $dean_status = 0;
                    $dean_date = '';
                    $vc_status = 0;
                    $vc_date = '';
                    $final_status = 0;
                    $completed_date = '';

                    $appID = 0;
                    $refNo = 0;

                    $emp_type = 0;
                }


                return view('admin.increment.Academic.self_status', compact(

                    'has_record',
                    'incre_year',
                    'incre_date',
                    'incre_amount',
                    'sal_step',
                    'new_sal_step',
                    'check_date',
                    'ex_status',
                    'ex_date',
                    'head_forward_status',
                    'head_forward_date',
                    'self_eval_status',
                    'self_eval_date',
                    'head_status',
                    'head_date',
                    'dean_status',
                    'dean_date',
                    'vc_status',
                    'vc_date',
                    'appID',
                    'refNo',
                    'empNo',
                    'final_status',
                    'completed_date',
                    'emp_type'
                ));
            } elseif ($desig_group_id == 100 || $desig_group_id == 103 || $desig_group_id == 102) {
                //EX
                return view('admin.commonPages.coming_soon');
            } else {
                //Other
                // return view('admin.commonPages.coming_soon');
                // return redirect()->route('increment.progress','encrypt($empNo)');
                return redirect()->route('increment.progress', ['id' => encrypt($empNo)]);
            }
        } else {
            return view('admin.commonPages.coming_soon');
        }
    }

    public function selfEvalOpen(Request $request)
    {
        $appID = $request->appID;
        $refID = $request->RefNo;
        $empNo = $request->empNo;

        $part1_text = increAcSerMain::where('incre_id', $request->appID)->get();
        if (count($part1_text) > 0) {
            foreach ($part1_text as $part1_texts) {
                $under_lect_conduct_week = $part1_texts->under_lect_conduct_week;
                $under_lect_conduct_year = $part1_texts->under_lect_conduct_year;
                $post_lect_conduct_week = $part1_texts->post_lect_conduct_week;
                $post_lect_conduct_year = $part1_texts->post_lect_conduct_year;
                $under_tutor_conduct_week = $part1_texts->under_tutor_conduct_week;
                $under_tutor_conduct_year = $part1_texts->under_tutor_conduct_year;
                $post_tutor_conduct_week = $part1_texts->post_tutor_conduct_week;
                $post_tutor_conduct_year = $part1_texts->post_tutor_conduct_year;
                $under_practical_conduct_week = $part1_texts->under_practical_conduct_week;
                $under_practical_conduct_year = $part1_texts->under_practical_conduct_year;
                $post_practical_conduct_week = $part1_texts->post_practical_conduct_week;
                $post_practical_conduct_year = $part1_texts->post_practical_conduct_year;
                $under_project_supervised = $part1_texts->under_project_supervised;
                $post_project_supervised = $part1_texts->post_project_supervised;
            }
        } else {
            $under_lect_conduct_week = '';
            $under_lect_conduct_year = '';
            $post_lect_conduct_week = '';
            $post_lect_conduct_year = '';
            $under_tutor_conduct_week = '';
            $under_tutor_conduct_year = '';
            $post_tutor_conduct_week = '';
            $post_tutor_conduct_year = '';
            $under_practical_conduct_week = '';
            $under_practical_conduct_year = '';
            $post_practical_conduct_week = '';
            $post_practical_conduct_year = '';
            $under_project_supervised = '';
            $post_project_supervised = '';
        }

        $part2_text = increAcSerOther::where('incre_id', $request->appID)
            ->get();

        return view('admin.increment.Academic.self_eval_report', compact(
            'appID',
            'refID',
            'empNo',
            'under_lect_conduct_week',
            'under_lect_conduct_year',
            'post_lect_conduct_week',
            'post_lect_conduct_year',
            'under_tutor_conduct_week',
            'under_tutor_conduct_year',
            'post_tutor_conduct_week',
            'post_tutor_conduct_year',
            'under_practical_conduct_week',
            'under_practical_conduct_year',
            'post_practical_conduct_week',
            'post_practical_conduct_year',
            'under_project_supervised',
            'post_project_supervised',
            'part2_text'
        ));
    }

    public function serSubmit(Request $request)
    {

        $part1_delete1 = increAcSerMain::where('incre_id', $request->appID)->delete();
        $part2_delete1 = increAcSerOther::where('incre_id', $request->appID)->delete();

        $part1_text = new increAcSerMain();
        $part1_text->incre_id = $request->appID;
        $part1_text->incre_ref_no = $request->RefNo;
        $part1_text->under_lect_conduct_week = $request->lec_under_week;
        $part1_text->under_lect_conduct_year = $request->lect_under_year;
        $part1_text->post_lect_conduct_week = $request->lect_post_week;
        $part1_text->post_lect_conduct_year = $request->lect_post_year;
        $part1_text->under_tutor_conduct_week = $request->tuto_under_week;
        $part1_text->under_tutor_conduct_year = $request->tuto_under_year;
        $part1_text->post_tutor_conduct_week = $request->tuto_post_week;
        $part1_text->post_tutor_conduct_year = $request->tuto_post_year;
        $part1_text->under_practical_conduct_week = $request->pract_under_week;
        $part1_text->under_practical_conduct_year = $request->pract_under_year;
        $part1_text->post_practical_conduct_week = $request->pract_post_week;
        $part1_text->post_practical_conduct_year = $request->pract_post_year;
        $part1_text->under_project_supervised = $request->under_proj_sup;
        $part1_text->post_project_supervised = $request->post_proj_sup;
        $part1_text->user_id = $request->empNo;
        $part1_text->save();

        $sections = [
            'section5' => 304,
            'section6' => 305,
            'section7' => 306,
            'section8' => 307,
            'section9' => 308,
            'section10' => 309,
        ];

        foreach ($sections as $sectionKey => $typeNo) {
            $sectionData = $request->$sectionKey;

            if ($sectionData !== null && is_array($sectionData)) {
                foreach ($sectionData as $desc) {
                    $record = new increAcSerOther();
                    $record->incre_id = $request->appID;
                    $record->incre_ref_no = $request->RefNo;
                    $record->type_no = $typeNo;
                    $record->description = $desc;
                    $record->user_id = $request->empNo;
                    $record->save();
                }
            }
        }

        $s_test = incrementsProcessAc::where('id', '=', $request->appID)
            ->update([
                'self_eval_status' => 2,
                'self_eval_date' => today(),
                'head_status' => 1
            ]);
        //comment

        $emp_text = Employee::join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->select(
                'employees.initials',
                'employees.last_name',
                'designations.designation_name',
                'departments.id as depID',
                'departments.department_name',
                'categories.category_name',
                't.category_name as title',
            )
            ->where('employees.employee_no', $request->empNo)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $depID = $emp_texts->depID;
                $depName = $emp_texts->department_name;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
            }
        } else {
            $name = '';
            $depName = '';
            $desig = '';
            $depID = 0;
        }

        $hod_text = DepartmentHead::join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->select(
                't.category_name as title',
                'employees.initials',
                'employees.last_name',
                'department_heads.email'
            )
            ->where('department_heads.department_id', '=', $depID)
            ->get();

        if (count($hod_text) > 0) {
            foreach ($hod_text as $hod_texts) {
                $hod_name = $hod_texts->title . ' ' . $hod_texts->initials . ' ' . $hod_texts->last_name;
                $hod_email = $hod_texts->email;
            }
        } else {
            $hod_name = '';
            $hod_email = '';
        }


        //send email
        $emailData = [
            'name' =>  $name,
            'headName' => $hod_name,
            'depName' => $depName,
            'desig' => $desig,

        ];

        $mail = new acIncreForwardToHodMail($emailData);
        //sending email
        Mail::to($hod_email)->send($mail);
        // Mail::to('<EMAIL>')->send($mail);

        $notification = array(
            'message' => 'Self evaluation report submitted successfully.',
            'alert-type' => 'success'
        );

        return redirect()->route('ac.incre.selfEval.page1.open')->with($notification);
    }

    public function serSave(Request $request)
    {
        $part1_delete1 = increAcSerMain::where('incre_id', $request->appID)->delete();
        $part2_delete1 = increAcSerOther::where('incre_id', $request->appID)->delete();

        $empNo = $request->empNo;

        $part1_text = new increAcSerMain();
        $part1_text->incre_id = $request->appID;
        $part1_text->incre_ref_no = $request->RefNo;
        $part1_text->under_lect_conduct_week = $request->lec_under_week;
        $part1_text->under_lect_conduct_year = $request->lect_under_year;
        $part1_text->post_lect_conduct_week = $request->lect_post_week;
        $part1_text->post_lect_conduct_year = $request->lect_post_year;
        $part1_text->under_tutor_conduct_week = $request->tuto_under_week;
        $part1_text->under_tutor_conduct_year = $request->tuto_under_year;
        $part1_text->post_tutor_conduct_week = $request->tuto_post_week;
        $part1_text->post_tutor_conduct_year = $request->tuto_post_year;
        $part1_text->under_practical_conduct_week = $request->pract_under_week;
        $part1_text->under_practical_conduct_year = $request->pract_under_year;
        $part1_text->post_practical_conduct_week = $request->pract_post_week;
        $part1_text->post_practical_conduct_year = $request->pract_post_year;
        $part1_text->under_project_supervised = $request->under_proj_sup;
        $part1_text->post_project_supervised = $request->post_proj_sup;
        $part1_text->user_id = $request->empNo;
        $part1_text->save();
        
        $sections = [
            'section5' => 304,
            'section6' => 305,
            'section7' => 306,
            'section8' => 307,
            'section9' => 308,
            'section10' => 309,
        ];

        foreach ($sections as $sectionKey => $typeNo) {
            $sectionData = $request->$sectionKey;

            if ($sectionData !== null && is_array($sectionData)) {
                foreach ($sectionData as $desc) {
                    $record = new increAcSerOther();
                    $record->incre_id = $request->appID;
                    $record->incre_ref_no = $request->RefNo;
                    $record->type_no = $typeNo;
                    $record->description = $desc;
                    $record->user_id = $request->empNo;
                    $record->save();
                }
            }
        }

        $notification = array(
            'message' => 'Self evaluation report save successfully.',
            'alert-type' => 'success'
        );

        return redirect()->route('ac.incre.selfEval.page1.open',['empNo' => $empNo])->with($notification);
    }

    public function serPrint($id)
    {
        $tbID = decrypt($id);

        $emp_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('designations', 'designations.id', '=', 'increments_process_acs.desig_id')
            ->join('departments', 'departments.id', '=', 'increments_process_acs.dep_no')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            ->select(
                'employees.initials',
                'employees.last_name',

                'designations.designation_name',
                'departments.id as depID',
                'departments.department_name',
                'departments.name_status',
                'faculties.id as facID',
                'faculties.faculty_name',
                'categories.category_name',
                't.category_name as title',

                'employees.current_appointment_date',
                'employees.initial_appointment_date',
                'salary_scales.salary_scale_txt',

                'salary_scales.increment_value1',
                'increments_process_acs.emp_no',
                'increments_process_acs.incre_date',
                'increments_process_acs.ref_no',
                'increments_process_acs.id',
                'increments_process_acs.year',
                'increments_process_acs.incre_amount',
                'increments_process_acs.present_sal_step',
                'increments_process_acs.new_sal_step',
                'increments_process_acs.check_date',
                'increments_process_acs.self_eval_date'

            )
            ->where('increments_process_acs.id', $tbID)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $incre_id = $emp_texts->id;
                $emp_no = $emp_texts->emp_no;
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $appDate = $emp_texts->current_appointment_date;
                $depID = $emp_texts->depID;
                if ($emp_texts->name_status == 1) {
                    $depName = 'Departmen of ' . $emp_texts->department_name;
                } else {
                    $depName = $emp_texts->department_name;
                }

                $fac = $emp_texts->faculty_name;
                $fAppDate = $emp_texts->initial_appointment_date;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
                $salScale = $emp_texts->salary_scale_txt;
                $increDate = $emp_texts->incre_date;
                $bSal = sprintf("%.2f", $emp_texts->present_sal_step);
                $increAmount = sprintf("%.2f", $emp_texts->incre_amount);
                $newBSal = sprintf("%.2f", $emp_texts->new_sal_step);
                $lock = $emp_texts->lock;
                $depId = $emp_texts->depID;
                $refNo = $emp_texts->ref_no;
                $increYear = $emp_texts->year;
                $serDate = $emp_texts->self_eval_date;
                $facID = $emp_texts->facID;
            }


            $ma_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.check_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.check_date'
                )
                ->where('increments_process_acs.id', $tbID)
                ->get();

            if (count($ma_text) > 0) {
                foreach ($ma_text as $ma_texts) {
                    $ma_name = $ma_texts->title . ' ' . $ma_texts->initials . ' ' . $ma_texts->last_name;
                    $ma_date = $ma_texts->check_date;
                }
            } else {
                $ma_name = "";
                $ma_date = "";
            }

            $ex_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.ex_forward_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.ex_position')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'increments_process_acs.ex_forward_date'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($ex_text) > 0) {
                foreach ($ex_text as $ex_texts) {
                    $ex_name = $ex_texts->title . ' ' . $ex_texts->initials . ' ' . $ex_texts->last_name;
                    $ex_position = $ex_texts->position;
                    $ex_date = $ex_texts->ex_forward_date;
                }
            } else {
                $ex_name = "";
                $ex_position = "";
                $ex_date = "";
            }

            $ser_part1 = increAcSerMain::where('incre_id', '=', $tbID)
                ->get();
            if (count($ser_part1) > 0) {
                foreach ($ser_part1 as $ser_part1s) {
                    $under_lect_week = $ser_part1s->under_lect_conduct_week;
                    $under_lect_year = $ser_part1s->under_lect_conduct_year;
                    $post_lect_week = $ser_part1s->post_lect_conduct_week;
                    $post_lect_year = $ser_part1s->post_lect_conduct_year;
                    $under_tutor_week = $ser_part1s->under_tutor_conduct_week;
                    $under_tutor_year = $ser_part1s->under_tutor_conduct_year;
                    $post_tutor_weeek = $ser_part1s->post_tutor_conduct_week;
                    $post_tutor_year = $ser_part1s->post_tutor_conduct_year;
                    $under_pract_week = $ser_part1s->under_practical_conduct_week;
                    $under_pract_year = $ser_part1s->under_practical_conduct_year;
                    $post_pract_week = $ser_part1s->post_practical_conduct_week;
                    $post_pract_year = $ser_part1s->post_practical_conduct_year;
                    $under_project_sup = $ser_part1s->under_project_supervised;
                    $post_project_sup = $ser_part1s->post_project_supervised;
                }
            } else {
                $under_lect_week = "";
                $under_lect_year = "";
                $post_lect_week = "";
                $post_lect_year = "";
                $under_tutor_week = "";
                $under_tutor_year = "";
                $post_tutor_weeek = "";
                $post_tutor_year = "";
                $under_pract_week = "";
                $under_pract_year = "";
                $post_pract_week = "";
                $post_pract_year = "";
                $under_project_sup = "";
                $post_project_sup = "";
            }

            $maxSalaryRecord = Increment::where('emp_no', $emp_no)->orderBy('basic_sal', 'desc')->first();
            if ($maxSalaryRecord) {
                $incre_last_date = $maxSalaryRecord->effective_date;

                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->where('leave_academic_summeries.starting_date', '>=', $incre_last_date)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            } else {
                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            }

            $ser_part2 = increAcSerOther::where('incre_id', '=', $tbID)->get();
        } else {
            $incre_id = 0;
            $emp_no = "";
            $name = "";
            $appDate = "";
            $depID = 0;
            $depName = "";
            $fac = "";
            $fAppDate = "";
            $desig = "";
            $salScale = "";
            $increDate = "";
            $bSal = "";
            $increAmount = "";
            $newBSal = "";

            $lock = 0;
            $depId = 0;
            $refNo = '';
            $increYear = "";
            $ma_name = "";
            $ma_date = "";

            $ex_name = "";
            $ex_position = "";
            $ex_date = "";

            $serDate = "";

            $under_lect_week = "";
            $under_lect_year = "";
            $post_lect_week = "";
            $post_lect_year = "";
            $under_tutor_week = "";
            $under_tutor_year = "";
            $post_tutor_weeek = "";
            $post_tutor_year = "";
            $under_pract_week = "";
            $under_pract_year = "";
            $post_pract_week = "";
            $post_pract_year = "";
            $under_project_sup = "";
            $post_project_sup = "";

            $ser_part2 = array();

            $facID = 0;

            $leave_text = array();
        }


        $data = [
            'name' => $name,
            'desig' => $desig,
            'appDate' => $appDate,
            'fAppDate' => $fAppDate,
            'depName' => $depName,
            'salScale' => $salScale,
            'bSal' => $bSal,
            'increDate' => $increDate,
            'increAmount' => $increAmount,
            'newBSal' => $newBSal,
            'leave_text' => $leave_text,
            'ma_name' => $ma_name,
            'ma_date' => $ma_date,
            'ex_name' => $ex_name,
            'ex_position' => $ex_position,
            'ex_date' => $ex_date,
            'under_lect_week' => $under_lect_week,
            'under_lect_year' => $under_lect_year,
            'post_lect_week' => $post_lect_week,
            'post_lect_year' => $post_lect_year,
            'under_tutor_week' => $under_tutor_week,
            'under_tutor_year' => $under_tutor_year,
            'post_tutor_weeek' => $post_tutor_weeek,
            'post_tutor_year' => $post_tutor_year,
            'under_pract_week' => $under_pract_week,
            'under_pract_year' => $under_pract_year,
            'post_pract_week' => $post_pract_week,
            'post_pract_year' => $post_pract_year,
            'under_project_sup' => $under_project_sup,
            'post_project_sup' => $post_project_sup,
            'ser_part2' => $ser_part2,
            'serDate' => $serDate,
            'facID' => $facID,
            'fac' => $fac,

        ];

        $pdf = FacadePdf::loadView('admin.increment.Academic.ser_print', $data)->setPaper('a4');

        // return $pdf->stream('increment_reprot.pdf');
        // Render the PDF (first pass to calculate pages)
        $pdf->render();

        // Now, get the total number of pages
        $canvas = $pdf->getCanvas();
        $canvas->page_text(510, 820, "Page {PAGE_NUM} of {PAGE_COUNT}", null, 10, array(0, 0, 0)); // You can adjust the position and font here

        // Return the generated PDF
        return $pdf->stream('increment_reprot.pdf');
    }

    public function selfAssessmentOpen(Request $request)
    {
        $appID = $request->appID;
        $refID = $request->RefNo;
        $empNo = $request->empNo;

        return view('admin.increment.Academic.as_ser', compact(
            'appID',
            'refID',
            'empNo'
        ));
    }

    public function sel_assessment_store(Request $request)
    {
        if ($request->task != null) {
            for ($i = 0; $i < count($request->task); $i++) {
                $sect5_text = new increAsSelfAssessment();
                $sect5_text->incre_id = $request->appID;
                $sect5_text->incre_ref_no = $request->RefNo;
                $sect5_text->task = $request->task[$i];
                $sect5_text->assessment1 = $request->sect1[$i];
                $sect5_text->assessment2 = $request->sect2[$i];
                $sect5_text->assessment3 = $request->sect3[$i];
                $sect5_text->assessment4 = $request->sect4[$i];
                $sect5_text->assessment5 = $request->sect5[$i];
                $sect5_text->user_id = $request->empNo;
                $sect5_text->save();
            }
        }

        $s_test = incrementsProcessAc::where('id', '=', $request->appID)
            ->update([
                'self_eval_status' => 2,
                'self_eval_date' => today(),
                'head_status' => 1
            ]);
        //comment

        $emp_text = Employee::join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->select(
                'employees.initials',
                'employees.last_name',
                'designations.designation_name',
                'departments.id as depID',
                'departments.department_name',
                'categories.category_name',
                't.category_name as title',
            )
            ->where('employees.employee_no', $request->empNo)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $depID = $emp_texts->depID;
                $depName = $emp_texts->department_name;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
            }
        } else {
            $name = '';
            $depName = '';
            $desig = '';
            $depID = 0;
        }

        $hod_text = DepartmentHead::join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->select(
                't.category_name as title',
                'employees.initials',
                'employees.last_name',
                'department_heads.email'
            )
            ->where('department_heads.department_id', '=', $depID)
            ->get();

        if (count($hod_text) > 0) {
            foreach ($hod_text as $hod_texts) {
                $hod_name = $hod_texts->title . ' ' . $hod_texts->initials . ' ' . $hod_texts->last_name;
                $hod_email = $hod_texts->email;
            }
        } else {
            $hod_name = '';
            $hod_email = '';
        }


        //send email
        $emailData = [
            'name' =>  $name,
            'headName' => $hod_name,
            'depName' => $depName,
            'desig' => $desig,

        ];

        $mail = new acIncreForwardToHodMail($emailData);
        //sending email
        Mail::to($hod_email)->send($mail);
        // Mail::to('<EMAIL>')->send($mail);

        $notification = array(
            'message' => 'Self evaluation report submitted successfully.',
            'alert-type' => 'success'
        );

        return redirect()->route('ac.incre.selfEval.page1.open')->with($notification);
    }
}
