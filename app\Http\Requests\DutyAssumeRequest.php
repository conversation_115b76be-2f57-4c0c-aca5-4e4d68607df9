<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DutyAssumeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    public function rules()
    {

        $rules['nic'] = ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m'];
        $rules['title'] = 'required';

        if ($this->get('recom') == 2) {

            $rules['name_with_initials'] = 'required|regex:/^([A-Z]\.)+$/';
        }

        $rules['name_denoted_by_initials'] = 'required';
        $rules['last_name'] = 'required';

        if ($this->get('recom') == 2) {

            $rules['telephone_mobile'] = 'required|max:12|min:10';
            $rules['telephone_residence'] = 'nullable|max:12|min:10';
        }

        $rules['email_address'] = 'required|email:rfc,dns';
        $rules['date_of_birth'] = 'required|date|before:-18 years';

        if ($this->get('recom') == 2) {

            $rules['gender_id'] = 'required';
        }

        $rules['civil_status'] = 'required';
        $rules['permanent_address_line1'] = 'required';
        $rules['permanent_address_city'] = 'required';
        $rules['postal_address_line1'] = 'required';
        $rules['postal_address_city'] = 'required';
        $rules['citizen_sri_lanka_obtained'] = 'required';
        $rules['citizen_sri_lanka_obtained'] = 'required';

        if ($this->get('citizen_sri_lanka_obtained') == 'By Registration') {

            $rules['citizen_registration_no'] = 'required';
        }
        $rules['academic_qualification'] = 'required';

        /************************************************** */


        if ($this->get('recom') == 2) {
        $rules['file_reference_number'] = 'required';
        $rules['faculty_id'] = 'required';
        $rules['department_id'] = 'required';
        $rules['carder_faculty_id'] = 'required';
        $rules['carder_department_id'] = 'required';

        $rules['current_basic_salary'] = ['required', 'regex:/^\d{1,13}(\.\d{1,4})?$/'];
        $rules['duty_assume_date'] = 'required|date|before_or_equal:today';

        if ($this->get('designation_category') != 138) {

            $rules['salary_termination_date'] = 'required|date|after:today';
            $rules['salary_payment_type'] = 'required';
        }

       }



        return $rules;
    }

    public function messages()
    {
        return [
            'nic.required' => 'The employee NIC is required.',
            'nic.regex' => 'The NIC format is invalid.',

            'title.required' => 'The title is required.',

            'name_with_initials.required' => 'The initials are required.',
            'name_with_initials.regex' => 'The initials format is incorrect.',

            'name_denoted_by_initials.required' => 'The name denoted by initials is required.',

            'last_name.required' => 'The last name is required.',

            'telephone_mobile.required' => 'The mobile number is required.',
            'telephone_mobile.min' => 'The mobile number must be at least 10 digits.',
            'telephone_mobile.max' => 'The mobile number must not exceed 12 digits.',

            'telephone_residence.min' => 'The residence number must be at least 10 digits.',
            'telephone_residence.max' => 'The residence number must not exceed 12 digits.',

            'email_address.required' => 'The email address is required.',
            'email_address.email' => 'The email address must be a valid email.',

            'date_of_birth.required' => 'The date of birth is required.',
            'date_of_birth.date' => 'The date of birth must be a valid date.',
            'date_of_birth.before' => 'You must be at least 18 years old.',

            'gender_id.required' => 'The gender is required.',

            'civil_status.required' => 'The civil status is required.',

            'permanent_address_line1.required' => 'The permanent address is required.',
            'permanent_address_city.required' => 'The permanent city is required.',

            'postal_address_line1.required' => 'The postal address is required.',
            'postal_address_city.required' => 'The postal city is required.',

            'citizen_sri_lanka_obtained.required' => 'The method of obtaining citizenship is required.',

            'citizen_registration_no.required' => 'The citizen registration number is required when citizenship is by registration.',

            'file_reference_number.required' => 'The file reference number is required.',

            'faculty_id.required' => 'The faculty is required.',
            'department_id.required' => 'The department is required.',
            'carder_faculty_id.required' => 'The carder faculty is required.',
            'carder_department_id.required' => 'The carder department is required.',

            'current_basic_salary.required' => 'The current basic salary is required.',
            'current_basic_salary.regex' => 'The current basic salary format is invalid.',

            'duty_assume_date.required' => 'The duty assume date is required.',
            'duty_assume_date.date' => 'The duty assume date must be a valid date.',
            'duty_assume_date.before' => 'The duty assume date cannot be in the future.',

            'salary_termination_date.date' => 'The salary termination date must be a valid date.',

            'academic_qualification.required' => 'The highest educational qualification is required.',
        ];
    }
}
