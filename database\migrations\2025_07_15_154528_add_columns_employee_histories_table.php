<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('employee_histories', function (Blueprint $table) {
            $table->integer('tin_no')->default(0)->nullable()->after('welcome_mail');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('employee_histories', function (Blueprint $table) {
            $table->dropColumn('tin_no');
        });
    }
};
