@extends('admin.admin_master')
@section('admin')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Change Employee Status </h1>
                </div><!-- /.col -->
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Status Change</li>
                    </ol>
                </div><!-- /.col -->
            </div><!-- /.row -->
        </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">

                    {{-- <div class="card">
                    <div class="card-header">
                        <form class="form-inline md-form mr-auto mb-4" action="{{ route('main.branch.change.view')}}" method="POST">
                            @csrf
                            <div class="row mt-3 ml-2">

                                <label>Emp No</label>
                                &nbsp;&nbsp;
                                <input class="form-control mr-sm-2" type="text" value="{{$pass_employee_no}}" name="employee_no" aria-label="Search">

                                <input type="submit" name="search" value="Search" class="btn btn-primary">

                                <a href="{{ route('main.branch.change.view') }}" class="btn btn-danger ml-2">Reset</a>

                            </div>

                        </form>
                        <div class="row ml-3 mb-2">
                            <span class="text-danger">@error('emp_no'){{$message}}@enderror</span>
                        </div>
                    </div>


                </div> --}}

                    <form action="{{ route('status.update.store') }}" method="POST">
                        <input name="emp_no" type="hidden" value="{{ $empData->employee_no }}">
                        <input name="nic" type="hidden" value="{{ $empData->nic }}">
                        @csrf
                        @if ($empData->employee_no != '')
                            <div class="card">
                                <div class="card-body">
                                    <h5>Employee Details</h5>
                                    <hr>
                                    <div class="row">
                                        <div class="col-10">
                                            <div class="row">
                                                <div class="col-3 p-1">
                                                    <label>Emp. No.</label>
                                                </div>
                                                <div class="col-4 p-1">
                                                    <p><b>:</b> {{ $empData->employee_no }}</p>
                                                </div>
                                                {{-- <div class="col-2 p-1">
                                            <label>NIC.</label>
                                        </div>
                                        <div class="col-3 p-1">
                                            <p><b>:</b> {{ $nic }}</p>
                                        </div> --}}
                                            </div>
                                            <div class="row">
                                                <div class="col-3 p-1">
                                                    <label>Name</label>
                                                </div>
                                                <div class="col-9 p-1">
                                                    <p><b>:</b>
                                                        {{ $empData->getNameTitle->category_name . ' ' . $empData->initials . ' ' . $empData->last_name }}
                                                    </p>
                                                </div>

                                            </div>
                                            <div class="row">
                                                <div class="col-3 p-1">
                                                    <label>Department</label>
                                                </div>
                                                <div class="col-9 p-1">
                                                    <p><b>:</b> {{ $empData->getDepartmentName->department_name }}</p>
                                                </div>

                                            </div>
                                            <div class="row">
                                                <div class="col-3 p-1">
                                                    <label>Faculty</label>
                                                </div>
                                                <div class="col-9 p-1">
                                                    <p><b>:</b> {{ $empData->getFacultyName->faculty_name }} </p>
                                                </div>

                                            </div>
                                            <div class="row">
                                                <div class="col-3 p-1">
                                                    <label>Current Designation</label>
                                                </div>
                                                <div class="col-9 p-1">
                                                    <p><b>:</b> {{ $empData->designationName->designation_name }} </p>
                                                </div>

                                            </div>

                                        </div>
                                        <div class="col-2">
                                            <div class="text-center">
                                                @if (file_exists(public_path('backend/dist/img/profile/' . $empData->employee_no . '.jpg')))
                                                    <img src="{{ asset('backend/dist/img/profile/' . $empData->employee_no . '.jpg') }}"
                                                        class="img-responsive mx-auto d-block img-fluid border border-dark img-thumbnail"
                                                        style="width: 150px;" alt="profile-image">
                                                @else
                                                    <img src="{{ asset('backend/dist/img/profile.jpg') }}"
                                                        class="img-responsive mx-auto d-block img-fluid border border-dark img-thumbnail"
                                                        style="width: 150px;" alt="profile-image">
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Current Employment Status:</label>
                                            <select name="old_employee_status_id" id="old_employee_status_id"
                                                class="select2bs5" style="width: 100%">
                                                @foreach ($employeeStatusIds as $employeeStatusId)
                                                    @if ($empData->employee_status_id == $employeeStatusId->id)
                                                        <option value="{{ $employeeStatusId->id }}"
                                                            {{ $employeeStatusId->id == $empData->employee_status_id ? 'selected' : '' }}>
                                                            {{ ucfirst($employeeStatusId->category_name) }}</option>
                                                    @endif
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Current Status Type:</label>
                                            <select name="old_employee_status_type_id" id="old_employee_status_type_id"
                                                class="select2bs5" style="width: 100%">

                                                @foreach ($employeeStatusTypes as $employeeStatusType)
                                                    @if ($empData->employee_status_type_id == $employeeStatusType->id)
                                                        <option value="{{ $employeeStatusType->id }}" selected>
                                                            {{ ucfirst($employeeStatusType->category_name) }}</option>
                                                    @endif
                                                @endforeach

                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Current Effective Date:</label>
                                            <input type="text" class="form-control datetimepicker-input" data-target="#date_of_birth" name="old_effective_date" id="date_of_birth_val" value="" placeholder="Ex:- 01-Jan-2000" data-target="#date_of_birth" data-toggle="datetimepicker" readonly>
                                        </div>
                                        <div class="form-group">
                                            <input type="submit" name="submit" value="Change" class="btn btn-primary">
                                            <a href="{{ route('employee.updation', encrypt($empData->employee_no)) }}"
                                                class="btn btn-warning btn-md ml-2">Cancel</a>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>New Employment Status:</label>
                                            <select name="employee_status_id" id="employee_status_id"
                                                class="select2bs5" style="width: 100%">
                                                <option value="" selected disabled>Select Employee Status</option>
                                                @foreach ($employeeStatusIds as $employeeStatusId)
                                                        <option value="{{ $employeeStatusId->id }}">
                                                            {{ ucfirst($employeeStatusId->category_name) }}</option>
                                                @endforeach
                                            </select>
                                            <span class="text-danger">@error('employee_status_id'){{$message}}@enderror</span>
                                        </div>
                                        <div class="form-group">
                                            <label>New Status Type:</label>
                                            <select name="employee_status_type_id" id="employee_status_type_id"
                                                class="select2bs5" style="width: 100%">

                                                {{-- @foreach ($employeeStatusTypes as $employeeStatusType)

                                                        <option value="{{ $employeeStatusType->id }}" selected>
                                                            {{ ucfirst($employeeStatusType->category_name) }}</option>

                                                @endforeach --}}

                                            </select>
                                            <span class="text-danger">@error('employee_status_type_id'){{$message}}@enderror</span>
                                        </div>
                                        <div class="form-group">
                                            <label>New Effective Date:</label>
                                            <div class="input-group date" id="date_of_birth_new" data-target-input="nearest">
                                                <input type="text" class="form-control datetimepicker-input" data-target="#date_of_birth_new" name="effective_date" id="date_of_birth_new_val" value="{{ old('effective_date') }}" placeholder="Ex:- 01-Jan-2000" data-target="#date_of_birth_new" data-toggle="datetimepicker" readonly>
                                                <div class="input-group-append" data-target="#date_of_birth_new" data-toggle="datetimepicker">
                                                    <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                                </div>

                                            </div>
                                            <span class="text-danger">@error('effective_date'){{$message}}@enderror</span>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </section>
    <script type="text/javascript">

        $(document).ready(function () {

            $('select[name="employee_status_id"]').on('change', function(){
                var employee_status_id = $(this).val();
                  if(employee_status_id) {
                      $.ajax({
                          url: "{{  url('/employee/status/type/load/ajax') }}/"+employee_status_id,
                          type:"GET",
                          dataType:"json",
                            success:function(data) {
                                var d =$('select[name="employee_status_type_id"]').empty();
                                $.each(data, function(key, value){
                                $('select[name="employee_status_type_id"]').append('<option value="'+ value.id +'">' + value.category_name + '</option>');
                             });
                              },
                         });
                      }
          });
        });
    </script>
@endsection
