<?php

namespace App\Console\Commands;

use App\Models\Department;
use App\Models\DepartmentHead;
use App\Models\Faculty;
use App\Models\FacultyDean;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DepartmentHeadDeativateCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dhead:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $data = DepartmentHead::whereDate('end_date', '<', date('Y-m-d'))->whereDate('end_date', '!=', '1970-01-01')->where('active_status', 1)->get();

        foreach ($data as $heads) {

            $empNo = $heads->emp_no;
            $deptId = $heads->department_id;

            $facultyDeanIdentifier = Faculty::where('faculty_dean_office', $deptId)->count();

            if ($facultyDeanIdentifier == 1) {

                $faculty = Faculty::where('faculty_dean_office', $deptId)->first();
                $facultyId = $faculty->id;

                $departmentHeads = DepartmentHead::whereDate('end_date', '<', date('Y-m-d'))
                    ->whereDate('end_date', '!=', '1970-01-01')
                    ->get();

                foreach ($departmentHeads as $departmentHead) {
                    $departmentHead->active_status = 0;
                    $departmentHead->save();
                }

                $facultyDeans = FacultyDean::whereDate('end_date', '<', date('Y-m-d'))
                    ->whereDate('end_date', '!=', '1970-01-01')
                    ->get();

                foreach ($facultyDeans as $facultyDean) {
                    $facultyDean->active_status = 0;
                    $facultyDean->save();
                }


                /******************************************************************************************************* */

                if (DepartmentHead::where('emp_no', $empNo)->count() > 1 && FacultyDean::where('emp_no', $empNo)->count() > 1) {
                } elseif (DepartmentHead::where('emp_no', $empNo)->count() > 1 && FacultyDean::where('emp_no', $empNo)->count() == 1) {

                    $user = User::where('employee_no', $empNo)->first();
                    $user->removeRole('dean');
                } else {

                    $user = User::where('employee_no', $empNo)->first();

                    if ($user->hasAnyRole(['head', 'dean'])) {

                        $user->removeRole('head');
                        $user->removeRole('dean');
                    }
                }

                /*********************************************************************************** */


                $department = Department::find($deptId);
                if ($department) {
                    $department->head_email = '';
                    $department->head_user_id = 0;
                    $department->save();
                }


                $faculty = Faculty::find($facultyId);
                if ($faculty) {
                    $faculty->dean_email = '';
                    $faculty->dean_user_id = 0;
                    $faculty->save();
                }
            } else {

                $departmentHeads = DepartmentHead::whereDate('end_date', '<', date('Y-m-d'))
                    ->whereDate('end_date', '!=', '1970-01-01')
                    ->get();

                foreach ($departmentHeads as $departmentHead) {
                    $departmentHead->active_status = 0;
                    $departmentHead->save();
                }


                /****************************************************************************************** */

                if (DepartmentHead::where('emp_no', $empNo)->count() > 1) {

                    $user = User::where('employee_no', '=', $empNo)->first();

                    if ($user) {
                        $user->status_id = 1;
                        $user->save();
                    }
                } else {

                    // record exists
                    $user = User::where('employee_no', $empNo)->first();

                    if ($user->hasRole(['head'])) {

                        $user->removeRole('head');
                    }
                }

                /****************************************************************************************** */
                $department = Department::find($deptId);

                if ($department) {
                    $department->head_email = '';
                    $department->head_user_id = 0;
                    $department->save();
                }
            }
        }

        return 0;
    }
}
