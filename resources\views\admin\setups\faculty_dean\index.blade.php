@extends('admin.admin_master')
@section('admin')

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Faculty Deans List</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="#">Home</a></li>
                    <li class="breadcrumb-item active">Faculty Deans</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <!-- /.box-header -->
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example1" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Fac ID</th>
                                        <th width="20%">Faculty Name</th>
                                        <th>Faculty Dean</th>
                                        {{-- <th>Dean Email</th> --}}
                                        <th>Appointment Type</th>
                                        <th>Appointment Date</th>
                                        <th>Termination Date</th>
                                        @role('super-admin')
                                        <th>Role</th>
                                        @endrole
                                        <th>Status</th>
                                        <th>Assign Person</th>
                                        <th width="12%" data-priority="1">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($facultyDeans as $key => $facultyDean)
                                    @php
                                    $deanRoleCount = App\Models\User::join(
                                        'employees',
                                        'employees.employee_no',
                                        '=',
                                        'users.employee_no',
                                    )
                                        ->join(
                                            'model_has_roles',
                                            'model_has_roles.model_id',
                                            '=',
                                            'users.id',
                                        )
                                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                                        ->select(
                                            'users.id',
                                            'users.status_id',
                                            'employees.initials',
                                            'employees.last_name',
                                            'employees.employee_no',
                                        )
                                        ->selectRaw('GROUP_CONCAT(roles.name) as roles')
                                        ->where('users.id', '!=', 1)
                                        ->where('employees.employee_no', $facultyDean->emp_no)
                                        ->where(function ($query) {
                                            $query->where('roles.id', 9);
                                        })
                                        ->groupBy(
                                            'users.id',
                                            'users.status_id',
                                            'employees.initials',
                                            'employees.last_name',
                                            'employees.employee_no',
                                        )
                                        ->orderBy('users.id')
                                        ->count();

                                @endphp
                                    @auth
                                    @if (Auth()->user()->employee_no == $facultyDean->added_user_id)
                                    <tr style="background-color: #fffccd;">
                                    @else
                                    <tr>
                                    @endif
                                    @endauth
                                        <td>{{ $facultyDean->faculty_id }}</td>
                                        <td>{{ ucfirst($facultyDean->facultyName->faculty_name) }}</td>
                                        <td>{{ $facultyDean->employeeName->initials }} {{ $facultyDean->employeeName->last_name }}</td>
                                        {{-- <td>{{ $facultyDean->email }}</td> --}}
                                        <td>{{ $facultyDean->AppointmentTypeName->category_name }}</td>
                                        <td>{{ date("d-M-Y", strtotime($facultyDean->start_date)) }}</td>
                                        <td>{{ $facultyDean->end_date != '1970-01-01' ?  date("d-M-Y", strtotime($facultyDean->end_date)) : '-' }}</td>
                                        @role('super-admin')
                                            <td>
                                                @if($deanRoleCount == 1)
                                                <span class="badge badge-pill badge-success">Dean Role Attached</span>
                                                @else
                                                <span class="badge badge-pill badge-danger">Only User Role</span>
                                                @endif
                                            </td>
                                        @endrole
                                        <td>
                                            @if($facultyDean->active_status == 1)
                                            <span class="badge badge-pill badge-success">Active</span>
                                            @else
                                            <span class="badge badge-pill badge-danger">Deactive</span>
                                            @endif
                                            </td>
                                            <td>{{ $facultyDean->Assignuser->initials }} {{ $facultyDean->Assignuser->last_name }}</td>
                                        <td>
                                            @role('super-admin|administrator|est-head|cc')
                                            <a href="{{ route('employee.show', encrypt($facultyDean->emp_no)) }}" class="btn btn-sm btn-primary">Show <i class="fa fa-eye"></i></a>
                                            @endrole
                                            @role('sc')
                                            @auth
                                                @if (Auth()->user()->employee_no == $facultyDean->added_user_id)
                                                <a href="{{ route('employee.show', encrypt($facultyDean->emp_no)) }}" class="btn btn-sm btn-primary">Show <i class="fa fa-eye"></i></a>
                                                @endif
                                            @endauth
                                            @endrole
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection
