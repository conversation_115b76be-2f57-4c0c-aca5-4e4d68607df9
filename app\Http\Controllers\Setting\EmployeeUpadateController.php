<?php

namespace App\Http\Controllers\Setting;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\City;
use App\Models\Department;
use App\Models\DepartmentSub;
use App\Models\Designation;
use App\Models\Employee;
use App\Models\EmployeeChangeHistory;
use App\Models\Faculty;
use App\Models\InternalTransfer;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class EmployeeUpadateController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }



    public function employeeChange()
    {

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $data = EmployeeChangeHistory::join('employees', 'employee_change_histories.emp_no', '=', 'employees.employee_no')
                ->join('categories as title', 'title.id', '=', 'employees.title_id')
                ->join('categories as change_type', 'change_type.id', '=', 'employee_change_histories.type')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    'employee_change_histories.*',
                    'title.category_name as title_name',
                    'change_type.category_name as change_type_name'
                )
                ->where('employee_change_histories.approvability', 1)
                ->where('employee_change_histories.approved_status', 0)
                ->orderBy('employee_change_histories.id', 'DESC')
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $data = EmployeeChangeHistory::join('employees', 'employee_change_histories.emp_no', '=', 'employees.employee_no')
                    ->join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('categories as change_type', 'change_type.id', '=', 'employee_change_histories.type')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'employee_change_histories.*', 'title.category_name as title_name', 'change_type.category_name as change_type_name')
                    ->where('employee_change_histories.approvability', 1)
                    ->where('employee_change_histories.approved_status', 0)
                    ->where('employees.main_branch_id', 52)
                    ->orderBy('employee_change_histories.id', 'DESC')
                    ->get();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $data = EmployeeChangeHistory::join('employees', 'employee_change_histories.emp_no', '=', 'employees.employee_no')
                    ->join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('categories as change_type', 'change_type.id', '=', 'employee_change_histories.type')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'employee_change_histories.*', 'title.category_name as title_name', 'change_type.category_name as change_type_name')
                    ->where('employee_change_histories.approvability', 1)
                    ->where('employee_change_histories.approved_status', 0)
                    ->where('employees.main_branch_id', 53)
                    ->orderBy('employee_change_histories.id', 'DESC')
                    ->get();
            }
        }
        //dd($data);
        return view('admin.employee.change_approval', compact('data'));
    }

    public function employeeChangeApproval($id)
    {

        $data = EmployeeChangeHistory::find($id);
        $data->approved_status = 1;
        $data->approved_user_id = auth()->user()->employee_no;
        $data->approved_date = date("Y-m-d");
        $data->save();

        if ($data->type == 293) {

            $newRecord = json_decode($data->new_record, true);
            $newNIC = $newRecord[0]['key'] ?? null;

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => $newNIC]);

            $nicData = json_decode($empDetails->body(), true);

            $employee = Employee::find($data->emp_no);
            $employee->nic = strtoupper($newNIC);
            $employee->nic_old = $nicData['oldnic'] ?? null;
            $employee->nic_new = $nicData['newnic'] ?? null;
            $employee->active_nic = $nicData['activenic'] ?? null;
            $employee->dob_gen = $nicData['dob'] ?? null;
            $employee->save();
        }

        if ($data->type == 340) {

            $newRecord = json_decode($data->new_record, true);
            $newDesignation = $newRecord[0]['key'] ?? null;

            $employee = Employee::find($data->emp_no);
            $employee->designation_id = $newDesignation;
            $employee->save();
        }

        if ($data->type == 341) {

            $newRecord = json_decode($data->new_record, true);
            $newCurrentBasicSalary = $newRecord[0]['key'] ?? null;

            $employee = Employee::find($data->emp_no);
            $employee->current_basic_salary = $newCurrentBasicSalary;
            $employee->save();
        }

        if ($data->type == 342) {

            $newRecord = json_decode($data->new_record, true);
            $newStatus = $newRecord[0]['key'] ?? null;
            $newStatusType = $newRecord[1]['key'] ?? null;
            $newEffectiveDate = $newRecord[2]['key'] ?? null;

            if ($newStatus == 110) {

                if ($newStatusType == 112) {

                    $employee = Employee::find($data->emp_no);
                    $employee->employee_status_id = $newStatus;
                    $employee->employee_status_type_id = $newStatusType;
                    $employee->salary_termination_date_1 = NULL;
                    $employee->salary_termination_date_2 = NULL;
                    $employee->save();

                } else {

                    $employee = Employee::find($data->emp_no);
                    $employee->employee_status_id = $newStatus;
                    $employee->employee_status_type_id = $newStatusType;
                    $employee->salary_termination_date_2 = $newEffectiveDate;
                    $employee->save();
                }

            } elseif ($newStatus == 111) {

                $employee = Employee::find($data->emp_no);
                $employee->employee_status_id = $newStatus;
                $employee->employee_status_type_id = $newStatusType;
                $employee->salary_termination_date_1 = $newEffectiveDate;
                $employee->save();
            }
        }

        if ($data->type == 346) {

            $newRecord = json_decode($data->new_record, true);
            $newDepartment = $newRecord[0]['key'] ?? null;
            $newEffectiveData = $newRecord[1]['key'] ?? null;
            $newRemark = $newRecord[2]['key'] ?? null;
            //employee updation
            $employee = Employee::find($data->emp_no);
            $employee->department_id = $newDepartment;
            $employee->faculty_id = $this->employeefacultyGet($newDepartment);
            $employee->carder_department_id = $newDepartment;
            $employee->carder_faculty_id = $this->employeefacultyGet($newDepartment);
            $employee->save();
            //internal transfer record insertion
            $NonAceInterTransfer = new InternalTransfer();
            $NonAceInterTransfer->emp_no = $data->emp_no;
            $NonAceInterTransfer->transfer_date = date("Y-m-d", strtotime($newEffectiveData));
            $NonAceInterTransfer->dep_id = $newDepartment;
            $NonAceInterTransfer->descriptions = $newRemark;
            $NonAceInterTransfer->user_id = auth()->user()->employee_no;
            $NonAceInterTransfer->save();
        }



        $notification = array(
            'message' => 'Change request approved successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('employee.change')->with($notification);
    }

    public function employeeChangeReject($id)
    {

        $data = EmployeeChangeHistory::find($id);
        $data->approved_status = 2;
        $data->approved_user_id = auth()->user()->employee_no;
        $data->approved_date = date("Y-m-d");
        $data->save();

        $notification = array(
            'message' => 'Change request rejected successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('employee.change')->with($notification);
    }

    public function nicUpdateView($id)
    {

        $search_emp_no = decrypt($id);


        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot Update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.nic_change", compact('empData'));
        }
    }

    public function nicUpdateStore(Request $request)
    {

        if (!isset($request->nic)) {

            $notification = array(
                'message' => 'Please enter new NIC',
                'alert-type' => 'error'
            );

            return redirect()->route('nic.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m']

                ],
                [
                    'nic.required' => 'the employee nic required',

                ]
            );

            //$employee = Employee::find($request->emp_no);

            if ($request->old_nic !== strtoupper($request->nic)) {

                $perRequest = EmployeeChangeHistory::where('emp_no', $request->emp_no)->where('type', 293)->where('approved_status', 0)->first();
                if ($perRequest) {

                    $notification = array(
                        'message' => 'You already send a request for NIC update',
                        'alert-type' => 'error'
                    );

                    return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
                }

                $data = new EmployeeChangeHistory();
                $data->type = 293;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => strtoupper($request->old_nic), 'value' => strtoupper($request->old_nic), 'text' => 'Old NIC')]);
                $data->new_record = json_encode([array('key' => strtoupper($request->nic), 'value' => strtoupper($request->nic), 'text' => 'New NIC')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 1;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();

                $notification = array(
                    'message' => 'NIC Update Send for approval',
                    'alert-type' => 'info'
                );

                return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
            }


            $notification = array(
                'message' => 'You entered same NIC',
                'alert-type' => 'error'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function nameUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $categories = $this->getCategories([5]);
        $titles = $categories->where('category_type_id', '5');

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.name_change", compact('empData', 'titles'));
        }
    }

    public function nameUpdateStore(Request $request)
    {

        if (!isset($request->titel) && !isset($request->initials) && !isset($request->name_denoted_by_initials) && !isset($request->last_name)) {


            $notification = array(
                'message' => 'Please enter new value/s',
                'alert-type' => 'error'
            );

            return redirect()->route('name.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'titel' => 'nullable',
                    'initials' => ['nullable', 'regex:/^([A-Z]\.)+$/'],
                    'name_denoted_by_initials' => 'nullable',
                    'last_name' => 'nullable'

                ],
                [
                    'titel.required' => 'the title required',
                    'initials.required' => 'the name initials required',
                    'initials.regex' => 'name initials incorrect formatted',
                    'name_denoted_by_initials.required' => 'the initials denoted name required',
                    'last_name.required' => 'the last name required',

                ]
            );

            //dd($request->titel);

            $employee = Employee::find($request->emp_no);

            if ($request->old_titel !== $request->titel && !is_null($request->titel)) {

                $employee->title_id = $request->titel;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 294;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_titel, 'value' => $this->getCategoriesName($request->old_titel), 'text' => 'Old Title')]);
                $data->new_record = json_encode([array('key' => $request->titel, 'value' => $this->getCategoriesName($request->titel), 'text' => 'New Title')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_initials !== $request->initials && !is_null($request->initials)) {

                $employee->initials = $request->initials;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 295;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_initials, 'value' => $request->old_initials, 'text' => 'Old Initials')]);
                $data->new_record = json_encode([array('key' => $request->initials, 'value' => $request->initials, 'text' => 'New Initials')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_name_denoted_by_initials !== $request->name_denoted_by_initials && !is_null($request->name_denoted_by_initials)) {

                $employee->name_denoted_by_initials = $request->name_denoted_by_initials;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 296;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_name_denoted_by_initials, 'value' => $request->old_name_denoted_by_initials, 'text' => 'Old Name Denoted By Initials')]);
                $data->new_record = json_encode([array('key' => $request->name_denoted_by_initials, 'value' => $request->name_denoted_by_initials, 'text' => 'New Name Denoted By Initials')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_last_name !== $request->last_name && !is_null($request->last_name)) {

                $employee->last_name = $request->last_name;
                $employee->save();

                $user = User::where('employee_no', $request->emp_no)->first();
                $user->name = $request->last_name;
                $user->save();

                $data = new EmployeeChangeHistory();
                $data->type = 297;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_last_name, 'value' => $request->old_last_name, 'text' => 'Old Last Name')]);
                $data->new_record = json_encode([array('key' => $request->last_name, 'value' => $request->last_name, 'text' => 'New Last Name')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            $notification = array(
                'message' => 'Name updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function mobileUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.mobile_change", compact('empData'));
        }
    }

    public function mobileUpdateStore(Request $request)
    {

        if (!isset($request->mobile_no) && !isset($request->telephone_no) && !isset($request->office_ext) && !isset($request->office_number)) {


            $notification = array(
                'message' => 'Please enter new value/s',
                'alert-type' => 'error'
            );

            return redirect()->route('mobile.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'mobile_no' => 'nullable|max:12|min:10',
                    'telephone_no' => 'nullable|max:12|min:10',
                    'office_ext' => 'nullable|max:4|min:4',
                    'office_number' => 'nullable|max:10|min:10',

                ],
                [
                    'mobile_no.min' => 'you entered phone number in invalid format',
                    'mobile_no.max' => 'you entered phone number in invalid format',
                    'phone_no.max' => 'you entered phone number in invalid format',
                    'phone_no.min' => 'you entered phone number in invalid format',
                    'office_ext.max' => 'you entered office extension in invalid format',
                    'office_ext.min' => 'you entered office extension in invalid format',
                    'office_number.max' => 'you entered office number in invalid format',
                    'office_number.min' => 'you entered office number in invalid format',

                ]
            );

            //dd($request->titel);

            $employee = Employee::find($request->emp_no);

            if ($request->old_mobile_no !== $request->mobile_no && !is_null($request->mobile_no)) {

                $employee->mobile_no = $request->mobile_no;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 298;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_mobile_no, 'value' => $request->old_mobile_no, 'text' => 'Old Mobile No')]);
                $data->new_record = json_encode([array('key' => $request->mobile_no, 'value' => $request->mobile_no, 'text' => 'New Mobile No')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_phone_no !== $request->phone_no && !is_null($request->phone_no)) {

                $employee->phone_no = $request->phone_no;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 299;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_phone_no, 'value' => $request->old_phone_no, 'text' => 'Old Home Phone No')]);
                $data->new_record = json_encode([array('key' => $request->phone_no, 'value' => $request->phone_no, 'text' => 'New Home Phone No')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_office_ext !== $request->office_ext && !is_null($request->office_ext)) {

                $employee->office_ext = $request->office_ext;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 300;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_office_ext, 'value' => $request->old_office_ext, 'text' => 'Old Office Extension')]);
                $data->new_record = json_encode([array('key' => $request->office_ext, 'value' => $request->office_ext, 'text' => 'New Office Extension')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_office_number !== $request->office_number && !is_null($request->office_number)) {

                $employee->office_number = $request->office_number;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 301;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_office_number, 'value' => $request->old_office_number, 'text' => 'Old Office Extension No')]);
                $data->new_record = json_encode([array('key' => $request->office_number, 'value' => $request->office_number, 'text' => 'New Office Extension No')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            $notification = array(
                'message' => 'Phone number updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }


    public function emailUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.email_change", compact('empData'));
        }
    }

    public function emailUpdateStore(Request $request)
    {

        if (!isset($request->email) && !isset($request->personal_email)) {


            $notification = array(
                'message' => 'Please enter new value/s',
                'alert-type' => 'error'
            );

            return redirect()->route('email.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'email' => 'nullable|email:rfc,dns|ends_with:@sjp.ac.lk',
                    'personal_email' => 'nullable|email:rfc,dns',
                ],
                [
                    //'email.required' => 'you entered email in invalid format',
                    'email.email' => 'you entered email in invalid format',
                    'email.ends_with' => 'you entered email in invalid format',
                    'personal_email.email' => 'you entered email in invalid format'

                ]
            );

            $employee = Employee::find($request->emp_no);

            if ($request->old_email !== $request->email && !is_null($request->email)) {

                $employee->email = $request->email;
                $employee->save();

                $user = User::where('employee_no', $request->emp_no)->update(['email' => $request->email]);

                $data = new EmployeeChangeHistory();
                $data->type = 302;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_email, 'value' => $request->old_email, 'text' => 'Old SJP Email')]);
                $data->new_record = json_encode([array('key' => $request->email, 'value' => $request->email, 'text' => 'New SJP Email')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_personal_email !== $request->personal_email && !is_null($request->personal_email)) {

                $employee->personal_email = $request->personal_email;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 303;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_personal_email, 'value' => $request->old_personal_email, 'text' => 'Old Personal Email')]);
                $data->new_record = json_encode([array('key' => $request->personal_email, 'value' => $request->personal_email, 'text' => 'New Personal Email')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            $notification = array(
                'message' => 'Email updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function dobUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.dob_change", compact('empData'));
        }
    }


    public function dobUpdateStore(Request $request)
    {

        if (!isset($request->date_of_birth)) {


            $notification = array(
                'message' => 'Please enter new value',
                'alert-type' => 'error'
            );

            return redirect()->route('dob.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'date_of_birth' => 'required|date|before:-18 years',
                ],
                [
                    'date_of_birth.required' => 'the birthday required',
                    'date_of_birth.date' => 'the birthday must be date',
                    'date_of_birth.before' => 'Age must be 18 or more'
                ]
            );


            $employee = Employee::find($request->emp_no);

            if ($request->old_date_of_birth !== $request->date_of_birth && !is_null($request->date_of_birth)) {

                $employee->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                $employee->retirement_date = $this->checkPernsionDateGet($request->emp_no, $request->date_of_birth);
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 310;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => date("Y-m-d", strtotime($request->old_date_of_birth)), 'value' => date("Y-m-d", strtotime($request->old_date_of_birth)), 'text' => 'Old Date of Birth')]);
                $data->new_record = json_encode([array('key' => date("Y-m-d", strtotime($request->date_of_birth)), 'value' => date("Y-m-d", strtotime($request->date_of_birth)), 'text' => 'New Date of Birth')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            $notification = array(
                'message' => 'Date of birth updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function genderUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $categories = $this->getCategories([1]);
        $genders = $categories->where('category_type_id', '1');

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.gender_change", compact('empData', 'genders'));
        }
    }

    public function genderUpdateStore(Request $request)
    {

        if (!isset($request->gender_id)) {


            $notification = array(
                'message' => 'Please enter new value',
                'alert-type' => 'error'
            );

            return redirect()->route('gender.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'gender_id' => 'required',
                ],
                [
                    'gender_id.required' => 'the gender required',
                ]
            );


            $employee = Employee::find($request->emp_no);

            if ($request->old_gender_id !== $request->gender_id) {

                $employee->gender_id = $request->gender_id;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 332;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_gender_id, 'value' => $this->getCategoriesName($request->old_gender_id), 'text' => 'Old Gender')]);
                $data->new_record = json_encode([array('key' => $request->gender_id, 'value' => $this->getCategoriesName($request->gender_id), 'text' => 'New Gender')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            $notification = array(
                'message' => 'Gender updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function civilStatusUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $categories = $this->getCategories([4]);
        $civilStatus = $categories->where('category_type_id', '4');

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.civil_status_change", compact('empData', 'civilStatus'));
        }
    }

    public function civilStatusUpdateStore(Request $request)
    {

        if (!isset($request->civil_status_id)) {


            $notification = array(
                'message' => 'Please enter new value',
                'alert-type' => 'error'
            );

            return redirect()->route('civil.status.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'civil_status_id' => 'required',
                ],
                [
                    'civil_status_id.required' => 'the gender required',
                ]
            );


            $employee = Employee::find($request->emp_no);

            if ($request->old_civil_status_id !== $request->civil_status_id) {

                $employee->civil_status_id = $request->civil_status_id;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 333;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_civil_status_id, 'value' => $this->getCategoriesName($request->old_civil_status_id), 'text' => 'Old Civil Status')]);
                $data->new_record = json_encode([array('key' => $request->civil_status_id, 'value' => $this->getCategoriesName($request->civil_status_id), 'text' => 'New Civil Status')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            $notification = array(
                'message' => 'Gender updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function raceReligionUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $categories = $this->getCategories([2, 3]);
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.race_religion_change", compact('empData', 'races', 'religions'));
        }
    }

    public function raceReligionUpdateStore(Request $request)
    {

        if (!isset($request->race_id) && !isset($request->religion_id)) {


            $notification = array(
                'message' => 'Please enter new value',
                'alert-type' => 'error'
            );

            return redirect()->route('race.religion.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'race_id' => 'required',
                    'religion_id' => 'required',
                ],
                [
                    'race_id.required' => 'the race required',
                    'religion_id.required' => 'the religion required',
                ]
            );


            $employee = Employee::find($request->emp_no);

            if ($request->old_race_id !== $request->race_id) {

                $employee->race_id = $request->race_id;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 334;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_race_id, 'value' => $this->getCategoriesName($request->old_race_id), 'text' => 'Old Race')]);
                $data->new_record = json_encode([array('key' => $request->race_id, 'value' => $this->getCategoriesName($request->race_id), 'text' => 'New Race')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_religion_id !== $request->religion_id) {

                $employee->religion_id = $request->religion_id;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 335;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_religion_id, 'value' => $this->getCategoriesName($request->old_religion_id), 'text' => 'Old Religion')]);
                $data->new_record = json_encode([array('key' => $request->religion_id, 'value' => $this->getCategoriesName($request->religion_id), 'text' => 'New Religion')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            $notification = array(
                'message' => 'Race and Religion updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function addressUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $cities = City::all();

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.address_change", compact('empData', 'cities'));
        }
    }

    public function addressUpdateStore(Request $request)
    {
        // dd($request->all());
        $old_address = preg_replace("/(\,|\.)/", " ", $request->old_permanent_add1);
        $old_address .= ', ' . ucwords(preg_replace("/(\,|\.)/", " ", $request->old_permanent_add2));
        $old_address .= ', ' . ucwords(preg_replace("/(\,|\.)/", " ", $request->old_permanent_add3));
        $old_address .= ', ' . $this->getCityName($request->old_permanent_city_id);

        $new_address = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
        $new_address .= ', ' . ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
        $new_address .= ', ' . ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
        $new_address .= ', ' . $this->getCityName($request->permanent_city_id);

        //dd($old_address, $new_address);

        if ($old_address == $new_address) {


            $notification = array(
                'message' => 'Please enter new value',
                'alert-type' => 'error'
            );

            return redirect()->route('address.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'permanent_add1' => 'required',
                    'permanent_add2' => 'nullable',
                    'permanent_add3' => 'nullable',
                    'permanent_city_id' => 'required',
                ],
                [
                    'permanent_add1.required' => 'the permanent address line 1 required',
                    'permanent_city_id.required' => 'the permanent city required',
                ]
            );


            $employee = Employee::find($request->emp_no);
            $employee->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $employee->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $employee->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $employee->permanent_city_id = $request->permanent_city_id;
            $employee->save();

            $data = new EmployeeChangeHistory();
            $data->type = 336;
            $data->emp_no = $request->emp_no;
            $data->pervious_record = json_encode([array('key' => $old_address, 'value' => $old_address, 'text' => 'Old Permanent Address')]);
            $data->new_record = json_encode([array('key' => $new_address, 'value' => $new_address, 'text' => 'New Permanent Address')]);
            $data->date = date("Y-m-d");
            $data->updated_user_id = auth()->user()->employee_no;
            $data->approvability = 0;
            $data->approved_status = 0;
            $data->created_at = Carbon::now();
            $data->save();

            $notification = array(
                'message' => 'Permanent address updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function postalAddressUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $cities = City::all();

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.postal_address_change", compact('empData', 'cities'));
        }
    }

    public function postalAddressUpdateStore(Request $request)
    {
        // dd($request->all());
        $old_address = preg_replace("/(\,|\.)/", " ", $request->old_postal_add1);
        $old_address .= ', ' . ucwords(preg_replace("/(\,|\.)/", " ", $request->old_postal_add2));
        $old_address .= ', ' . ucwords(preg_replace("/(\,|\.)/", " ", $request->old_postal_add3));
        $old_address .= ', ' . $this->getCityName($request->old_postal_city_id);

        $new_address = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
        $new_address .= ', ' . ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
        $new_address .= ', ' . ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
        $new_address .= ', ' . $this->getCityName($request->postal_city_id);

        //dd($old_address, $new_address);

        if ($old_address == $new_address) {

            $notification = array(
                'message' => 'Please enter new value',
                'alert-type' => 'error'
            );

            return redirect()->route('postal.address.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'postal_add1' => 'required',
                    'postal_add2' => 'nullable',
                    'postal_add3' => 'nullable',
                    'postal_city_id' => 'required',
                ],
                [
                    'postal_add1.required' => 'the postal address line 1 required',
                    'postal_city_id.required' => 'the postal city required',
                ]
            );


            $employee = Employee::find($request->emp_no);
            $employee->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $employee->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $employee->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $employee->postal_city_id = $request->postal_city_id;
            $employee->save();

            $data = new EmployeeChangeHistory();
            $data->type = 337;
            $data->emp_no = $request->emp_no;
            $data->pervious_record = json_encode([array('key' => $old_address, 'value' => $old_address, 'text' => 'Old Postal Address')]);
            $data->new_record = json_encode([array('key' => $new_address, 'value' => $new_address, 'text' => 'New Postal Address')]);
            $data->date = date("Y-m-d");
            $data->updated_user_id = auth()->user()->employee_no;
            $data->approvability = 0;
            $data->approved_status = 0;
            $data->created_at = Carbon::now();
            $data->save();

            $notification = array(
                'message' => 'Postal address updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function fileRefNumberUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.file_number_change", compact('empData'));
        }
    }

    public function fileRefNumberUpdateStore(Request $request)
    {

        if (!isset($request->file_reference_number)) {

            $notification = array(
                'message' => 'Please enter new File Reference No',
                'alert-type' => 'error'
            );

            return redirect()->route('file.ref.number.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'file_reference_number' => ['nullable']

                ]
            );

            $employee = Employee::find($request->emp_no);

            if ($request->old_file_reference_number !== strtoupper($request->file_reference_number)) {

                $employee->file_reference_number = $request->file_reference_number;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 338;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => strtoupper($request->old_file_reference_number), 'value' => strtoupper($request->old_file_reference_number), 'text' => 'Old File Reference No')]);
                $data->new_record = json_encode([array('key' => strtoupper($request->file_reference_number), 'value' => strtoupper($request->file_reference_number), 'text' => 'New File Reference No')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();

                $notification = array(
                    'message' => 'File Reference No Update Send for approval',
                    'alert-type' => 'info'
                );

                return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
            }


            $notification = array(
                'message' => 'You entered same File Reference No',
                'alert-type' => 'error'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function educationUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $categories = $this->getCategories([16]);
        $educationLevels = $categories->where('category_type_id', '16');
        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.education_level_change", compact('empData', 'educationLevels'));
        }
    }

    public function educationUpdateStore(Request $request)
    {

        if (!isset($request->emp_highest_edu_level)) {


            $notification = array(
                'message' => 'Please enter new highest educational qualification',
                'alert-type' => 'error'
            );

            return redirect()->route('education.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'emp_highest_edu_level' => 'required',
                ],
                [
                    'emp_highest_edu_level.required' => 'the highest educational qualification required',
                ]
            );


            $employee = Employee::find($request->emp_no);

            if ($request->old_emp_highest_edu_level !== $request->emp_highest_edu_level) {

                $employee->emp_highest_edu_level = $request->emp_highest_edu_level;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 339;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_emp_highest_edu_level, 'value' => $this->getCategoriesName($request->old_emp_highest_edu_level), 'text' => 'Old Highest Educational Qualification')]);
                $data->new_record = json_encode([array('key' => $request->emp_highest_edu_level, 'value' => $this->getCategoriesName($request->emp_highest_edu_level), 'text' => 'New Highest Educational Qualification')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            $notification = array(
                'message' => 'Highest educational qualification updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function designationUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();
        } elseif ($mainBranch == 52) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();
        } elseif ($mainBranch == 53) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();
        }

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.designation_change", compact('empData', 'designations'));
        }
    }



    public function designationUpdateStore(Request $request)
    {

        if (!isset($request->designation_id)) {

            $notification = array(
                'message' => 'Please enter new designation',
                'alert-type' => 'error'
            );

            return redirect()->route('designation.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'designation_id' => ['required']

                ],
                [
                    'designation_id.required' => 'the employee designation required',

                ]
            );

            //$employee = Employee::find($request->emp_no);

            if ($request->old_designation_id !== $request->designation_id) {

                $perRequest = EmployeeChangeHistory::where('emp_no', $request->emp_no)->where('type', 340)->where('approved_status', 0)->first();
                if ($perRequest) {

                    $notification = array(
                        'message' => 'You already send a request for designation update',
                        'alert-type' => 'error'
                    );

                    return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
                }

                $data = new EmployeeChangeHistory();
                $data->type = 340;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_designation_id, 'value' => $this->getDesignationName($request->old_designation_id), 'text' => 'Old Designation')]);
                $data->new_record = json_encode([array('key' => $request->designation_id, 'value' => $this->getDesignationName($request->designation_id), 'text' => 'New Designation')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 1;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();

                $notification = array(
                    'message' => 'Designation Update Send for approval',
                    'alert-type' => 'info'
                );

                return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
            }


            $notification = array(
                'message' => 'You select same designation',
                'alert-type' => 'error'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function basicSalaryUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();



        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.basic_salary_change", compact('empData'));
        }
    }

    public function basicSalaryUpdateStore(Request $request)
    {

        if (!isset($request->current_basic_salary)) {

            $notification = array(
                'message' => 'Please enter new basic salary',
                'alert-type' => 'error'
            );

            return redirect()->route('basic.salary.update.view', encrypt($request->emp_no))->with($notification);
        } else {

            $request->validate(
                [
                    'current_basic_salary' => ['required', 'numeric']

                ],
                [
                    'current_basic_salary.required' => 'the employee basic salary required',

                ]
            );

            //$employee = Employee::find($request->emp_no);

            if ($request->old_current_basic_salary !== $request->current_basic_salary) {

                $perRequest = EmployeeChangeHistory::where('emp_no', $request->emp_no)->where('type', 341)->where('approved_status', 0)->first();
                if ($perRequest) {

                    $notification = array(
                        'message' => 'You already send a request for basic salary update',
                        'alert-type' => 'error'
                    );

                    return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
                }

                $data = new EmployeeChangeHistory();
                $data->type = 341;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_current_basic_salary, 'value' => $request->old_current_basic_salary, 'text' => 'Old Basic Salary')]);
                $data->new_record = json_encode([array('key' => $request->current_basic_salary, 'value' => $request->current_basic_salary, 'text' => 'New Basic Salary')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 1;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();

                $notification = array(
                    'message' => 'Basic Salary Update Send for approval',
                    'alert-type' => 'info'
                );

                return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
            }


            $notification = array(
                'message' => 'You select same basic salary',
                'alert-type' => 'error'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }


    public function statusUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            ->first();

        $categories = $this->getCategories([21, 22]);
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeStatusTypes = $categories->where('category_type_id', '22');

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.status_change", compact('empData', 'employeeStatusIds', 'employeeStatusTypes'));
        }
    }


    public function statusUpdateStore(Request $request)
    {

        if (!isset($request->employee_status_id) && !isset($request->employee_status_type_id)) {

            $notification = array(
                'message' => 'Please enter new status',
                'alert-type' => 'error'
            );

            return redirect()->route('status.update.view', encrypt($request->emp_no))->with($notification);

        } else {

            $request->validate(
                [
                    'employee_status_id' => ['required'],
                    'employee_status_type_id' => ['required'],
                    'effective_date' => [
                        'required_unless:employee_status_type_id,112'
                    ],
                ],
                [
                    'employee_status_id.required' => 'The employee status is required.',
                    'employee_status_type_id.required' => 'The employee status type is required.',
                    'effective_date.required_unless' => 'The effective date is required.',
                ]
            );

            if ($request->old_employee_status_id !== $request->employee_status_id || $request->old_employee_status_type_id !== $request->employee_status_type_id) {

                $perRequest = EmployeeChangeHistory::where('emp_no', $request->emp_no)->where('type', 342)->where('approved_status', 0)->first();

                if ($perRequest) {

                    $notification = array(
                        'message' => 'You already send a request for employee status update',
                        'alert-type' => 'error'
                    );

                    return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
                }

                $statusChange = $this->activeStatusChangeType(
                    $request->old_employee_status_id,
                    $request->employee_status_id
                );

                if ($statusChange === 'inactive_to_other') {

                    if ($this->checkSimilarNIC($request->nic)) {
                        return redirect()->back()->withErrors([
                            'employee_status_id' => 'An active employee with this NIC already exists in the system.'
                        ]);
                    }

                }

                $data = new EmployeeChangeHistory();
                $data->type = 342;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([
                    [
                        'key' => $request->old_employee_status_id,
                        'value' => $this->getCategoriesName($request->old_employee_status_id),
                        'text' => 'Old Employee Status'
                    ],
                    [
                        'key' => $request->old_employee_status_type_id,
                        'value' => $this->getCategoriesName($request->old_employee_status_type_id),
                        'text' => 'Old Employee Status Type'
                    ],
                    [
                        'key' => '',
                        'value' => '',
                        'text' => 'Old Effective Date'
                    ]
                ]);

                $data->new_record = json_encode([
                    [
                        'key' => $request->employee_status_id,
                        'value' => $this->getCategoriesName($request->employee_status_id),
                        'text' => 'New Employee Status'
                    ],
                    [
                        'key' => $request->employee_status_type_id,
                        'value' => $this->getCategoriesName($request->employee_status_type_id),
                        'text' => 'New Employee Status Type'
                    ],
                    [
                        'key' => date("Y-m-d", strtotime($request->effective_date)),
                        'value' => $request->effective_date,
                        'text' => 'New Effective Date'
                    ]
                ]);

                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 1;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();

                $notification = array(
                    'message' => 'Employee Status Update Send for approval',
                    'alert-type' => 'info'
                );

                return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
            }


            $notification = array(
                'message' => 'You select same status',
                'alert-type' => 'error'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function fundNumberUpdateView($id){

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.epf_upf_no_change", compact('empData'));
        }

    }

    public function fundNumberUpdateStore(Request $request)
    {

        if (!isset($request->etf_no) && !isset($request->upf_no)) {


            $notification = array(
                'message' => 'Please enter new value',
                'alert-type' => 'error'
            );

            return redirect()->route('fund.number.update.view', encrypt($request->emp_no))->with($notification);

        } else {
            $request->validate(
                [
                    'etf_no' => ['nullable'],
                    'upf_no' => ['nullable', 'regex:/^S [0-9]{6}$/'],
                    'pension_reference_no' => ['nullable', 'regex:/^PS [0-9]{6}$/'],
                ],
                [
                    'etf_no.required' => 'The ETF number is required.',
                    'upf_no.required' => 'The UPF number is required.',
                    'upf_no.regex' => 'The UPF number must follow the format "S 123456".',
                    'pension_reference_no.regex' => 'The Pension Reference Number must follow the format "PS 123456".',
                    'race_id.required' => 'The race is required.',
                    'religion_id.required' => 'The religion is required.',
                ]
            );



            $employee = Employee::find($request->emp_no);

            if ($request->old_etf_no !== $request->etf_no && !is_null($request->etf_no)) {

                $employee->etf_no = $request->etf_no;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 343;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_etf_no, 'value' => $request->old_etf_no, 'text' => 'Old ETF Number')]);
                $data->new_record = json_encode([array('key' => $request->etf_no, 'value' => $request->etf_no, 'text' => 'New ETF Number')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_upf_no !== $request->upf_no && !is_null($request->upf_no)) {

                $employee->upf_no = $request->upf_no;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 344;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_upf_no, 'value' => $request->old_upf_no, 'text' => 'Old UPF Number')]);
                $data->new_record = json_encode([array('key' => $request->upf_no, 'value' => $request->upf_no, 'text' => 'New UPF Number')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_pension_reference_no !== $request->pension_reference_no && !is_null($request->pension_reference_no)) {

                $employee->pension_reference_no = $request->pension_reference_no;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 345;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_pension_reference_no, 'value' => $request->old_pension_reference_no, 'text' => 'Old Pension Number')]);
                $data->new_record = json_encode([array('key' => $request->pension_reference_no, 'value' => $request->pension_reference_no, 'text' => 'New Pension Number')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            $notification = array(
                'message' => 'UPF, EPF and Pension No updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    public function carderLocationUpdateView($id)
    {

        $search_emp_no = decrypt($id);

        $empData = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->where('employees.employee_no', $search_emp_no)
            //->where('employees.employee_no', '!=', auth()->user()->employee_no)
            ->first();

        $CarderFaculties = Faculty::all();
        $CarderDepartment = Department::all();
        $CarderSubDepartment = DepartmentSub::all();

        $row_count = $empData->count();

        /*********************************************************** */

        if ($row_count < 0) {

            $notification = array(
                'message' => 'Cannot update own data',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        } else {

            return view("admin.setting.employee.carder_location_change", compact('empData', 'CarderFaculties', 'CarderDepartment', 'CarderSubDepartment'));
        }
    }

    public function carderLocationUpdateStore(Request $request)
    {

        if (!isset($request->carder_faculty_id) && !isset($request->carder_department_id)) {


            $notification = array(
                'message' => 'Please enter new value',
                'alert-type' => 'error'
            );

            return redirect()->route('carder.location.update.view', encrypt($request->emp_no))->with($notification);

        } else {
            $request->validate(
                [
                    'carder_faculty_id' => ['required'],
                    'carder_department_id' => ['required'],
                    'carder_sub_department_id' => ['nullable'],
                ],
                [
                    'carder_faculty_id.required' => 'employee carder faculty required',
                    'carder_department_id.required' => 'employee carder department required',
                ]
            );



            $employee = Employee::find($request->emp_no);

            if ($request->old_carder_faculty_id !== $request->carder_faculty_id && !is_null($request->carder_faculty_id)) {

                $employee->carder_faculty_id = $request->carder_faculty_id;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 347;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_carder_faculty_id, 'value' => $this->getFacultyName($request->old_carder_faculty_id), 'text' => 'Old Carder Faculty')]);
                $data->new_record = json_encode([array('key' => $request->carder_faculty_id, 'value' => $this->getFacultyName($request->carder_faculty_id), 'text' => 'New Carder Faculty')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_carder_department_id !== $request->carder_department_id && !is_null($request->carder_department_id)) {

                $employee->carder_department_id = $request->carder_department_id;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 348;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_carder_department_id, 'value' => $this->getDepartmentName($request->old_carder_department_id), 'text' => 'Old Carder Department')]);
                $data->new_record = json_encode([array('key' => $request->carder_department_id, 'value' => $this->getDepartmentName($request->carder_department_id), 'text' => 'New Carder Department')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            if ($request->old_carder_sub_department_id !== $request->carder_sub_department_id && !is_null($request->carder_sub_department_id)) {

                $employee->carder_sub_department_id = $request->carder_sub_department_id;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type = 349;
                $data->emp_no = $request->emp_no;
                $data->pervious_record = json_encode([array('key' => $request->old_carder_sub_department_id, 'value' => $this->getSubDepartmentName($request->old_carder_sub_department_id), 'text' => 'Old Carder Sub Department')]);
                $data->new_record = json_encode([array('key' => $request->carder_sub_department_id, 'value' => $this->getSubDepartmentName($request->carder_sub_department_id), 'text' => 'New Carder Sub Department')]);
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->approvability = 0;
                $data->approved_status = 0;
                $data->created_at = Carbon::now();
                $data->save();
            }

            $notification = array(
                'message' => 'Carder location updated successfully',
                'alert-type' => 'info'
            );

            return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
        }
    }

    private function checkPernsionDateGet($empNo, $dob)
    {

        $employee = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->where('employees.employee_no', $empNo)
            ->first();

        if ($employee->main_group == 97) {

            $dobget = date("Y-m-d", strtotime($dob));
            $date = date('Y-m-d', strtotime($dobget . ' + 65 years'));
        } else if ($employee->main_group == 98) {

            $dobget = date("Y-m-d", strtotime($dob));
            $date = date('Y-m-d', strtotime($dobget . ' + 60 years'));
        }

        return $date;
    }

    private function getCategoriesName($categoryId)
    {
        $results = Category::where('id', $categoryId)->select('category_name')->first();
        return $results;
    }

    private  function getDesignationName($id)
    {

        $designation = Designation::find($id);
        return $designation->designation_name . ' (' . $designation->salary_code . ')';
    }

    private function getCityName($id)
    {

        $city = City::find($id);
        return $city->name_en;
    }

    private function employeefacultyGet($deptNo){
        $faculty = Department::where('id', $deptNo)->first();
        return $faculty ? $faculty->faculty_code : null;
    }

    private function getFacultyName($facId){

        $faculty = Faculty::where('id', $facId)->first();
        return $faculty ? $faculty->faculty_name : null;
    }

    private function getDepartmentName($deptId){

        $department = Department::where('id', $deptId)->first();
        return $department ? $department->department_name : null;
    }

    private function getSubDepartmentName($subDeptId){

        $subDepartment = DepartmentSub::where('id', $subDeptId)->first();
        return $subDepartment ? $subDepartment->sub_department_name : null;
    }

    private function checkSimilarNIC($nic)
    {
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post('https://hrms.sjp.ac.lk/api/nic/data', [
            'nic' => strtoupper($nic)
        ]);

        $nicData = json_decode($empDetails->body(), true);

        $exists = Employee::where(function ($query) use ($nicData) {
            $query->where('nic_old', $nicData['oldnic'])
                ->orWhere('nic_new', $nicData['newnic']);
        })->where('employee_status_id', 110)->exists();

        return $exists;
    }


    private function activeStatusChangeType($previous, $current)
    {
        if ($previous == 111 && $current != 111) {
            return 'inactive_to_other';
        }

        if ($previous == 110 && $current != 110) {
            return 'active_to_other';
        }

        return 'no_change';
    }

}
