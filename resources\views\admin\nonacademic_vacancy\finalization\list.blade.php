@extends('admin.admin_master')
@section('admin')
    <!-- Content Header (Page header) -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <div class="content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-12 text-center">
                    <h5 class="mb-1">
                        {{ strtoupper($vacancy_name) }} <b>
                            <font style="font-size: 20px;">(
                                @if ($designation_category == 138)
                                    PERMANENT
                                @elseif ($designation_category == 140)
                                    TEMPORARY
                                @elseif ($designation_category == 141)
                                    CONTRACT
                                @elseif ($designation_category == 142)
                                    ASSIGNMENT BASIS
                                @endif
                                )
                            </font>
                        </b><br>
                        INTERVIEW APPLICANTS
                    </h5>
                </div><!-- /.col -->
            </div><!-- /.row -->
        </div><!-- /.container-fluid -->
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <table id="example1" class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>SN</th>
                                        <th>App. No</th>
                                        <th>Name</th>
                                        <th>NIC</th>
                                        <th>Status</th>
                                        <th>Total Marks</th>
                                        <th width="25%">View</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($data_text as $key => $data_texts)

                                        <tr>
                                            <td>{{ $key+1 }}</td>
                                            <td>{{ $data_texts->id }}</td>
                                            <td>{{ $data_texts->name_with_initials }} {{ $data_texts->last_name }}</td>
                                            <td>{{ strtoupper($data_texts->nic) }}</td>
                                            <td>
                                            @if ($data_texts->duty_assume_status == 0)
                                            <span class="badge badge-pill badge-danger" style="font-size: 12px;">Duty Assume Pending</span>
                                            @elseif ($data_texts->duty_assume_status == 1)
                                            <span class="badge badge-pill badge-dark" style="font-size: 12px;">Appointment Not Accept</span>
                                            @elseif ($data_texts->duty_assume_status == 2)
                                            <span class="badge badge-pill badge-primary" style="font-size: 12px;">Duty Assumed</span>
                                            @elseif ($data_texts->duty_assume_status == 3)
                                            <span class="badge badge-pill badge-success" style="font-size: 12px;">Applicant Trasfered to Employee</span>
                                            @endif
                                            </td>
                                            <td>{{ $data_texts->total_marks }}</td>
                                            <td>
                                                <div style="display: flex; align-items: center;">
                                                    <form method="post"
                                                        action="{{ route('nonacademic.vacancy.application.show') }}"
                                                        style="margin-left: 10px;">
                                                        @csrf
                                                        <input type="hidden" name="app_id" value="{{ $data_texts->id }}">
                                                        <input type="submit" class="btn btn-sm btn-info"
                                                            value="Application">
                                                    </form>
                                                @role('super-admin|administrator|cc|sc')
                                                @if($data_texts->duty_assume_status == 0)
                                                <a href="{{ route('nac.applicant.duty.assume', encrypt($data_texts->id)) }}" class="btn btn-sm btn-primary ml-2">Duty Assumption</a>
                                                @endif
                                                @endrole

                                                @role('super-admin|administrator|est-head')
                                                @if($data_texts->duty_assume_status == 2)
                                                <a href="{{ route('nac.applicant.convert.employee', encrypt($data_texts->id)) }}" class="btn btn-sm btn-primary ml-2">Employee Enrollment</a>
                                                @endif
                                                @endrole

                                                @role('super-admin|administrator|est-head')
                                                @if($data_texts->duty_assume_status == 3)
                                                <a href="{{ route('employee.show', encrypt($data_texts->employee_no)) }}" class="btn btn-sm btn-primary" style="margin-left: 10px;">Show Employee Profile</a>
                                                @endif
                                                @endrole
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                @if ($booked == $noduty || $booked == $empConvert || $noduty + $empConvert == $booked)
                <div class="card">
                    <div class="card-body">
                        <form action="{{ route('nac.vacancy.finalize')}}" method="POST"  onsubmit="return validateForm()" id="shortlistForm">
                            @csrf
                            <div class="row">

                                <div class="col-6 d-flex justify-content-end">
                                    <input type="hidden" id="vacancyid" name="vacancyid" value="{{$vacancyid}}">
                                    <input type="hidden" name="empConvert" value="{{$empConvert}}">
                                </div>
                            </div>

                            <div class="row mt-3" id="contributionMarkSection">
                                <div class="col-2">

                                </div>
                                <div class="col-4">

                                </div>
                                <div class="col-6 d-flex justify-content-end">
                                    <button type="button" class="btn btn-md btn-success"  onclick="confirmFinalization()"> Confirm List </button>
                                </div>
                            </div>
                        </form>


                        <script>
                        function confirmFinalization() {

                            //if (!validateForm()) return;

                            Swal.fire({
                                title: 'Are you sure?',
                                text: 'Do you want to finalize this vacancy ? This action cannot be undone.',
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: 'Yes, finalize it!'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    document.getElementById('shortlistForm').submit();
                                }
                            });
                        }
                        </script>
                    </div>
                </div>
                @endif

                </div>
            </div>
        </div>
    </section>
@endsection
