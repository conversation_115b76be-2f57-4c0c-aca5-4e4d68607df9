<?php

namespace App\Http\Controllers\Summary;

use App\Http\Controllers\Controller;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Faculty;
use App\Models\salaryRevision2025;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class InternalSummaryController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin');
    }

    public function InternalSystem()
    {

        return view('admin.summary.internal_index');
    }

    public function findSimilarNICEmployee()
    {
        // Retrieve all unique email addresses along with their associated employee numbers
        $data = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select('initials', 'last_name', 'nic_old', 'nic_new', 'nic', 'category_name as title')->where('employees.employee_status_id', 110)->get();

        // Return the list of email addresses and their corresponding employee numbers
        return view('admin.summary.internal.nic_similar_list', compact('data'));
    }

    public function findSimilarEmailEmployee()
    {
        // Retrieve all unique email addresses along with their associated employee numbers
        $emails = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select('initials', 'last_name', 'email', 'category_name as title', 'employee_status_id', 'employee_work_type', 'retirement_date', 'salary_termination_date_1')
            ->where('employees.employee_status_id', 110)
            ->where(function ($query) {
                $query->whereNotNull('email')
                    ->orWhere('email', '!=', '');
            })
            ->distinct()
            ->get();

        $pendingemails = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->select('initials', 'last_name', 'employee_no', 'category_name as title', 'employee_status_id', 'department_name', 'designation_name', 'employee_work_type', 'retirement_date', 'salary_termination_date_1')
            ->where('employees.employee_status_id', 110)
            ->where(function ($query) {
                $query->whereNull('email')
                    ->orWhere('email', '==', '');
            })
            ->distinct()
            ->get();



        // Return the list of email addresses and their corresponding employee numbers
        return view('admin.summary.internal.email_similar_list', compact('emails', 'pendingemails'));
    }


    public function facultyAcademicZoomEmployee()
    {
        // Retrieve all unique email addresses along with their associated employee numbers
        $faculties_perm = Faculty::leftJoin('employees', 'faculties.id', '=', 'employees.faculty_id')
            ->Join('designations', 'designations.id', '=', 'employees.designation_id')
            ->select(
                'faculties.id as faculty_id',
                'faculties.faculty_name',
                DB::raw('COUNT(CASE WHEN employees.employee_status_id = 110 THEN employees.id ELSE NULL END) as total_employee_count'),
                DB::raw('SUM(IF(employees.zoom_active_status = 1, 1, 0)) as zoom_active_employee_count')
            )
            //->where('employees.employee_status_id',110)
            ->where('designations.main_group', 97)
            ->whereIn('employees.employee_work_type', [138, 139])
            ->groupBy('faculties.id', 'faculties.faculty_name')
            ->get();

        $faculties_temp = Faculty::leftJoin('employees', 'faculties.id', '=', 'employees.faculty_id')
            ->Join('designations', 'designations.id', '=', 'employees.designation_id')
            ->select(
                'faculties.id as faculty_id',
                'faculties.faculty_name',
                DB::raw('COUNT(CASE WHEN employees.employee_status_id = 110 THEN employees.id ELSE NULL END) as total_employee_count'),
                DB::raw('SUM(IF(employees.zoom_active_status = 1, 1, 0)) as zoom_active_employee_count')
            )
            //->where('employees.employee_status_id',110)
            ->where('designations.main_group', 97)
            ->whereIn('employees.employee_work_type', [140])
            ->groupBy('faculties.id', 'faculties.faculty_name')
            ->get();

        $faculties_cont = Faculty::leftJoin('employees', 'faculties.id', '=', 'employees.faculty_id')
            ->Join('designations', 'designations.id', '=', 'employees.designation_id')
            ->select(
                'faculties.id as faculty_id',
                'faculties.faculty_name',
                DB::raw('COUNT(CASE WHEN employees.employee_status_id = 110 THEN employees.id ELSE NULL END) as total_employee_count'),
                DB::raw('SUM(IF(employees.zoom_active_status = 1, 1, 0)) as zoom_active_employee_count')
            )
            //->where('employees.employee_status_id',110)
            ->where('designations.main_group', 97)
            ->whereIn('employees.employee_work_type', [141])
            ->groupBy('faculties.id', 'faculties.faculty_name')
            ->get();

        $faculties_non = Faculty::leftJoin('employees', 'faculties.id', '=', 'employees.faculty_id')
            ->Join('designations', 'designations.id', '=', 'employees.designation_id')
            ->select(
                'faculties.id as faculty_id',
                'faculties.faculty_name',
                DB::raw('COUNT(CASE WHEN employees.employee_status_id = 110 THEN employees.id ELSE NULL END) as total_employee_count'),
                DB::raw('SUM(IF(employees.zoom_active_status = 1, 1, 0)) as zoom_active_employee_count')
            )
            //->whereIn('employees.employee_work_type',[138,139])
            ->where('designations.main_group', 98)
            ->groupBy('faculties.id', 'faculties.faculty_name')
            ->get();

        // Return the list of email addresses and their corresponding employee numbers
        return view('admin.summary.internal.faculty_academic_zoom_list', compact('faculties_perm', 'faculties_temp', 'faculties_cont', 'faculties_non'));
    }

    public function DepartmentEmployeeCountList()
    {

        // Retrieve all unique email addresses along with their associated employee numbers
        $departments_perm = Department::leftJoin('employees', 'departments.id', '=', 'employees.department_id')
            ->Join('designations', 'designations.id', '=', 'employees.designation_id')
            ->select(
                'departments.id as department_id',
                'departments.department_name',
                DB::raw('COUNT(CASE WHEN employees.employee_status_id = 110 THEN employees.id ELSE NULL END) as total_employee_count'),
                DB::raw('SUM(IF(employees.zoom_active_status = 1, 1, 0)) as zoom_active_employee_count')
            )
            //->where('employees.employee_status_id',110)
            ->where('designations.main_group', 97)
            ->whereIn('employees.employee_work_type', [138, 139])
            ->groupBy('departments.id', 'departments.department_name')
            ->get();

        $departments_temp = Department::leftJoin('employees', 'departments.id', '=', 'employees.department_id')
            ->Join('designations', 'designations.id', '=', 'employees.designation_id')
            ->select(
                'departments.id as department_id',
                'departments.department_name',
                DB::raw('COUNT(CASE WHEN employees.employee_status_id = 110 THEN employees.id ELSE NULL END) as total_employee_count'),
                DB::raw('SUM(IF(employees.zoom_active_status = 1, 1, 0)) as zoom_active_employee_count')
            )
            //->where('employees.employee_status_id',110)
            ->where('designations.main_group', 97)
            ->whereIn('employees.employee_work_type', [140])
            ->groupBy('departments.id', 'departments.department_name')
            ->get();

        $departments_cont = Department::leftJoin('employees', 'departments.id', '=', 'employees.department_id')
            ->Join('designations', 'designations.id', '=', 'employees.designation_id')
            ->select(
                'departments.id as department_id',
                'departments.department_name',
                DB::raw('COUNT(CASE WHEN employees.employee_status_id = 110 THEN employees.id ELSE NULL END) as total_employee_count'),
                DB::raw('SUM(IF(employees.zoom_active_status = 1, 1, 0)) as zoom_active_employee_count')
            )
            //->where('employees.employee_status_id',110)
            ->where('designations.main_group', 97)
            ->whereIn('employees.employee_work_type', [141])
            ->groupBy('departments.id', 'departments.department_name')
            ->get();

        $departments_non = Department::leftJoin('employees', 'departments.id', '=', 'employees.department_id')
            ->Join('designations', 'designations.id', '=', 'employees.designation_id')
            ->select(
                'departments.id as department_id',
                'departments.department_name',
                DB::raw('COUNT(CASE WHEN employees.employee_status_id = 110 THEN employees.id ELSE NULL END) as total_employee_count'),
                DB::raw('SUM(IF(employees.zoom_active_status = 1, 1, 0)) as zoom_active_employee_count')
            )
            //->whereIn('employees.employee_work_type',[138,139])
            ->where('designations.main_group', 98)
            ->groupBy('departments.id', 'departments.department_name')
            ->get();

        // Return the list of email addresses and their corresponding employee numbers
        return view('admin.summary.internal.department_academic_zoom_list', compact('departments_perm', 'departments_temp', 'departments_cont', 'departments_non'));
    }

    public function serviceTerminationEmployee()
    {

        $activeEmployees = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select('employees.*', 'category_name as title')
            ->where('employees.employee_status_id', 110)
            ->whereIn('employees.employee_work_type', [138, 139])
            ->where('employees.salary_termination_date_1', '!=', '1970-01-01')
            ->get();

        $inactiveEmployees = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select('employees.*', 'category_name as title')
            ->where('employees.employee_status_id', 111)
            ->whereIn('employees.employee_work_type', [138, 139])
            //->where('employees.salary_termination_date_1','!=', '1970-01-01')
            ->get();

        // Return the list of email addresses and their corresponding employee numbers
        return view('admin.summary.internal.service_termination_list', compact('activeEmployees', 'inactiveEmployees'));
    }

    public function leaveTerminationEmployee()
    {

        $activeEmployees = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select('employees.*', 'category_name as title')
            ->where('employees.employee_status_id', 110)
            ->whereIn('employees.employee_work_type', [138, 139])
            ->where('employees.salary_termination_date_2', '!=', '1970-01-01')
            ->get();

        $inactiveEmployees = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select('employees.*', 'category_name as title')
            ->where('employees.employee_status_id', 111)
            ->whereIn('employees.employee_work_type', [138, 139])
            //->where('employees.salary_termination_date_1','!=', '1970-01-01')
            ->get();

        // Return the list of email addresses and their corresponding employee numbers
        return view('admin.summary.internal.leave_termination_list', compact('activeEmployees', 'inactiveEmployees'));
    }

    public function nicDobEmployee()
    {

        $data = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select('employees.*', 'category_name as title')
            //->where('employees.employee_status_id', 110)
            ->get();

        // Return the list of email addresses and their corresponding employee numbers
        return view('admin.summary.internal.nic_dob_list', compact('data'));
    }

    public function photoListEmployee()
    {
        // Fetch the employee data with the join and conditions
        $employees = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select('employees.*', 'category_name as title')
            ->whereIn('employees.employee_work_type', [138, 139])
            ->where('employees.employee_status_id', 110)
            ->get();

        // Extract employee numbers to check for image existence
        $employeeNumbers = $employees->pluck('employee_no')->toArray();
        $imagesExist = $this->checkEmployeeImages($employeeNumbers);

        // Separate employees with and without photos
        $employeesWithPhoto = $employees->filter(fn($employee) => $imagesExist[$employee->employee_no] ?? false);
        $employeesWithoutPhoto = $employees->filter(fn($employee) => !($imagesExist[$employee->employee_no] ?? false));

        // Pass both groups to the view
        return view('admin.summary.internal.profile_photo_list', compact('employeesWithPhoto', 'employeesWithoutPhoto'));
    }

    private function checkEmployeeImages(array $employeeNumbers)
    {
        $imagePath = public_path('backend/dist/img/profile/');
        $result = [];

        foreach ($employeeNumbers as $employeeNo) {
            $filename = $employeeNo . '.jpg';
            $result[$employeeNo] = File::exists($imagePath . $filename);
        }

        return $result;
    }

    public function salaryRevisionEmployee()
    {
        $withSalaryRevision = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('salary_revision2025s', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
            ->select('employees.*', 'categories.category_name as title', 'salary_revision2025s.*')
            ->get();

        $withoutSalaryRevision = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->leftJoin('salary_revision2025s', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
            ->whereNull('salary_revision2025s.emp_no')
            ->select('employees.*', 'categories.category_name as title')
            ->get();

        $withoutEmployee = salaryRevision2025::leftJoin('employees', 'salary_revision2025s.emp_no', '=', 'employees.employee_no')
            ->whereNull('employees.employee_no')
            ->get();

        return view('admin.summary.internal.salary_revision_list', [
            'withSalaryRevision' => $withSalaryRevision,
            'withoutSalaryRevision' => $withoutSalaryRevision,
            'withoutEmployee' => $withoutEmployee,
        ]);
    }

    public function TINListEmployee(){

        $TinEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->join('categories as ugs_mis', 'designations.ugc_mis', '=', 'ugs_mis.id')
                ->join('categories as staff_grade', 'designations.staff_grade', '=', 'staff_grade.id')
                ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status', 'ugs_mis.category_name as ugs_mis_name','staff_grade.category_name as staff_grade_name')
                //->where('employees.employee_status_id', 110)
                //->whereIN('designations.ugc_mis', [100, 102, 103, 135, 136])
                ->where('employees.main_branch_id', 52)
                ->where('employees.tin_no','!=',0)
                ->whereIn('employees.tin_no_batch',[1,2])
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

        $TinPendingEmployees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->join('categories as ugs_mis', 'designations.ugc_mis', '=', 'ugs_mis.id')
                ->join('categories as staff_grade', 'designations.staff_grade', '=', 'staff_grade.id')
                ->select('employees.*', 'categories.category_name', 'designations.salary_code', 'title.category_name as title_name', 'departments.name_status', 'ugs_mis.category_name as ugs_mis_name','staff_grade.category_name as staff_grade_name')
                ->where('employees.employee_status_id', 110)
                ->whereIN('designations.ugc_mis', [100, 102, 103, 135, 136])
                ->where('employees.main_branch_id', 52)
                ->where('employees.tin_no',0)
                ->orderBy('employees.employee_no', 'ASC')
                ->get();

        return view('admin.summary.internal.tin_list', compact('TinEmployees', 'TinPendingEmployees'));
    }
}
