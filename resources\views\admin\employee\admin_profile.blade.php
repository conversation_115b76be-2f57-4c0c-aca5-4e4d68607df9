@extends('admin.admin_master')
@section('admin')
<style type="text/css">
    .card-box {
        background-color: #fff;
        background-clip: border-box;
        border: 1px solid #e7eaed;
        padding: 1.5rem;
        margin-bottom: 24px;
        border-radius: .25rem;
    }
    .avatar-xl {
        height: 6rem;
        width: 6rem;
    }
    .rounded-circle {
        border-radius: 50%!important;
    }
    .nav-pills .nav-link.active, .nav-pills .show>.nav-link {
        color: #fff;
        background-color: #1abc9c;
    }
    .nav-pills .nav-link {
        border-radius: .25rem;
    }
    .navtab-bg li>a {
        background-color: #f7f7f7;
        margin: 0 5px;
    }
    .nav-pills>li>a, .nav-tabs>li>a {
        color: #6c757d;
        /* font-weight: 600; */
    }
    .mb-4, .my-4 {
        margin-bottom: 2.25rem!important;
    }
    .tab-content {
        padding: 20px 0 0 0;
    }
    .progress-sm {
        height: 5px;
    }
    .m-0 {
        margin: 0!important;
    }
    .table .thead-light th {
        color: #6c757d;
        background-color: #f1f5f7;
        border-color: #dee2e6;
    }
    .social-list-item {
        height: 2rem;
        width: 2rem;
        line-height: calc(2rem - 4px);
        display: block;
        border: 2px solid #adb5bd;
        border-radius: 50%;
        color: #adb5bd;
    }

    .text-purple {
        color: #6559cc!important;
    }
    .border-purple {
        border-color: #6559cc!important;
    }

    .timeline {
        margin-bottom: 50px;
        position: relative;
    }
    .timeline:before {
        background-color: #dee2e6;
        bottom: 0;
        content: "";
        left: 50%;
        position: absolute;
        top: 30px;
        width: 2px;
        z-index: 0;
    }
    .timeline .time-show {
        margin-bottom: 30px;
        margin-top: 30px;
        position: relative;
    }
    .timeline .timeline-box {
        background: #fff;
        display: block;
        margin: 15px 0;
        position: relative;
        padding: 20px;
    }
    .timeline .timeline-album {
        margin-top: 12px;
    }
    .timeline .timeline-album a {
        display: inline-block;
        margin-right: 5px;
    }
    .timeline .timeline-album img {
        height: 36px;
        width: auto;
        border-radius: 3px;
    }
    @media (min-width: 768px) {
        .timeline .time-show {
            margin-right: -69px;
            text-align: right;
        }
        .timeline .timeline-box {
            margin-left: 45px;
        }
        .timeline .timeline-icon {
            background: #dee2e6;
            border-radius: 50%;
            display: block;
            height: 20px;
            left: -54px;
            margin-top: -10px;
            position: absolute;
            text-align: center;
            top: 50%;
            width: 20px;
        }
        .timeline .timeline-icon i {
            color: #98a6ad;
            font-size: 13px;
            position: absolute;
            left: 4px;
        }
        .timeline .timeline-desk {
            display: table-cell;
            vertical-align: top;
            width: 50%;
        }
        .timeline-item {
            display: table-row;
        }
        .timeline-item:before {
            content: "";
            display: block;
            width: 50%;
        }
        .timeline-item .timeline-desk .arrow {
            border-bottom: 12px solid transparent;
            border-right: 12px solid #fff !important;
            border-top: 12px solid transparent;
            display: block;
            height: 0;
            left: -12px;
            margin-top: -12px;
            position: absolute;
            top: 50%;
            width: 0;
        }
        .timeline-item.timeline-item-left:after {
            content: "";
            display: block;
            width: 50%;
        }
        .timeline-item.timeline-item-left .timeline-desk .arrow-alt {
            border-bottom: 12px solid transparent;
            border-left: 12px solid #fff !important;
            border-top: 12px solid transparent;
            display: block;
            height: 0;
            left: auto;
            margin-top: -12px;
            position: absolute;
            right: -12px;
            top: 50%;
            width: 0;
        }
        .timeline-item.timeline-item-left .timeline-desk .album {
            float: right;
            margin-top: 20px;
        }
        .timeline-item.timeline-item-left .timeline-desk .album a {
            float: right;
            margin-left: 5px;
        }
        .timeline-item.timeline-item-left .timeline-icon {
            left: auto;
            right: -56px;
        }
        .timeline-item.timeline-item-left:before {
            display: none;
        }
        .timeline-item.timeline-item-left .timeline-box {
            margin-right: 45px;
            margin-left: 0;
            text-align: right;
        }
    }
    @media (max-width: 767.98px) {
        .timeline .time-show {
            text-align: center;
            position: relative;
        }
        .timeline .timeline-icon {
            display: none;
        }
    }
    .timeline-sm {
        padding-left: 110px;
    }
    .timeline-sm .timeline-sm-item {
        position: relative;
        padding-bottom: 20px;
        padding-left: 40px;
        border-left: 2px solid #dee2e6;
    }
    .timeline-sm .timeline-sm-item:after {
        content: "";
        display: block;
        position: absolute;
        top: 3px;
        left: -7px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #fff;
        border: 2px solid #1abc9c;
    }
    .timeline-sm .timeline-sm-item .timeline-sm-date {
        position: absolute;
        left: -104px;
    }

    /* .password-container {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
} */

.password-text {
    /* font-size: 16px; */
    margin: 0;
    /* padding: 10px; */
    /* border: 1px solid #ccc; */
    /* border-radius: 5px; */
    /* width: 200px; */
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.pin-input {
    padding: 5px;
    margin-left: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
    outline: none;
}

.toggle-button {
    padding: 10px;
    margin-left: 10px;
    border: none;
    background-color: #007bff;
    color: #fff;
    border-radius: 5px;
    cursor: pointer;
    /* font-size: 16px; */
}

.toggle-button:hover {
    background-color: #0056b3;
}
    @media (max-width: 420px) {
        .timeline-sm {
            padding-left: 0;
        }
        .timeline-sm .timeline-sm-date {
            position: relative !important;
            display: block;
            left: 0 !important;
            margin-bottom: 10px;
        }
    }
    </style>
<section class="content">
    <div class="container-fluid">
        <br>
        <div class="row">
            <div class="col-lg-4 col-xl-4">
                <div class="card-box text-center">
                    @if (file_exists(public_path('backend/dist/img/profile/'.$employee->employee_no.'.jpg')))
                    <img src="{{ asset('backend/dist/img/profile/'.$employee->employee_no.'.jpg') }}" class="img-responsive mx-auto d-block img-fluid border border-dark img-thumbnail" style="width: 150px;" alt="profile-image">
                    @else
                    <img src="{{ asset('backend/dist/img/profile.jpg') }}" class="img-responsive mx-auto d-block img-fluid border border-dark img-thumbnail" style="width: 150px;" alt="profile-image">
                    @endif
                    <br>

                    <button type="button" class="btn btn-lg btn-danger">{{ str_pad($employee->employee_no, 6, '0', STR_PAD_LEFT) }}</button>
                    <h4 class="mb-0"> {{ ucfirst($employee->getNameTitle->category_name) }} {{ ucfirst($employee->initials) }} {{ ucfirst($employee->last_name) }}
                    </h4>
                    <h4 class="mb-0 text-bold"> @foreach($designations as $designation)
                        @if ($designation->id == $employee->designation_id)
                        {{ ucfirst($designation->designation_name)}} - <span class="badge badge-primary">{{$designation->staff_grade }}</span><br><span class="badge badge-info">{{ $designation->salary_code }} </span>
                        @endif
                         @endforeach
                    </h4>
                    <p class="text-info h3"><span class="badge badge-warning">{{ strtoupper($employee->file_reference_number) }}</span></p>
                    <p class="text-dark h5"><button type="button" class="btn btn-dark">{{ strtoupper($employee->workTypeName->category_name) }}</button></p>
                    @if ($employee->employee_status_id == 110)
                    <button type="button" class="btn btn-success">{{ strtoupper($employee->empStatusName->category_name) }}</button>
                    <button type="button" class="btn btn-success">{{ strtoupper($employee->empStatusTypeName->category_name) }}</button>
                    <p ></p>
                    @else
                    <button type="button" class="btn btn-danger">{{ strtoupper($employee->empStatusName->category_name) }}</button>
                    <button type="button" class="btn btn-danger">{{ strtoupper($employee->empStatusTypeName->category_name) }}</button>
                    @endif



                    {{-- <button type="button" class="btn btn-success btn-xs waves-effect mb-2 waves-light">Follow</button>
                    <button type="button" class="btn btn-danger btn-xs waves-effect mb-2 waves-light">Message</button> --}}

                    <div class="text-left mt-3">

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-address-book" aria-hidden="true"></i> Active NIC :</strong><span class="ml-2 text-bold">{{ $employee->nic }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-phone" aria-hidden="true"></i> Mobile No:</strong><span class="ml-2 text-bold">{{ $employee->mobile_no }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-envelope" aria-hidden="true"></i> Email :</strong> <span class="ml-2 text-bold">{{ $employee->email }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-university" aria-hidden="true"></i> Faculty :</strong> <span class="ml-2 text-bold">{{ ucfirst($employee->getFacultyName->faculty_name) }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-graduation-cap" aria-hidden="true"></i> Department :</strong> <span class="ml-2 text-bold">{{ ucfirst($employee->getDepartmentName->department_name) }}</span></p>

                    </div>

                </div> <!-- end card-box -->
                @if($fdean->count() > 0)

                <div class="card-box" style="background-color: #fffccd;">
                    <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                        Faculty Dean Appointment</h5></u>

                        @foreach ($fdean as $fdean)
                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-home " aria-hidden="true"></i> Faculty :</strong><span class="ml-2 text-bold"> {{ $fdean->facultyName->faculty_name }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-sign-language" aria-hidden="true"></i> Appointment Type :</strong><span class="ml-2 text-bold">{{ $fdean->AppointmentTypeName->category_name }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-calendar" aria-hidden="true"></i> Start Date :</strong><span class="ml-2 text-bold">{{ date("d-M-Y", strtotime($fdean->start_date)) }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-calendar" aria-hidden="true"></i> End Date :</strong><span class="ml-2 text-bold">{{ $fdean->end_date != '1970-01-01' ? date("d-M-Y", strtotime($fdean->end_date)) : '-' }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-gift" aria-hidden="true"></i> Head Status :</strong><span class="ml-2 text-bold">
                            @if ($fdean->active_status == 1)
                            <button type="button" class="btn btn-success btn-sm">Active</button>
                             @else
                             <button type="button" class="btn btn-danger btn-sm">Deactive</button>
                            @endif

                        </span></p>
                        @endforeach

                </div> <!-- end card-box-->

                 @endif

                @if($dhead->count() > 0)

                <div class="card-box" style="background-color: #fffccd;">
                    <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                        Department Head Appointment</h5></u>

                        @foreach ($dhead as $dhead)
                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-home " aria-hidden="true"></i> Department :</strong><span class="ml-2 text-bold"> {{ $dhead->departmentName->department_name }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-address-book " aria-hidden="true"></i> Head Position :</strong><span class="ml-2 text-bold"> {{ $dhead->HeadPositionName->category_name }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-sign-language" aria-hidden="true"></i> Appointment Type :</strong><span class="ml-2 text-bold">{{ $dhead->AppointmentTypeName->category_name }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-calendar" aria-hidden="true"></i> Start Date :</strong><span class="ml-2 text-bold">{{ date("d-M-Y", strtotime($dhead->start_date)) }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-calendar" aria-hidden="true"></i> End Date :</strong><span class="ml-2 text-bold">{{ $dhead->end_date != '1970-01-01' ? date("d-M-Y", strtotime($dhead->end_date)) : '-' }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-gift" aria-hidden="true"></i> Head Status :</strong><span class="ml-2 text-bold">
                            @if ($dhead->active_status == 1)
                            <button type="button" class="btn btn-success btn-sm">Active</button>
                             @else
                             <button type="button" class="btn btn-danger btn-sm">Deactive</button>
                            @endif

                        </span></p>
                        <hr>
                        @endforeach

                </div> <!-- end card-box-->

                 @endif

                <div class="card-box" >
                    <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>Salary Related Information</h5></u>

                        @if ($employee->employee_work_type == 138 || $employee->employee_work_type == 139)

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-credit-card" aria-hidden="true"></i> Current Basic Salary :</strong><span class="ml-2 text-bold">Rs. {{ $employee->current_basic_salary }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-podcast" aria-hidden="true"></i> ETF No :</strong><span class="ml-2 text-bold">{{ $employee->etf_no }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-bullseye" aria-hidden="true"></i> UPF No :</strong><span class="ml-2 text-bold">{{ $employee->upf_no }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-gift" aria-hidden="true"></i> Pension No :</strong><span class="ml-2 text-bold">@if($employee->pension_reference_no == '')N/A @else{{ $employee->pension_reference_no }}@endif</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-graduation-cap" aria-hidden="true"></i> Payment Method :</strong><span class="ml-2 text-bold">{{ $employee->getPaymentMethod->category_name }}</span></p>

                        @elseif ($employee->employee_work_type == 140 || $employee->employee_work_type == 141 || $employee->employee_work_type == 142)

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-credit-card" aria-hidden="true"></i> Gross Salary :</strong><span class="ml-2 text-bold">Rs. {{ $employee->current_basic_salary }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-podcast" aria-hidden="true"></i> ETF No :</strong><span class="ml-2 text-bold">@if( $employee->etf_no == '')N/A @else{{  $employee->etf_no }}@endif</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-bullseye" aria-hidden="true"></i> UPF No :</strong><span class="ml-2 text-bold">@if( $employee->upf_no == '')N/A @else{{  $employee->upf_no }}@endif</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-gift" aria-hidden="true"></i> Pension No :</strong><span class="ml-2 text-bold">@if($employee->pension_reference_no == '')N/A @else{{ $employee->pension_reference_no }}@endif</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-graduation-cap" aria-hidden="true"></i> Payment Method :</strong><span class="ml-2 text-bold">{{ $employee->getPaymentMethod->category_name }}</span></p>

                        @endif


                </div> <!-- end card-box-->
                <div class="card-box">
                    <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>File Handling Information</h5></u>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-user-plus" aria-hidden="true"></i> Data Added By :</strong><span class="ml-2 text-bold">{{ $employee->getAddedOperator->initials }} {{ $employee->getAddedOperator->last_name }}  <span class="badge badge-dark">{{ $employee->added_ma_user_id }}</span></span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-calendar" aria-hidden="true"></i> Data Created Date :</strong><span class="ml-2 text-bold">{{ date("d-M-Y", strtotime($employee->added_ma_date)) }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-user-circle" aria-hidden="true"></i> Data Approved By :</strong><span class="ml-2 text-bold">{{ $employee->approved_ar_user_id }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-calendar" aria-hidden="true"></i> Data Approved Date :</strong><span class="ml-2 text-bold">@if($employee->approved_ar_date == '') N/A @else{{ date("d-M-Y", strtotime($employee->approved_ar_date)) }}@endif</span></p>

                        @if($employee->assign_ma_user_id != 0)
                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-id-badge" aria-hidden="true"></i> File Assigned To :</strong><span class="ml-2 text-bold">{{ $employee->getAssignOperator->initials }} {{ $employee->getAssignOperator->last_name }}  <span class="badge badge-dark">{{ $employee->assign_ma_user_id }}</span></span></p>
                        @else
                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-id-badge" aria-hidden="true"></i> File Assigned To :</strong><span class="ml-2 text-bold">No File Assign Operator </span></p>
                        @endif

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-calendar" aria-hidden="true"></i> Last File Assigned Date :</strong><span class="ml-2 text-bold">{{ date("d-M-Y", strtotime($employee->assign_ma_date)) }}</span></p>

                        <p class="mb-2 font-13"><strong class="text-danger"><i class="fa fa-calendar" aria-hidden="true"></i> Status :</strong><span class="ml-2 text-bold"> <button type="button" class="btn btn-success btn-sm">{{ $employee->empDecisionName->category_name }}</button></span></p>


                </div> <!-- end card-box-->
            </div> <!-- end col-->

            <div class="col-lg-8 col-xl-8">
                <div class="card-box">
                    <ul class="nav nav-pills navtab-bg">
                        <li class="nav-item">
                            <a href="#about-me" data-toggle="tab" aria-expanded="true" class="nav-link ml-0 active">
                                <i class="mdi mdi-face-profile mr-1"></i>Employee Basic
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#bonds" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>Bonds
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#increments" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>Increments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#promotions" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>Promotions/Service Records
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#transfers" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>Transfers
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#commendations" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>Commendations / Warning / Punishments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#leave" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>Leave
                            </a>
                        </li>
                        @role('super-admin')
                        <li class="nav-item">
                            <a href="#users" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>User Account Data
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#bank" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>Bank Data
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#salary" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>Salary Data
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#net" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>USJNet Data
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#ldap" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>Ldap Data
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#salaryRevision" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>Salary Revision 2025
                            </a>
                        </li>
                        @endrole
                        {{-- <li class="nav-item">
                            <a href="#settings" data-toggle="tab" aria-expanded="false" class="nav-link">
                                <i class="mdi mdi-settings-outline mr-1"></i>Settings
                            </a>
                        </li> --}}

                    </ul>

                    <div class="tab-content">

                        <div class="tab-pane show active" id="about-me">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                Personal Information</h5></u>

                                <p class="text-muted mb-2 font-13"><strong>Full Name :</strong> <span class="ml-2">{{ $employee->getNameTitle->category_name }} {{ $employee->name_denoted_by_initials }} {{ $employee->last_name }}</span></p>

                                @if ($employee->active_nic == 1)
                                <p class="text-muted mb-2 font-13"><strong>Old NIC :</strong><span class="ml-2 text-danger">{{ $employee->nic_old }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>New NIC :</strong><span class="ml-2">{{ $employee->nic_new }}</span></p>
                                @elseif ($employee->active_nic == 2)
                                <p class="text-muted mb-2 font-13"><strong>Old NIC :</strong><span class="ml-2">{{ $employee->nic_old }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>New NIC :</strong><span class="ml-2 text-danger">{{ $employee->nic_new }}</span></p>
                                @endif

                                <p class="text-muted mb-2 font-13"><strong>Mobile No:</strong><span class="ml-2">{{ $employee->mobile_no }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Residence Phone No :</strong> <span class="ml-2 ">@if($employee->telephone_no != ''){{ $employee->telephone_no }} @else N/A @endif</span></p>

                                <p class="text-muted mb-2 font-13"><strong>University Email :</strong> <span class="ml-2">{{ $employee->email }}</span></p>
                                @if ($employee->personal_email != '')
                                <p class="text-muted mb-2 font-13"><strong>Personal Email :</strong> <span class="ml-2">{{ $employee->personal_email }}</span></p>
                                @endif
                                <p class="text-muted mb-2 font-13"><strong>Permanent Address :</strong> <span class="ml-2">
                                    {{ $employee->permanent_add1 }}, @if($employee->permanent_add2 != '') {{ $employee->permanent_add2 }}, @endif @if($employee->permanent_add3 != '') {{ $employee->permanent_add3 }}, @endif {{ $employee->permanentCityName->name_en }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Postal Address :</strong> <span class="ml-2">
                                            {{ $employee->postal_add1 }}, @if($employee->postal_add2 != '') {{ $employee->postal_add2 }}, @endif @if($employee->postal_add3 != '') {{ $employee->postal_add3 }}, @endif {{ $employee->postalCityName->name_en }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Date of Birth :</strong> <span class="ml-2">{{ date("d-M-Y", strtotime($employee->date_of_birth)) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Gender :</strong> <span class="ml-2">{{ $employee->genderName->category_name }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Civil Status :</strong> <span class="ml-2">{{ $employee->civilStatusName->category_name }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Race :</strong> <span class="ml-2">{{ $employee->raceName->category_name }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Religion :</strong> <span class="ml-2">{{ $employee->religionName->category_name }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Citizenship :</strong> <span class="ml-2">Sri Lanka -[ {{ $employee->citizenshipeStateName->category_name }}] @if ($employee->state_of_citizenship_id == 26) With Reg No:{{ $employee->citizen_registration_no }} @endif</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Highest Educational Qualification :</strong> <span class="ml-2">{{ $employee->higheduName->category_name }}</span></p>
                                <br>

                                <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                    Working Place Information</h5></u>

                                    <p class="text-muted mb-2 font-13"><strong>Main Branch :</strong> <span class="ml-2">{{ $employee->mainBranch->category_name }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Faculty :</strong> <span class="ml-2">{{ $employee->getFacultyName->faculty_name }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Department :</strong> <span class="ml-2">{{ ucfirst($employee->getDepartmentName->department_name) }}</span></p>

                               <p class="text-muted mb-2 font-13"><strong>Sub Department :</strong> <span class="ml-2">
                                @foreach($subDepartments as $subDepartment)
                                    @if ($subDepartment->sub_department_code == $employee->sub_department_id )
                                    {{ ucfirst($subDepartment->sub_departmet_name)}}
                                    @endif
                                     @endforeach</span></p>
                            <br>

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                         Designation Information</h5></u>

                            <p class="text-muted mb-2 font-13"><strong>Designation (With Staff Grade) :</strong> <span class="ml-2">
                                @foreach($designations as $designation)
                                @if ( $designation->id == $employee->designation_id)
                                {{ ucfirst($designation->designation_name)}} - <span class="badge badge-dark">{{$designation->staff_grade }}</span>
                                @endif
                                @endforeach </span></p>

                                <p class="text-muted mb-2 font-13"><strong>Designation (With Salaray Code) :</strong> <span class="ml-2">
                                    @foreach($designations as $designation)
                                    @if ( $designation->id == $employee->designation_id)
                                    {{ ucfirst($designation->designation_name)}}  - <span class="badge badge-dark">{{ $designation->salary_code }}</span>
                                    @endif
                                    @endforeach </span></p>

                                @if ($employee->employee_work_type == 138 || $employee->employee_work_type == 139)
                                <p class="text-muted mb-2 font-13"><strong>Basic Salary (Current) :</strong> <span class="ml-2">Rs. {{ $employee->current_basic_salary }}</span></p>
                                @elseif ($employee->employee_work_type == 140 || $employee->employee_work_type == 141 || $employee->employee_work_type == 142)
                                <p class="text-muted mb-2 font-13"><strong>Gross Salary (Current) :</strong> <span class="ml-2">Rs. {{ $employee->current_basic_salary }}</span></p>
                                @endif


                                <p class="text-muted mb-2 font-13"><strong>Salary Scale (Active Version) :</strong> <span class="ml-2">
                                    {{ $scales?->salary_scale_txt ?? 'N/A' }} - <span class="badge badge-dark">{{ $scales?->version_id ?? 'N/A'}}</span>
                                </span></p>


                                <p class="text-muted mb-2 font-13"><strong>Designation Main Group :</strong> <span class="ml-2">
                                        @foreach($designations as $designation)
                                        @if ( $designation->id == $employee->designation_id)
                                        <span class="badge badge-primary"  style="font-size: 14px;">{{ $designation->main_group }}</span>
                                        @endif
                                @endforeach </span></p>

                                <p class="text-muted mb-2 font-13"><strong>Designation Salary Grade :</strong> <span class="ml-2">
                                    @foreach($designations as $designation)
                                    @if ( $designation->id == $employee->designation_id)
                                    <span class="badge badge-primary"  style="font-size: 14px;">{{ $designation->grade_id }}</span>
                                    @endif
                            @endforeach </span></p>

                            <p class="text-muted mb-2 font-13"><strong>Designation Category (UGC MIS) :</strong> <span class="ml-2">
                                @foreach($designations as $designation)
                                @if ( $designation->id == $employee->designation_id)
                                <span class="badge badge-primary"  style="font-size: 14px;">{{ $designation->ugc_mis }}</span>
                                @endif
                        @endforeach </span></p>

                        <p class="text-muted mb-2 font-13"><strong>Designation Category (UGC Finance) :</strong> <span class="ml-2">
                            @foreach($designations as $designation)
                            @if ( $designation->id == $employee->designation_id)
                            <span class="badge badge-primary"  style="font-size: 14px;">{{ $designation->ugc_finance }}</span>
                            @endif
                    @endforeach </span></p>

                    <p class="text-muted mb-2 font-13"><strong>Designation Service Category :</strong> <span class="ml-2">
                        @foreach($designations as $designation)
                        @if ( $designation->id == $employee->designation_id)
                        <span class="badge badge-primary"  style="font-size: 14px;">{{ $designation->service_category }}</span>
                        @endif
                @endforeach </span></p>
                    <br>
                    <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                        Service Record Information</h5></u>
                        @if ($employee->employee_work_type == 138 || $employee->employee_work_type == 139)
                        <p class="text-muted mb-2 font-13"><strong>Appointment Date Permanent (Initial) :</strong> <span class="ml-2">{{ date("d-M-Y", strtotime($employee->initial_appointment_date)) }}</span></p>
                        <p class="text-muted mb-2 font-13"><strong>Appointment Date (Continue Service) :</strong> <span class="ml-2">{{ date("d-M-Y", strtotime($employee->gratuity_cal_date)) }}</span></p>
                        <p class="text-muted mb-2 font-13"><strong>Duty Assumed Date (Current) :</strong> <span class="ml-2">{{ date("d-M-Y", strtotime($employee->current_appointment_date)) }}</span></p>
                        <p class="text-muted mb-2 font-13"><strong>Termination Date (Service) :</strong> <span class="ml-2">@if($employee->salary_termination_date_1 == '1970-01-01' || $employee->salary_termination_date_1 == NULL) N/A @else{{ date("d-M-Y", strtotime($employee->salary_termination_date_1)) }} @endif</span></p>
                        <p class="text-muted mb-2 font-13"><strong>Termination Date (Leave) :</strong> <span class="ml-2">@if($employee->salary_termination_date_2 == '1970-01-01' || $employee->salary_termination_date_2 == NULL) N/A @else{{ date("d-M-Y", strtotime($employee->salary_termination_date_2)) }} @endif</span></p>
                        <p class="text-muted mb-2 font-13"><strong>Retirement Date :</strong> <span class="ml-2">{{ date("d-M-Y", strtotime($employee->retirement_date)) }}</span></p>
                        <p class="text-muted mb-2 font-13"><strong>Increment Date :</strong> <span class="ml-2">{{ $employee->increment_date }}</span></p>

                        @elseif ($employee->employee_work_type == 140 || $employee->employee_work_type == 142)
                        <p class="text-muted mb-2 font-13"><strong>Service Start Date :</strong> <span class="ml-2">{{ date("d-M-Y", strtotime($employee->current_appointment_date)) }}</span></p>
                        <p class="text-muted mb-2 font-13"><strong>Service Termination Date :</strong> <span class="ml-2">{{ date("d-M-Y", strtotime($employee->salary_termination_date_1)) }}</span></p>
                        @elseif ($employee->employee_work_type == 141)
                        <p class="text-muted mb-2 font-13"><strong>Contract Start Date :</strong> <span class="ml-2">{{ date("d-M-Y", strtotime($employee->current_appointment_date)) }}</span></p>
                        <p class="text-muted mb-2 font-13"><strong>Contract End Date :</strong> <span class="ml-2">{{ date("d-M-Y", strtotime($employee->salary_termination_date_1)) }}</span></p>
                        @if($employee->sabbatical_start != NULL && $employee->sabbatical_start != '1970-01-01')
                        <p class="text-muted mb-2 font-13"><strong>Sabbatical Start Date :</strong> <span class="ml-2">{{ date("d-M-Y", strtotime($employee->sabbatical_start)) }}</span></p>
                        @endif
                        @if($employee->sabbatical_end != NULL && $employee->sabbatical_start != '1970-01-01')
                        <p class="text-muted mb-2 font-13"><strong>Sabbatical End Date :</strong> <span class="ml-2">{{ date("d-M-Y", strtotime($employee->sabbatical_end)) }}</span></p>
                        @endif
                        @endif


                        @if ($employee->vacancy_id != '')

                        <br>
                        <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                            Vacancy Related Information</h5></u>

                        <p class="text-muted mb-2 font-13"><strong>Vacancy Id :</strong> <span class="ml-2">{{ $employee->vacancy_id }}</span></p>

                        <p class="text-muted mb-2 font-13"><strong>Application Referance No :</strong> <span class="ml-2">{{ $employee->application_referance_no }}</span></p>
                        @endif
                        <br>
                        <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                            Additional Information</h5></u>

                        <p class="text-muted mb-2 font-13"><strong>User Account Status :</strong> <span class="ml-2"><span class="badge badge-dark" style="font-size: 14px;">{{ $employee->user_account_status == 1 ? 'Active' : 'NoAccount' }}</span></span></p>

                        <p class="text-muted mb-2 font-13"><strong>Zoom Account Status :</strong> <span class="ml-2"><span class="badge badge-dark" style="font-size: 14px;">{{ $employee->zoom_active_status == 1 ? 'Active' : 'NoZoom' }}</span></span></p>

                        <p class="text-muted mb-2 font-13"><strong>Taxpayer Identification Number :</strong> <span class="ml-2">{{ $employee->tin_no != 0 ? $employee->tin_no : 'N/A' }}</span></p>
                        </div>
                        <!-- end timeline content-->

                        <div class="tab-pane show" id="bonds">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                Employee Bond</h5></u>
                            @if($empBonds->count() > 0)
                            @foreach ($empBonds as $empBond)

                            <ul class="list-unstyled timeline-sm">
                                <li class="timeline-sm-item">
                                    <span class="timeline-sm-date">@if($empBond->effictive_date == '1970-01-01') Effective Date @else{{ date("d-M-Y", strtotime($empBond->effictive_date)) }}@endif</span>
                                    <h3 class="mt-0 mb-1">{{ $empBond->BondCategory->category_name }}</h3>
                                    <h5>@if($empBond->bond_type == '') Course Participate @else{{ $empBond->BondType->category_name }} @endif - <span class="text-dark">[ {{$empBond->obligated_service_period_year}} Year {{$empBond->obligated_service_period_month != 0 ? $empBond->obligated_service_period_month : 0 }} month ]</span></h5>
                                    <hr style="height: 2px;
                                    background: black;
                                    margin: 20px 0;">
                                    @if($empBond->bond_category_id == 164)
                                    <p class="mb-2 font-13"><strong class="text-danger"> Course Fee Value :</strong><span class="ml-2 text-bold">Rs.{{ number_format($empBond->salary_value, 2)}}</span></p>
                                    @else
                                    <p class="mb-2 font-13"><strong class="text-danger"> Salary Value :</strong><span class="ml-2 text-bold">Rs.{{ number_format($empBond->salary_value, 2)}}</span></p>
                                    @endif

                                    <p class="mb-2 font-13"><strong class="text-danger"> Scholorship Value :</strong><span class="ml-2 text-bold">Rs.{{ number_format($empBond->scholorship_value, 2)}}</span></p>
                                    <p class="mb-2 font-13"><strong class="text-danger"> Other Value :</strong><span class="ml-2 text-bold">Rs.{{ number_format($empBond->other_value, 2)}} @if ($empBond->other_value != '')
                                        @if($empBond->other_bond_description != '')( {{ $empBond->other_bond_description }} ) @endif
                                    @endif</span></p>
                                    <p class="mb-2 font-13"><strong class="text-danger"> Tolal Bond Value :</strong><span class="ml-2 text-bold">Rs.{{ number_format($empBond->total_value, 2)}}</span></p>
                                    <p class="mb-2 font-13"><strong class="text-danger"> Bond Violation and Legal Fee :</strong><span class="ml-2 text-bold">Rs.{{ number_format($empBond->bond_violation_fee, 2)}}</span></p>
                                    <p class="mb-2 font-13"><strong class="text-danger"> Tolal Bond Value + Bond Violation and Legal Fee:</strong><span class="ml-2 text-bold">Rs.{{ number_format($empBond->total_value + $empBond->bond_violation_fee, 2)}}</span></p>
                                    <p class="mb-2 font-13"><strong class="text-danger"> Effective Date :</strong><span class="ml-2 text-bold">@if($empBond->effictive_date == '1970-01-01') N/A @else{{ date("d-M-Y", strtotime($empBond->effictive_date)) }}@endif</span></p>
                                    <p class="mb-2 font-13"><strong class="text-danger"> End Date :</strong><span class="ml-2 text-bold">@if($empBond->end_date == '1970-01-01') N/A @else{{ date("d-M-Y", strtotime($empBond->end_date)) }}@endif</span></p>
                                    <p class="mb-2 font-13"><strong class="text-danger"> Bond Status:</strong><span class="ml-2 text-bold">@if ($empBond->status == 1)
                                        <button type="button" class="btn btn-success">Active</button>
                                        @elseif($empBond->status == 0)
                                        <button type="button" class="btn btn-danger">Complete</button>
                                        @elseif($empBond->status == 2)
                                        <button type="button" class="btn btn-dark">Effective Date No Set</button>
                                        @endif</span></p>

                                </li>

                            </ul>
                            <ul class="list-unstyled timeline-sm">
                                <li class="timeline-sm-item">
                                    <span class="timeline-sm-date">@if($empBond->end_date == '1970-01-01') End Date @else{{ date("d-M-Y", strtotime($empBond->end_date)) }}@endif</span>
                                </li>
                            </ul>
                            @endforeach
                            @else
                            <h5 class="mb-2 font-13"><strong class="text-danger">Bond Data not Found</strong></h5>
                            @endif

                           <br>
                            <u><h5 class="mb-3 mt-4 text-uppercase"><i class="mdi mdi-cards-variant mr-1"></i>
                                Bond University</h5></u>
                            <div class="table-responsive">
                                <table class="table table-borderless mb-0">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>#</th>
                                            <th>Institute Name</th>
                                            <th>Bond Value</th>
                                            <th>Bond Duration</th>
                                            <th>Effective Date</th>
                                        </tr>
                                    </thead>
                                    @foreach ($empBondUniversities as $key => $empBondUniversity)
                                    <tbody>
                                        <tr>
                                            <td>{{ $key+1 }}</td>
                                            <td>{{ $empBondUniversity->institute_name }}</td>
                                            <td>Rs.{{ number_format($empBondUniversity->bond_value, 2)}}</td>
                                            <td>{{$empBondUniversity->bond_year}} Y - {{$empBondUniversity->bond_month != 0 ? $empBondUniversity->bond_month : 0 }} M</td>
                                            <td>@if($empBondUniversity->bond_effictive_date == '1970-01-01') - @else{{ date("d-M-Y", strtotime($empBondUniversity->bond_effictive_date)) }}@endif</td>
                                        </tr>

                                    </tbody>
                                    @endforeach
                                </table>
                            </div>

                        </div>

                        <div class="tab-pane" id="increments">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                Employee Increments</h5></u>
                            @if($increments->count() > 0)
                            @foreach ($increments as $increment)
                            @if($loop->first)
                            <ul class="list-unstyled timeline-sm">
                                <li class="timeline-sm-item">
                                    <span class="timeline-sm-date">{{ date('d-M-Y', strtotime('+1 year', strtotime($increment->effective_date)) ) }}</span>
                                    <h4 class="mt-0 mb-1">
                                        @if(date("d-M-Y") < date('d-M-Y', strtotime('+1 year', strtotime($increment->effective_date)) ) )
                                        <button type="button" class="btn btn-primary">Waiting For Next Increment - <span class="badge badge-light">{{ date_diff(date_create(date('d-M-Y', strtotime('+1 year', strtotime($increment->effective_date)) )), date_create(date("d-M-Y")))->format('%y years %m months and %d days') }}</span></button>
                                        @else
                                        <button type="button" class="btn btn-dark">Next Increment Date Reached  - <span class="badge badge-light">{{ date_diff(date_create(date("d-M-Y")),date_create(date('d-M-Y', strtotime('+1 year', strtotime($increment->effective_date)) )))->format('%y years %m months and %d days') }}</span></button>
                                        @endif
                                    </h4>
                                </li>

                            </ul>
                            @endif
                            <ul class="list-unstyled timeline-sm">
                                <li class="timeline-sm-item">
                                    <span class="timeline-sm-date">{{ date("d-M-Y", strtotime($increment->effective_date)) }}</span>
                                    <h4 class="mt-0 mb-1"> @foreach($designations as $designation)
                                        @if ( $designation->id == $increment->desgnation_id)
                                        {{ ucfirst($designation->designation_name)}} - <span class="badge badge-dark">{{$designation->staff_grade }}</span> - <span class="badge badge-primary">{{ $designation->salary_code }}</span>
                                        @endif
                                        @endforeach</h4>

                                        <h5>@if($increment->increment_type == 329)
                                            <button type="button" class="btn btn-success">Annual Increment</button>
                                        @elseif ($increment->increment_type == 330)
                                        <button type="button" class="btn btn-info">Degree Increment</button>
                                        @elseif ($increment->increment_type == 331)
                                        <button type="button" class="btn btn-dark">Promotion Increment</button>
                                        @elseif ($increment->increment_type == 0)
                                        <button type="button" class="btn btn-secondary">Increment Type Not Selected</button>
                                        @endif</span> -
                                        @if($increment->decision == 172)
                                            <button type="button" class="btn btn-success">Approved</button>
                                        @elseif ($increment->decision == 173)
                                        <button type="button" class="btn btn-danger">Suspension of Increment</button>
                                        @elseif ($increment->decision == 174)
                                        <button type="button" class="btn btn-danger">Stoppage of Increment</button>
                                        @elseif ($increment->decision == 175)
                                        <button type="button" class="btn btn-danger">Reduction of Increment</button>
                                        @elseif ($increment->decision == 176)
                                        <button type="button" class="btn btn-danger">Deferment of Increment</button>
                                        @endif</span>
                                        </h5>
                                    <hr style="height: 2px;
                                    background: black;
                                    margin: 20px 0;">

                                    {{-- <p class="mb-2 font-13"><strong class="text-danger"> Basic Salary (Before Increment) :</strong><span class="ml-2 text-bold">Rs.{{ number_format($increment->salary_step, 2)}}</span></p> --}}
                                    <p class="mb-2 font-13"><strong class="text-danger"> Increment Value :</strong><span class="ml-2 text-bold">Rs.{{ number_format($increment->increment_value, 2)}}</span></p>
                                    <p class="mb-2 font-13"><strong class="text-danger"> Basic Salary (After Increment) :</strong><span class="ml-2 text-bold">Rs.{{ number_format($increment->basic_sal, 2)}}</span></p>
                                    <p class="mb-2 font-13"><strong class="text-danger"> Effective Date :</strong><span class="ml-2 text-bold">@if($increment->effective_date == '1970-01-01') N/A @else{{ date("d-M-Y", strtotime($increment->effective_date)) }}@endif</span></p>
                                    <p class="mb-2 font-13"><strong class="text-danger"> Increment Reason :</strong><span class="ml-2 text-bold">{{ $increment->reason }}</span></p>

                                    @if($increment->effective_date_arrears == '1970-01-01')
                                    @else
                                    <p class="mb-2 font-13"><strong class="text-danger"> Effective Date Arrears Increment :</strong><span class="ml-2 text-bold">{{ $increment->effective_date_arrears }}</span></p>
                                    @endif
                                    @if($increment->period == '' || $increment->period == 0)
                                    @else
                                    <p class="mb-2 font-13"><strong class="text-danger"> Increment Holding Period :</strong><span class="ml-2 text-bold">{{ $increment->period }} Month</span></p>
                                    @endif
                                </li>

                            </ul>

                            @endforeach
                            @else
                            <h5 class="mb-2 font-13"><strong class="text-danger">Increment Data not Found</strong></h5>
                            @endif

                        </div>

                        <div class="tab-pane" id="promotions">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                Employee Promotions / Employment records</h5></u>
                            @if($promotions->count() > 0)
                            @foreach ($promotions as $promotion)

                            <ul class="list-unstyled timeline-sm">
                                <li class="timeline-sm-item">
                                   @if($promotion->last_working_date != '1970-01-01')
                                   <span class="timeline-sm-date"> {{ date("d-M-Y", strtotime($promotion->last_working_date)) }}</span>
                                    @endif
                                    <h4 class="mt-0 mb-1"> @foreach($designations as $designation)
                                        @if ( $designation->id == $promotion->designation_id)
                                        {{ ucfirst($designation->designation_name)}} - <span class="badge badge-dark">{{$designation->staff_grade }}</span> - <span class="badge badge-primary">{{ $designation->salary_code }}</span>
                                        @endif
                                        @endforeach</h4>

                                    @if($promotion->type_id == 177)
                                    <button type="button" class="btn btn-success">{{ $promotion->promotionServiceTypeName->category_name }}</button>
                                    @elseif ($promotion->type_id == 207)
                                    <button type="button" class="btn bg-purple">{{ $promotion->promotionServiceTypeName->category_name }}</button>
                                    @elseif ($promotion->type_id == 178)
                                    <button type="button" class="btn btn-primary">{{ $promotion->promotionServiceTypeName->category_name }}</button>
                                    @else
                                    <button type="button" class="btn bg-yellow">{{ $promotion->promotionServiceTypeName->category_name }}</button>
                                    @endif
                                    <hr style="height: 2px;
                                    background: black;
                                    margin: 20px 0;">
                                    @if ($promotion->type_id != 207)
                                    <p class="mb-2 font-13"><strong class="text-danger"> Basic Salary  :</strong><span class="ml-2 text-bold">Rs.{{ number_format($promotion->basic_salary, 2)}}</span></p>
                                    @endif


                                    <p class="mb-2 font-13"><strong class="text-danger"> Duty Assumed Date :</strong><span class="ml-2 text-bold">@if($promotion->duty_assumed_date == '1970-01-01') N/A @else{{ date("d-M-Y", strtotime($promotion->duty_assumed_date)) }}@endif</span></p>

                                    <p class="mb-2 font-13"><strong class="text-danger"> Descriptions  :</strong><span class="ml-2 text-bold">{{ $promotion->descriptions }}</span></p>

                                    @if($promotion->last_working_date == '1970-01-01')
                                    @else
                                    <p class="mb-2 font-13"><strong class="text-danger"> Service Break Date :</strong><span class="ml-2 text-bold">{{ date("d-M-Y", strtotime($promotion->last_working_date)) }}</span></p>
                                    <button type="button" class="btn btn-sm btn-danger">Servive Brake</button>
                                    @endif

                                </li>

                            </ul>

                            <ul class="list-unstyled timeline-sm">
                                <li class="timeline-sm-item">

                                <span class="timeline-sm-date">{{ date("d-M-Y", strtotime($promotion->duty_assumed_date)) }}</span>

                                </li>
                            </ul>


                            @endforeach
                            @else
                            <h5 class="mb-2 font-13"><strong class="text-danger">Promotions Data not Found</strong></h5>
                            @endif

                        </div>

                        <div class="tab-pane" id="transfers">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                Internal Transfers</h5></u>
                            @if($internalTransfers->count() > 0)
                            @foreach ($internalTransfers as $internalTransfer)
                            <ul class="list-unstyled timeline-sm">
                                <li class="timeline-sm-item">
                                    <span class="timeline-sm-date">{{ date("d-M-Y", strtotime($internalTransfer->transfer_date)) }}</span>
                                    <h4 class="mt-0 mb-1">Internal Transfer To -<button type="button" class="btn btn-md btn-primary"> @foreach($departments as $department)
                                        @if ($department->id == $internalTransfer->dep_id)
                                        {{ ucfirst($department->department_name)}}
                                        @endif
                                         @endforeach</button></h4>

                                    <hr style="height: 2px;
                                    background: black;
                                    margin: 20px 0;">

                                    <p class="mb-2 font-13"><strong class="text-danger"> Transfer Date :</strong><span class="ml-2 text-bold">{{ date("d-M-Y", strtotime($internalTransfer->transfer_date)) }}</span></p>
                                    <p class="mb-2 font-13"><strong class="text-danger"> Descriptions  :</strong><span class="ml-2 text-bold">{{ $internalTransfer->descriptions }}</span></p>

                                </li>

                            </ul>

                            @endforeach
                            @else
                            <h5 class="mb-2 font-13"><strong class="text-danger">Internal Transfers Data not Found</strong></h5>
                            @endif

                            <br>
                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                External Transfers</h5></u>
                                @if($externalTransfers->count() > 0)
                                @foreach ($externalTransfers as $externalTransfer)
                                <ul class="list-unstyled timeline-sm">
                                    <li class="timeline-sm-item">
                                        <span class="timeline-sm-date">{{ date("d-M-Y", strtotime($externalTransfer->effective_date)) }}</span>
                                        <h4 class="mt-0 mb-1"> @foreach($designations as $designation)
                                            @if ( $designation->id == $externalTransfer->designation_id)
                                            {{ ucfirst($designation->designation_name)}} - <span class="badge badge-dark">{{$designation->staff_grade }}</span> - <span class="badge badge-primary">{{ $designation->salary_code }}</span>
                                            @endif
                                            @endforeach</h4>

                                            <button type="button" class="btn btn-success">{{ $externalTransfer->statusName->category_name }}</button>

                                        <hr style="height: 2px;
                                        background: black;
                                        margin: 20px 0;">

                                        <p class="mb-2 font-13"><strong class="text-danger"> University /Institue :</strong><span class="ml-2 text-bold">{{ $externalTransfer->universityName->uni_name }}</span></p>
                                        <p class="mb-2 font-13"><strong class="text-danger"> Bond :</strong><span class="ml-2 text-bold">{{ $externalTransfer->bondExist->category_name }}</span></p>
                                        <p class="mb-2 font-13"><strong class="text-danger"> Effective Date :</strong><span class="ml-2 text-bold">{{  date("d-M-Y", strtotime($externalTransfer->effective_date)) }}</span></p>


                                    </li>

                                </ul>

                                @endforeach
                                @else
                                <h5 class="mb-2 font-13"><strong class="text-danger">External Transfers Data not Found</strong></h5>
                                @endif

                        </div>

                        <div class="tab-pane" id="commendations">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                Commendation / Warning / Punishment</h5></u>
                                @if($commendations->count() > 0)
                                @foreach ($commendations as $commendation)
                                <ul class="list-unstyled timeline-sm">
                                    <li class="timeline-sm-item">
                                        <span class="timeline-sm-date">{{ date("d-M-Y", strtotime($commendation->letter_date)) }}</span>

                                            <button type="button" class="btn btn-dark">{{ $commendation->commendationTypeName->category_name }}</button>


                                        <hr style="height: 2px;
                                        background: black;
                                        margin: 20px 0;">

                                        <p class="mb-2 font-13"><strong class="text-danger"> Letter Date :</strong><span class="ml-2 text-bold">{{  date("d-M-Y", strtotime($commendation->letter_date)) }}</span></p>
                                        <p class="mb-2 font-13"><strong class="text-danger"> Officer Name :</strong><span class="ml-2 text-bold">{{ $commendation->officer_name }}</span></p>
                                        <p class="mb-2 font-13"><strong class="text-danger"> Officer Position :</strong><span class="ml-2 text-bold">{{ $commendation->officer_position }}</span></p>
                                        <p class="mb-2 font-13"><strong class="text-danger"> Commandation Note :</strong><span class="ml-2 text-bold">{{ $commendation->description }}</span></p>
                                        @if($commendation->page_no != 0)
                                        <button type="button" class="btn btn-primary">Page No: <span class="badge badge-light">{{ $commendation->page_no }}</span></button>
                                        @endif

                                    </li>

                                </ul>

                                @endforeach
                                @else
                                <h5 class="mb-2 font-13"><strong class="text-danger">Commendations Data not Found</strong></h5>
                                @endif

                        </div>

                        <div class="tab-pane" id="leave">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                Employee Leave</h5></u>
                                @if($leaves->count() > 0)
                                <div class="table-responsive">
                                    <table id="example2" class="table table-bordered">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>Year</th>
                                                <th>Casual Leave</th>
                                                <th>Sick Leave</th>
                                                <th>Medical Leave</th>
                                                <th>No Pay</th>
                                                <th>Study Leave</th>
                                            </tr>
                                        </thead>
                                        @foreach ($leaves as $key => $leave)
                                        <tbody>
                                            <tr>
                                                <td>{{ $leave->year }}</td>
                                                <td>{{ $leave->casual }}</td>
                                                <td>{{ $leave->sick }}</td>
                                                <td>{{ $leave->medical }}</td>
                                                <td>{{ $leave->nopay }}</td>
                                                <td>{{ $leave->study }}</td>
                                            </tr>

                                        </tbody>
                                        @endforeach
                                    </table>
                                </div>
                                @else
                                <h5 class="mb-2 font-13"><strong class="text-danger">Leave Data not Found</strong></h5>
                                @endif

                        </div>

                        <div class="tab-pane" id="users">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                User Account Details</h5></u>
                                @if(isset($users) && $users->count() > 0)
                                <p class="text-muted mb-2 font-13"><strong>User Id :</strong><span class="ml-2">{{$users->id }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>User Roles :</strong><span class="ml-2">@if ($users->roles)
                                    @foreach ($users->roles as $user_role)

                                        <span class="badge badge-pill badge-dark" style="font-size: 13px;">{{ $user_role->name }} </span>

                                    @endforeach
                                    @endif</span></p>

                                <p class="text-muted mb-2 font-13"><strong>User Email :</strong><span class="ml-2">{{ $users->email }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>User Blongs Branch:</strong><span class="ml-2">{{ $users->mainBranchName->category_name }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>User Identification Code :</strong><span class="ml-2">{{ $users->my_number }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>User Last Seen :</strong><span class="ml-2">@if($users->last_login_ip != ''){{ \Carbon\Carbon::parse($users->last_seen)->diffForHumans() }}- [{{ date("d-M-Y H:m", strtotime($users->last_seen)) }}] @endif</span></p>

                                <p class="text-muted mb-2 font-13"><strong>User Login IP :</strong><span class="ml-2">{{ $users->last_login_ip }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>User Last Login Time :</strong><span class="ml-2">{{ \Carbon\Carbon::parse($users->last_login_at)->diffForHumans() }} -[{{ date("d-M-Y H:m", strtotime($users->last_login_at)) }}]</span></p>

                                <p class="text-muted mb-2 font-13"><strong>User Account Status:</strong> <span class="ml-2 ">@if ($users->status_id == 1)
                                    <button type="button" class="btn btn-success">Active</button>
                                    @else
                                    <button type="button" class="btn btn-danger">Inactive</button>
                                @endif</span></p>
                                @else
                                <h5 class="mb-2 font-13"><strong class="text-danger">Currently No User Account Found</strong></h5>
                                @endif

                        </div>

                        <div class="tab-pane" id="bank">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                Salary Bank Details</h5></u>
                                @if($bankData['bank'] != '')
                                <p class="text-muted mb-2 font-13"><strong>Bank Name :</strong><span class="ml-2">{{ $bankData['bank'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Bank Code :</strong><span class="ml-2">{{ $bankData['bank_code'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Branch Name:</strong><span class="ml-2">{{ $bankData['branch'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Branch Code :</strong><span class="ml-2">{{ $bankData['branch_code'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Account No :</strong> <span class="ml-2 ">{{ $bankData['acc_no'] }}</span></p>
                                @else
                                <h5 class="mb-2 font-13"><strong class="text-danger">Bank Data not Found</strong></h5>
                                @endif

                        </div>

                        <div class="tab-pane" id="salary">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                Salary Details [Active Employee]</h5></u>

                                @if($activSalaryData['name'] != '')
                                <p class="text-muted mb-2 font-13"><strong> Salary Status: </strong><button type="button" class="btn btn-success">Active</button></p>

                                <p class="text-muted mb-2 font-13"><strong> Name with initials:</strong><span class="ml-2">{{ $activSalaryData['name'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Designation :</strong><span class="ml-2">{{ $activSalaryData['desig'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Department:</strong><span class="ml-2">{{ $activSalaryData['dept'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>NIC :</strong><span class="ml-2">{{ $activSalaryData['nic'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Email :</strong> <span class="ml-2 ">{{ $activSalaryData['email'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Original Basic Salary :</strong> <span class="ml-2 ">Rs. {{ $activSalaryData['obsal'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Current Basic Salary :</strong> <span class="ml-2 ">Rs. {{ $activSalaryData['bsal'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Appointment Date :</strong> <span class="ml-2 ">{{ $activSalaryData['appDate'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Terminatement Date :</strong> <span class="ml-2 ">{{ $activSalaryData['terminateDate'] }}</span></p>
                                @else
                                <p class="text-muted mb-2 font-13"><strong> Salary Status: </strong><button type="button" class="btn btn-danger">inactive</button></p>
                                @endif

                                <br>

                                <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                    Salary Details [All Employee]</h5></u>

                                    @if($allSalaryData['name'] != '')

                                    <p class="text-muted mb-2 font-13"><strong> Name with initials:</strong><span class="ml-2">{{ $allSalaryData['name'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Designation :</strong><span class="ml-2">{{ $allSalaryData['desig'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>NIC :</strong><span class="ml-2">{{ $allSalaryData['nic'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Email :</strong> <span class="ml-2 ">{{ $allSalaryData['email'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Original Basic Salary :</strong> <span class="ml-2 ">Rs. {{ $allSalaryData['obsal'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Current Basic Salary :</strong> <span class="ml-2 ">Rs. {{ $allSalaryData['bsal'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>ETF No :</strong> <span class="ml-2 ">{{ $allSalaryData['nETFNo'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>UPF No :</strong> <span class="ml-2 ">{{ $allSalaryData['strUpfNo'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Pension No :</strong> <span class="ml-2 ">{{ $allSalaryData['strPentionNumber'] }}</span></p>
                                    @else
                                    <h5 class="mb-2 font-13"><strong class="text-danger">Salary Data not Found</strong></h5>
                                    @endif

                        </div>

                        <div class="tab-pane" id="net">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                USJNet Details [Employee No]</h5></u>

                                @if($usjnetDataEmp['sjpmail'] != '')

                                <img src="https://usjnetsso.sjp.ac.lk/crop_and_upload2/upload/{{ $usjnetDataEmp['user_profileName'] }}" class="img-responsive mx-auto d-block img-fluid border border-dark img-thumbnail">

                                <p class="text-muted mb-2 font-13"><strong> Account Creation Status: </strong><button type="button" class="btn btn-success">Created</button></p>

                                <p class="text-muted mb-2 font-13"><strong> Name with initials:</strong><span class="ml-2">{{ $usjnetDataEmp['namewithinitials'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Full Name:</strong><span class="ml-2">{{ $usjnetDataEmp['fname'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Designation :</strong><span class="ml-2">{{ $usjnetDataEmp['designation'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Faculty:</strong><span class="ml-2">{{ $usjnetDataEmp['faculty'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Department:</strong><span class="ml-2">{{ $usjnetDataEmp['department'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>User Name :</strong><span class="ml-2">{{ $usjnetDataEmp['username'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>NIC :</strong><span class="ml-2">{{ $usjnetDataEmp['nic'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>SJP Email :</strong> <span class="ml-2 ">{{ $usjnetDataEmp['sjpmail'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Alternative Email :</strong> <span class="ml-2 ">{{ $usjnetDataEmp['altmail'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Mobile :</strong> <span class="ml-2 ">{{ $usjnetDataEmp['tp'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Lecture or Not :</strong> <span class="ml-2 ">{{ $usjnetDataEmp['lectureOrNot'] == 0 ? 'No' : 'Yes' }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Terminated Date :</strong> <span class="ml-2 ">{{ $usjnetDataEmp['terminateDate'] == '' ? ' ' : $usjnetDataEmp['terminateDate'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Password Update Status :</strong> <span class="ml-2 ">{{ $usjnetDataEmp['updatepw'] == 1 ? 'Yes' : 'No' }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Expired Password Update Status :</strong> <span class="ml-2 ">{{ $usjnetDataEmp['expirepwupdated'] == 1 ? 'Yes' : 'No' }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Block Time Count :</strong> <span class="ml-2 ">{{ $usjnetDataEmp['block_time'] == 0 ? 'No Blocking' : $usjnetDataEmp['block_time'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Account Active Status :</strong> <span class="ml-2 ">{{ $usjnetDataEmp['accState'] == 0 ? 'Yes' : 'No' }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Account Completed Status :</strong> <span class="ml-2 ">{{ $usjnetDataEmp['isCompleted'] == 1 ? 'Yes' : 'No' }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>LDAP Updated Status :</strong> <span class="ml-2 ">{{ $usjnetDataEmp['isldapUpdated'] == 1 ? 'Yes' : 'No' }}</span></p>
                                @else
                                <p class="text-muted mb-2 font-13"><strong> Account Creation Status: </strong><button type="button" class="btn btn-danger">Not Created Yet</button></p>
                                @endif

                                <br>

                                <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                    USJNet Details [SJP Email]</h5></u>

                                    @if($usjnetDataEmail['reg_no'] != '')

                                    <img src="https://usjnetsso.sjp.ac.lk/crop_and_upload2/upload/{{ $usjnetDataEmp['user_profileName'] }}" class="img-responsive mx-auto d-block img-fluid border border-dark img-thumbnail">

                                    <p class="text-muted mb-2 font-13"><strong> Account Creation Status: </strong><button type="button" class="btn btn-success">Created</button></p>

                                    <p class="text-muted mb-2 font-13"><strong> Name with initials:</strong><span class="ml-2">{{ $usjnetDataEmail['namewithinitials'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Full Name:</strong><span class="ml-2">{{ $usjnetDataEmail['fname'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Designation :</strong><span class="ml-2">{{ $usjnetDataEmail['designation'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Faculty:</strong><span class="ml-2">{{ $usjnetDataEmail['faculty'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Department:</strong><span class="ml-2">{{ $usjnetDataEmail['department'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>User Name :</strong><span class="ml-2">{{ $usjnetDataEmail['username'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>NIC :</strong><span class="ml-2">{{ $usjnetDataEmail['nic'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Alternative Email :</strong> <span class="ml-2 ">{{ $usjnetDataEmail['altmail'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Mobile :</strong> <span class="ml-2 ">{{ $usjnetDataEmail['tp'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Lecture or Not :</strong> <span class="ml-2 ">{{ $usjnetDataEmail['lectureOrNot'] == 0 ? 'No' : 'Yes' }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Terminated Date :</strong> <span class="ml-2 ">{{ $usjnetDataEmail['terminateDate'] == '' ? ' ' : $usjnetDataEmail['terminateDate'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Password Update Status :</strong> <span class="ml-2 ">{{ $usjnetDataEmail['updatepw'] == 1 ? 'Yes' : 'No' }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Expired Password Update Status :</strong> <span class="ml-2 ">{{ $usjnetDataEmail['expirepwupdated'] == 1 ? 'Yes' : 'No' }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Block Time Count :</strong> <span class="ml-2 ">{{ $usjnetDataEmail['block_time'] == 0 ? 'No Blocking' : $usjnetDataEmail['block_time'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Account Active Status :</strong> <span class="ml-2 ">{{ $usjnetDataEmail['accState'] == 0 ? 'Yes' : 'No' }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Account Completed Status :</strong> <span class="ml-2 ">{{ $usjnetDataEmail['isCompleted'] == 1 ? 'Yes' : 'No' }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>LDAP Updated Status :</strong> <span class="ml-2 ">{{ $usjnetDataEmail['isldapUpdated'] == 1 ? 'Yes' : 'No' }}</span></p>
                                    @else
                                    <p class="text-muted mb-2 font-13"><strong> Account Creation Status: </strong><button type="button" class="btn btn-danger">Not Created Yet</button></p>
                                    @endif

                                    <br>

                                <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                    USJNet Login Status [LDAP Auth]</h5></u>

                                    @if($getLoginStatusData['email'] != '')

                                    <p class="text-muted mb-2 font-13"><strong> Account Login Status: </strong><button type="button" class="btn btn-success">Login Successfully</button></p>

                                    <p class="text-muted mb-2 font-13"><strong> Name :</strong><span class="ml-2">{{ $getLoginStatusData['name'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>User Name:</strong><span class="ml-2">{{ $getLoginStatusData['username'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Created Date :</strong><span class="ml-2">{{ $getLoginStatusData['created_at'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Updated Date :</strong><span class="ml-2">{{ $getLoginStatusData['updated_at'] }}</span></p>
                                    @else
                                    <p class="text-muted mb-2 font-13"><strong> Account Login Status: </strong><button type="button" class="btn btn-danger">Not Login Yet</button></p>
                                    @endif

                        </div>

                        <div class="tab-pane" id="ldap">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                LDAP Details [Employee No]</h5></u>

                                @if(!empty($ldapEmployeeDataEmp) && count($ldapEmployeeDataEmp) > 0)

                                <p class="text-muted mb-2 font-13"><strong> Ldap Status: </strong><button type="button" class="btn btn-success">Record Created</button></p>

                                <p class="text-muted mb-2 font-13"><strong>Surname (sn):</strong><span class="ml-2">{{ $ldapEmployeeDataEmp['sn'][0] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Common Name (cn):</strong><span class="ml-2">{{ $ldapEmployeeDataEmp['cn'][0] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Distinguished Name (DN):</strong><span class="ml-2">{{ $ldapEmployeeDataEmp['dn'] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>eduPersonOrgUnitDN:</strong><span class="ml-2">{{ $ldapEmployeeDataEmp['edupersonorgunitdn'][0] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>givenName:</strong><span class="ml-2">{{ $ldapEmployeeDataEmp['givenname'][0] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>mail :</strong><span class="ml-2">{{ $ldapEmployeeDataEmp['mail'][0] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>mobile :</strong> <span class="ml-2 ">{{ $ldapEmployeeDataEmp['mobile'][0] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>description :</strong> <span class="ml-2 ">{{ $ldapEmployeeDataEmp['description'][0] }}</span></p>

                                {{-- <p class="text-muted mb-2 font-13"><strong>initials :</strong> <span class="ml-2 ">{{ $ldapEmployeeDataEmp->initials[0] }}</span></p> --}}

                                <p class="text-muted mb-2 font-13"><strong>o (Organization) :</strong> <span class="ml-2 ">{{ $ldapEmployeeDataEmp['o'][0] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>eduPersonAffiliation :</strong> <span class="ml-2 ">{{ $ldapEmployeeDataEmp['edupersonaffiliation'][0] }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>edupersonentitlement :</strong> <span class="ml-2 ">@if ($ldapEmployeeDataEmp['edupersonentitlement'][0] == 'urn:mace:dir:entitlement:common-lib-terms') Common Library @elseif ($ldapEmployeeDataEmp['edupersonentitlement'][0] == 'urn:mace:dir:entitlement:common-lib-terms:learn-zoom')Common Library/ Zoom Learn @else  @endif</span></p>

                                <p class="text-muted mb-2 font-13"><strong>uid (User ID):</strong><span class="ml-2">{{ $ldapEmployeeDataEmp['uid'][0] }}</span></p>

                                <div class="password-container">
                                    <p class="text-muted mb-2 font-13"><strong>userpassword :</strong> <span class="ml-2 "><span id="password" class="password-text">••••••••</span>
                                    <input type="password" id="pin" class="pin-input" placeholder="Enter PIN">
                                    <input type="hidden" id="passwordHidden" value="{{ $ldapEmployeeDataEmp['userpassword'][0] }}">
                                    <button id="togglePassword" class="toggle-button">Show</button>
                                </div>

                                @else
                                <p class="text-muted mb-2 font-13"><strong> Ldap Status: </strong><button type="button" class="btn btn-danger">Record Not Found</button></p>
                                @endif

                                <br>

                                <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                    LDAP Details [SJP Email]</h5></u>

                                    @if(!empty($ldapEmployeeDataEmail) && count($ldapEmployeeDataEmail) > 0)

                                    <p class="text-muted mb-2 font-13"><strong> Ldap Status: </strong><button type="button" class="btn btn-success">Record Created</button></p>

                                    <p class="text-muted mb-2 font-13"><strong>Surname (sn):</strong><span class="ml-2">{{ $ldapEmployeeDataEmail['sn'][0] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Common Name (cn):</strong><span class="ml-2">{{ $ldapEmployeeDataEmail['cn'][0] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>Distinguished Name (DN):</strong><span class="ml-2">{{ $ldapEmployeeDataEmail['dn'] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>eduPersonOrgUnitDN:</strong><span class="ml-2">{{ $ldapEmployeeDataEmail['edupersonorgunitdn'][0] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>givenName:</strong><span class="ml-2">{{ $ldapEmployeeDataEmail['givenname'][0] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>employeenumber :</strong><span class="ml-2">{{ $ldapEmployeeDataEmail['employeenumber'][0] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>mobile :</strong> <span class="ml-2 ">{{ $ldapEmployeeDataEmail['mobile'][0] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>description :</strong> <span class="ml-2 ">{{ $ldapEmployeeDataEmail['description'][0] }}</span></p>

                                    {{-- <p class="text-muted mb-2 font-13"><strong>initials :</strong> <span class="ml-2 ">{{ $ldapEmployeeDataEmail->initials[0] }}</span></p> --}}

                                    <p class="text-muted mb-2 font-13"><strong>o (Organization) :</strong> <span class="ml-2 ">{{ $ldapEmployeeDataEmail['o'][0] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>eduPersonAffiliation :</strong> <span class="ml-2 ">{{ $ldapEmployeeDataEmail['edupersonaffiliation'][0] }}</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>edupersonentitlement :</strong> <span class="ml-2 ">@if ($ldapEmployeeDataEmail['edupersonentitlement'][0] == 'urn:mace:dir:entitlement:common-lib-terms') Common Library @elseif ($ldapEmployeeDataEmp['edupersonentitlement'][0] == 'urn:mace:dir:entitlement:common-lib-terms:learn-zoom')Common Library/ Zoom Learn @else  @endif</span></p>

                                    <p class="text-muted mb-2 font-13"><strong>uid (User ID):</strong><span class="ml-2">{{ $ldapEmployeeDataEmail['uid'][0] }}</span></p>

                                    <div class="password-container">
                                        <p class="text-muted mb-2 font-13"><strong>userpassword :</strong> <span class="ml-2 "><span id="password2" class="password-text">••••••••</span>
                                        <input type="password" id="pin2" class="pin-input" placeholder="Enter PIN">
                                        <input type="hidden" id="passwordHidden2" value="{{ $ldapEmployeeDataEmail['userpassword'][0] }}">
                                        <button id="togglePassword2" class="toggle-button">Show</button>
                                    </div>

                                    @else
                                    <p class="text-muted mb-2 font-13"><strong> LDAP Status: </strong><button type="button" class="btn btn-danger">Record Not Found</button></p>
                                    @endif

                        </div>


                        <div class="tab-pane" id="salaryRevision">

                            <u><h5 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>
                                Salary Revision 2025 Details [Employee No]</h5></u>

                                @if(!empty($salaryRevision))

                                <p class="text-muted mb-2 font-13"><strong> Salary Revision 2025 Status: </strong>
                                    @if($salaryRevision->status == 0)
                                    <button type="button" class="btn btn-warning">Pending First Checking</button>
                                    @elseif($salaryRevision->status == 1 && $salaryRevision->checking2_status == 1)
                                    <button type="button" class="btn btn-info">First Checking Done</button>
                                    @elseif($salaryRevision->checking2_status == 2 && $salaryRevision->accept_status == 1)
                                    <button type="button" class="btn btn-primary">Second Checking Done</button>
                                    @elseif($salaryRevision->accept_status == 2)
                                    <button type="button" class="btn btn-success">Salary Revision 2025 Accepted</button>
                                    @endif
                                </p>

                                <p class="text-muted mb-2 font-13"><strong>Employee No:</strong><span class="ml-2">{{ $salaryRevision->emp_no }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Designation:</strong><span class="ml-2">{{ $salaryRevision->designation }} - {{  $salaryRevision->sal_code }} @if($salaryRevision->grade != NULL || $salaryRevision->grade != '')-{{ $salaryRevision->grade }} @endif</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Service Category:</strong><span class="ml-2">{{ $salaryRevision->service_category }}</span></p>

                                <br>

                                <u><h6 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>Salary Data on 2024.12.31</h6></u>

                                <p class="text-muted mb-2 font-13"><strong>Salary Step:</strong><span class="ml-2">{{ $salaryRevision->sal_step_2024 }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Basic Salary:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->bSal_2024, 2) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Salary:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->sal_2024,2) }}</span></p>

                                <br>

                                <u><h6 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>Salary Data on 2025.01.01</h6></u>

                                <p class="text-muted mb-2 font-13"><strong>Salary Step:</strong><span class="ml-2">{{ $salaryRevision->sal_step_2025 }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Basic Salary:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->bSal_2025,2) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Salary:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->sal_2025,2) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Unpaid Amount:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->unpaid_2025,2) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Paid Amount:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->paid_2025,2) }}</span></p>

                                <br>
                                 <u><h6 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>Salary with Increment 2025</h6></u>

                                <p class="text-muted mb-2 font-13"><strong>Salary Step:</strong><span class="ml-2">{{ $salaryRevision->sal_step_withIncre_2025 }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Basic Salary:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->bSal_withIncre_2025,2) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Salary:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->sal_withIncre_2025,2) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Unpaid Amount:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->unpaid_withIncre_2025,2) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Paid Amount:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->paid_withIncre_2025,2) }}</span></p>


                                <br>
                                 <u><h6 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>Salary Data on 2026.01.01</h6></u>

                                <p class="text-muted mb-2 font-13"><strong>Salary Step:</strong><span class="ml-2">{{ $salaryRevision->sal_step_2026 }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Unpaid Amount:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->unpaid_2026,2) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Paid Amount:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->paid_2026,2) }}</span></p>


                                <br>
                                 <u><h6 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>Salary with Increment 2026</h6></u>

                                <p class="text-muted mb-2 font-13"><strong>Salary Step:</strong><span class="ml-2">{{ $salaryRevision->sal_step_withIncre_2026 }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Basic Salary:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->bSal_withIncre_2026,2) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Salary:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->sal_withIncre_2026,2) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Unpaid Amount:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->unpaid_withIncre_2026,2) }}</span></p>

                                <p class="text-muted mb-2 font-13"><strong>Paid Amount:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->paid_withIncre_2026,2) }}</span></p>

                               <br>
                                 <u><h6 class="mb-4 text-uppercase"><i class="mdi mdi-briefcase mr-1"></i>Salary Data on 2027.01.01</h6></u>

                                 <p class="text-muted mb-2 font-13"><strong>Salary:</strong><span class="ml-2">Rs. {{ number_format($salaryRevision->sal_2027,2) }}</span></p>

                                @else
                                <p class="text-muted mb-2 font-13"><strong> Salary Revision 2025 Status: </strong><button type="button" class="btn btn-danger">Record Not Found</button></p>
                                @endif



                        </div>

                        <div class="tab-pane" id="settings">
                            <form>
                                <h5 class="mb-3 text-uppercase bg-light p-2"><i class="mdi mdi-account-circle mr-1"></i> Personal Info</h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="firstname">First Name</label>
                                            <input type="text" class="form-control" id="firstname" placeholder="Enter first name">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="lastname">Last Name</label>
                                            <input type="text" class="form-control" id="lastname" placeholder="Enter last name">
                                        </div>
                                    </div> <!-- end col -->
                                </div> <!-- end row -->

                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="userbio">Bio</label>
                                            <textarea class="form-control" id="userbio" rows="4" placeholder="Write something..."></textarea>
                                        </div>
                                    </div> <!-- end col -->
                                </div> <!-- end row -->

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="useremail">Email Address</label>
                                            <input type="email" class="form-control" id="useremail" placeholder="Enter email">
                                            <span class="form-text text-muted"><small>If you want to change email please <a href="javascript: void(0);">click</a> here.</small></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="userpassword">Password</label>
                                            <input type="password" class="form-control" id="userpassword" placeholder="Enter password">
                                            <span class="form-text text-muted"><small>If you want to change password please <a href="javascript: void(0);">click</a> here.</small></span>
                                        </div>
                                    </div> <!-- end col -->
                                </div> <!-- end row -->

                                <h5 class="mb-3 text-uppercase bg-light p-2"><i class="mdi mdi-office-building mr-1"></i> Company Info</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="companyname">Company Name</label>
                                            <input type="text" class="form-control" id="companyname" placeholder="Enter company name">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="cwebsite">Website</label>
                                            <input type="text" class="form-control" id="cwebsite" placeholder="Enter website url">
                                        </div>
                                    </div> <!-- end col -->
                                </div> <!-- end row -->

                                <h5 class="mb-3 text-uppercase bg-light p-2"><i class="mdi mdi-earth mr-1"></i> Social</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="social-fb">Facebook</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fab fa-facebook-square"></i></span>
                                                </div>
                                                <input type="text" class="form-control" id="social-fb" placeholder="Url">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="social-tw">Twitter</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fab fa-twitter"></i></span>
                                                </div>
                                                <input type="text" class="form-control" id="social-tw" placeholder="Username">
                                            </div>
                                        </div>
                                    </div> <!-- end col -->
                                </div> <!-- end row -->

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="social-insta">Instagram</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fab fa-instagram"></i></span>
                                                </div>
                                                <input type="text" class="form-control" id="social-insta" placeholder="Url">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="social-lin">Linkedin</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fab fa-linkedin"></i></span>
                                                </div>
                                                <input type="text" class="form-control" id="social-lin" placeholder="Url">
                                            </div>
                                        </div>
                                    </div> <!-- end col -->
                                </div> <!-- end row -->

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="social-sky">Skype</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fab fa-skype"></i></span>
                                                </div>
                                                <input type="text" class="form-control" id="social-sky" placeholder="@username">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="social-gh">Github</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fab fa-github"></i></span>
                                                </div>
                                                <input type="text" class="form-control" id="social-gh" placeholder="Username">
                                            </div>
                                        </div>
                                    </div> <!-- end col -->
                                </div> <!-- end row -->

                                <div class="text-right">
                                    <button type="submit" class="btn btn-success waves-effect waves-light mt-2"><i class="mdi mdi-content-save"></i> Save</button>
                                </div>
                            </form>
                        </div>
                        <!-- end settings content-->

                    </div> <!-- end tab-content -->
                </div> <!-- end card-box-->

            </div> <!-- end col -->
        </div>
    </div>
</section>
<script src="script.js"></script>
<script>
    document.getElementById('togglePassword').addEventListener('click', function () {
    const passwordElement = document.getElementById('password');
    const pinInput = document.getElementById('pin');
    const correctPin = '1994';  // Set your correct PIN here
    const maskedPassword = '••••••••';
    const actualPassword = document.getElementById('passwordHidden').value;

    if (pinInput.value === correctPin) {
        if (passwordElement.textContent === maskedPassword) {
            passwordElement.textContent = actualPassword;
            this.textContent = 'Hide';
        } else {
            passwordElement.textContent = maskedPassword;
            this.textContent = 'Show';
        }
    } else {
        alert('Incorrect PIN');
    }
});

document.getElementById('togglePassword2').addEventListener('click', function () {
    const passwordElement = document.getElementById('password2');
    const pinInput = document.getElementById('pin2');
    const correctPin = '1994';  // Set your correct PIN here
    const maskedPassword = '••••••••';
    const actualPassword = document.getElementById('passwordHidden2').value;

    if (pinInput.value === correctPin) {
        if (passwordElement.textContent === maskedPassword) {
            passwordElement.textContent = actualPassword;
            this.textContent = 'Hide';
        } else {
            passwordElement.textContent = maskedPassword;
            this.textContent = 'Show';
        }
    } else {
        alert('Incorrect PIN');
    }
});

</script>
@endsection
