@extends('admin.admin_master')
@section('admin')

<!-- Content Header (Page header) -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<div class="content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-12 text-center">
                <h5 class="mb-1">
                    {{ strtoupper($vacancy_name)}} <b>
                        <font style="font-size: 20px;">(
                            @if ($designation_category == 138)
                            PERMANENT
                            @elseif ($designation_category == 140)
                            TEMPORARY
                            @elseif ($designation_category == 141)
                            CONTRACT
                            @elseif ($designation_category == 142)
                            ASSIGNMENT BASIS
                            @endif
                            )</font>
                    </b><br>
                    @if($vacancy_status == 28)
                    APPLICANTS LIST
                    @elseif ($vacancy_status == 29)
                    SHORTLISTED APPLICANTS
                    @endif
                </h5>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-2">
                <label class="text-warning">Pending : {{$countForPending}}</label>
            </div>
            <div class="col-2">
                <label class="text-success">Accept : {{$countForAccept}}</label>
            </div>
            <div class="col-2">
                <label class="text-danger">Reject : {{$countForReject}}</label>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <table id="example5" class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>SN</th>
                                    <th>App. No</th>
                                    <th>Name</th>
                                    <th>NIC</th>
                                    <th>Apply Date</th>
                                    <th>Status</th>
                                    <th>Mobile</th>
                                    <th>Email</th>
                                    <th>Remark</th>
                                    <th width="20%">view</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                $i = 1;
                                @endphp
                                @foreach ($data_text as $data_texts)
                                <tr>
                                    <td>{{$i++}}</td>
                                    <td>{{$data_texts->id}}</td>
                                    <td>{{$data_texts->name_with_initials}} {{$data_texts->last_name}}</td>
                                    <td>{{ strtoupper($data_texts->nic)}}</td>
                                    <td>{{$data_texts->created_at}}</td>
                                    <td>
                                        @if ($data_texts->short_list_status == 0)

                                        <span class="badge bg-warning"> Pending </span>
                                        @elseif ($data_texts->short_list_status == 1)
                                        <span class="badge bg-success"> Accept </span>
                                        @elseif ($data_texts->short_list_status == 2)
                                        <span class="badge bg-danger"> Reject </span>
                                        @endif

                                    </td>
                                    <td>{{$data_texts->telephone_mobile}}</td>
                                    <td>{{$data_texts->email_address}}</td>
                                    <td>{{$data_texts->short_list_remark}}</td>
                                    <td>
                                        <div class="d-flex gap-2">
                                            <a href="{{ route('nac.recruitment.process.shortList.page3.open', encrypt($data_texts->id)) }}" class="btn btn-sm btn-primary">View <i class="fa fa-eye"></i></a>

                                            <form method="post" action="{{ route('nonacademic.application.report.download') }}" class="m-0 ml-2" target="_blank">
                                                @csrf
                                                <input type="hidden" name="app_id" value="{{ $data_texts->id }}" >
                                                <button type="submit" class="btn btn-sm btn-success"><i class="fa fa-print"></i> Application</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

                @if ($countForPending == 0)
                <div class="card">
                    <div class="card-body">
                        <form action="{{ route('nac.recruitment.process.shortList.complete')}}" method="POST" enctype="multipart/form-data" onsubmit="return validateForm()" id="shortlistForm">
                            @csrf
                            <div class="row">
                                <div class="col-2">
                                    <label>Select Next Vacancy Process</label>
                                </div>
                                <div class="col-4">
                                    <select name="proType" id="proType" class="select2bs5" style="width: 100%;">
                                        <option value="0" selected disabled>Select Next Vacancy Process</option>
                                        <option value="1">Written Exam</option>
                                        <option value="2">Practical Exam</option>
                                        <option value="3">Interview</option>
                                    </select>
                                    <div class="text-danger mt-1" id="proTypeError" style="display: none;">
                                        Please select a valid option.
                                    </div>
                                </div>
                                <div class="col-6 d-flex justify-content-end">
                                    <input type="hidden" id="vacancyid" name="vacancyid" value="{{$vacancyid}}">
                                </div>
                            </div>

                            <div class="row mt-3" id="contributionMarkSection">
                                <div class="col-2">
                                    <label for="contributionMark">Final Interview Contribution Percentage for Selected Process</label>
                                </div>
                                <div class="col-4">
                                    <input type="number" class="form-control" id="contributionMark" name="contributionMark" min="0" max="100" step="5" placeholder="Enter contribution mark (0-100)">
                                    <span class="text-danger">@error('contributionMark'){{$message}}@enderror</span>
                                </div>
                                <div class="col-6 d-flex justify-content-end">
                                    @if ($short_list_complete_status == 1)
                                    <button type="button" class="btn btn-md btn-danger" disabled> Finalized </button>
                                    @else
                                    <button class="btn btn-md btn-success" type="button" onclick="confirmFinalization()"> Confirm List </button>
                                    @endif
                                </div>
                            </div>
                        </form>

                        <script>
                        function validateForm() {
                            let isValid = true;
                            const proType = document.getElementById('proType').value;
                            const contributionMark = parseFloat(document.getElementById('contributionMark').value);

                            if (proType === '0' || !proType) {
                                document.getElementById('proTypeError').style.display = 'block';
                                isValid = false;
                            } else {
                                document.getElementById('proTypeError').style.display = 'none';
                            }

                            if (contributionMark < 0 || contributionMark > 100) {
                                document.getElementById('contributionMarkError').style.display = 'block';
                                isValid = false;
                            } else {
                                document.getElementById('contributionMarkError').style.display = 'none';
                            }

                            return isValid;
                        }
                        </script>
                        <script>
                        function confirmFinalization() {
                            if (!validateForm()) return;

                            Swal.fire({
                                title: 'Are you sure?',
                                text: 'Do you want to finalize this shortlist? This action cannot be undone.',
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: 'Yes, finalize it!'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    document.getElementById('shortlistForm').submit();
                                }
                            });
                        }
                        </script>
                    </div>
                </div>
                @endif

            </div>
        </div>
    </div>
</section>

<script>
    function validateForm() {
        const proType = document.getElementById('proType');
        const errorDiv = document.getElementById('proTypeError');

        if (proType.value === "0" || proType.value === "") {
            errorDiv.style.display = 'block';
            proType.classList.add('is-invalid');
            return false; // Prevent form submission
        } else {
            errorDiv.style.display = 'none';
            proType.classList.remove('is-invalid');
            return true; // Allow form submission
        }
    }
</script>

@endsection
