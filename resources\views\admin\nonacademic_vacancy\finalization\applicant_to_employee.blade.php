@extends('admin.admin_master')
@section('admin')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<section class="content">

    <div class="col-12">
        <div class="card card-primary">
            <div class="content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-12 text-center">
                    <h5 class="mb-1">
                        {{ strtoupper($vacancy->designations->designation_name) }} @if ($vacancy->display_name != '' && $vacancy->designation_category == 138)
                            ({{ strtoupper($vacancy->display_name) }})
                        @endif
                        -
                        <b>
                            <font style="font-size: 20px;">(
                                @if ($vacancy->designation_category == 138)
                                    Permanent
                                @elseif ($vacancy->designation_category == 140)
                                    Temporary
                                @elseif ($vacancy->designation_category == 141)
                                    Contract
                                @elseif ($vacancy->designation_category == 142)
                                    Assignment Basis
                                @endif
                                )
                            </font>
                        </b>
                    </h5>

                    <h6 align="center">
                        Application for @if ($vacancy->designation_category == 138)
                            Permanent
                        @elseif ($vacancy->designation_category == 140)
                            Temporary
                        @elseif ($vacancy->designation_category == 141)
                            Contract
                        @elseif ($vacancy->designation_category == 142)
                            Assignment Basis
                        @endif Non-Academic Positions
                    </h6>
                    <font style="font-size: 15px;">Application ID : <b class="text-danger"
                            style="font-size: 25px;">{{ str_pad($appData->id, 3, '0', STR_PAD_LEFT) }}
                        </b> &nbsp;&nbsp;&nbsp;&nbsp;Submit Date : <b
                            class="text-dark">{{ date_format($appData->created_at, 'd-M-Y') }}</b>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                    </font>
                </div><!-- /.col -->
            </div><!-- /.row -->
        </div><!-- /.container-fluid -->
    </div>
          <div class="card-body">
            <form method="POST" action="{{ route('nac.applicant.convert.employee.store') }}" id="myForm">
                @csrf
            <div class="row">
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label>Referance No. : <span style="color: #ff0000;">*</span></label>
                  <input type="text" name="reference_no" class="form-control" id="reference_no" value="{{ $appData->reference_number }}" readonly/>
                   <span class="text-danger">@error('reference_no'){{$message}}@enderror</span>
                </div>
              </div>
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label>NIC No. : <span style="color: #ff0000;">*</span></label>
                  <input type="text" name="nic" class="form-control" id="nic" value="{{ $appData->nic }}" readonly/>
                  <span class="text-danger">@error('nic'){{$message}}@enderror</span>
                </div>
              </div>
              <div class="col col-md-6 col-sm-12">
                <div class="row">
                  <div class="col col-md-4 col-sm-12">
                    <div class="form-group">
                      <label>Title: <span style="color: #ff0000;">*</span></label>
                      <select name="titel_id" id="titel_id" class="select2bs4" style="width: 100%">

                        <option value="{{ $appData->title }}" selected>{{ ucfirst($appData->applicantTitle->category_name)}}</option>

                      </select>
                      <span class="text-danger">@error('titel_id'){{$message}}@enderror</span>
                    </div>
                  </div>
                  <div class="col col-md-8 col-sm-12">
                    <div class="form-group">
                      <label>Initials: <span style="color: #ff0000;">*</span></label>
                      <input type="text" name="initials" class="form-control" id="initials" value="{{ $appData->name_with_initials }}" placeholder="Ex:- R.M.L." readonly/>
                      <span class="text-danger">@error('initials'){{$message}}@enderror</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-8 col-sm-12">
                <div class="form-group">
                  <label>Name Denoted By Initials : <span style="color: #ff0000;">*</span></label>
                  <input type="text" name="name_denoted_by_initials" class="form-control" id="name_denoted_by_initials" value="{{ ucwords(strtolower($appData->name_denoted_by_initials)) }}" readonly/>
                  <span class="text-danger">@error('name_denoted_by_initials'){{$message}}@enderror</span>
                </div>
              </div>
              <div class="col col-md-4 col-sm-12">
                <div class="form-group">
                  <label>Last Name: <span style="color: #ff0000;">*</span></label>
                  <input type="text" name="last_name" class="form-control" id="last_name" value="{{ ucwords(strtolower($appData->last_name)) }}" readonly/>
                  <span class="text-danger">@error('last_name'){{$message}}@enderror</span>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label>Mobile No.: <span style="color: #ff0000;">*</span></label>
                  <input type="text" name="mobile_no" class="form-control" id="mobile_no" value="{{ $appData->telephone_mobile }}" placeholder="Ex:-0777777777" readonly/>
                  <span class="text-danger">@error('mobile_no'){{$message}}@enderror</span>
                </div>
              </div>
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label>Residence Phone No: </label>
                  <input type="text" name="phone_no" class="form-control" id="phone_no" value="{{ $appData->telephone_residence }}" placeholder="Ex:-0112222222" readonly/>
                  <span class="text-danger">@error('phone_no'){{$message}}@enderror</span>
                </div>

              </div>
              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Personal Email: <span style="color: #ff0000;">*</span></label>
                  <input type="email" name="email" id="email" class="form-control" id="email" value="{{ $appData->email_address }}" readonly/>
                  <span class="text-danger">@error('email'){{$message}}@enderror</span>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-4 col-sm-12">
                <div class="form-group">
                  <label>Date of Birth: <span style="color: #ff0000;">*</span></label>
                    <div class="input-group date" id="date_of_birth" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#date_of_birth" name="date_of_birth" id="date_of_birth_val" value="{{ date("d-M-Y", strtotime($appData->date_of_birth)) }}" placeholder="Ex:- 01-Jan-2000" data-target="#date_of_birth" data-toggle="datetimepicker" disabled>
                        <div class="input-group-append" data-target="#date_of_birth" data-toggle="datetimepicker">
                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                        </div>
                    </div>
                    <span class="text-danger">@error('date_of_birth'){{$message}}@enderror</span>
                </div>
              </div>
              <div class="col col-md-4 col-sm-12">
                <div class="form-group">
                  <label>Gender: <span style="color: #ff0000;">*</span></label>
                  <select name="gender_id" id="gender_id" class="select2bs4" style="width: 100%">

                    <option value="{{ $appData->gender_id }}" >{{ ucfirst($appData->gender->category_name)}}</option>
                  </select>
                  <span class="text-danger">@error('gender_id'){{$message}}@enderror</span>
                </div>
              </div>
              <div class="col col-md-4 col-sm-12">
                <div class="form-group">
                  <label>Civil Status: <span style="color: #ff0000;">*</span></label>
                  <select name="civil_status_id" id="civil_status_id" class="select2bs4" style="width: 100%">

                    <option value="{{ $appData->civil_status }}" selected>{{ ucfirst($appData->civilStatus->category_name)}}</option>

                  </select>
                  <span class="text-danger">@error('civil_status_id'){{$message}}@enderror</span>
                </div>
              </div>

            </div>

            <div class="row">
              <div class="col col-md-12">
                <label><strong>Permanent Address</strong></label>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label class="font-weight-normal">Address Line 1: <span style="color: #ff0000;">*</span></label>
                  <input type="text" name="permanent_add1" class="form-control" id="permanent_add1" value="{{ ucwords(strtolower($appData->permanent_address_line1)) }}" readonly/>
                  <span class="text-danger">@error('permanent_add1'){{$message}}@enderror</span>
                </div>
              </div>
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label class="font-weight-normal">Address Line 2:</label>
                  <input type="text" name="permanent_add2" class="form-control" id="permanent_add2" value="{{ ucwords(strtolower($appData->permanent_address_line2)) }}" readonly/>
                </div>
              </div>
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label class="font-weight-normal">Address Line 3:</label>
                  <input type="text" name="permanent_add3" class="form-control" id="permanent_add3" value="{{ ucwords(strtolower($appData->permanent_address_line3)) }}" readonly/>
                </div>
              </div>
              <div class="col col-md-3 col-sm-12">
                <div class="form-group">
                  <label class="font-weight-normal">City: <span style="color: #ff0000;">*</span></label>
                  <select name="permanent_city_id" id="permanent_city_id" class="select2bs5" style="width: 100%">
                    <option value="{{ $appData->permanent_address_city }}" selected>{{ ucfirst($appData->pCity->name_en)}}</option>
                  </select>
                  <span class="text-danger">@error('permanent_city_id'){{$message}}@enderror</span>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-12 col-sm-12">
                <label><strong>Postal Address</strong></label>
              </div>
            </div>
            {{-- <div class="row">
              <div class="col col-md-12 col-sm-12">
                <label class="font-weight-normal">Same as Permanent Address:</label> <input type="checkbox" id="checkbox1" >
              </div>
            </div> --}}
              <div class="row">
                <div class="col col-md-3 col-sm-12">
                  <div class="form-group">
                    <label class="font-weight-normal">Address Line 1: <span style="color: #ff0000;">*</span></label>
                    <input type="text" name="postal_add1" class="form-control" id="postal_add1" value="{{ ucwords(strtolower($appData->postal_address_line1)) }}" readonly/>
                    <span class="text-danger">@error('postal_add1'){{$message}}@enderror</span>
                  </div>
                </div>
                <div class="col col-md-3 col-sm-12">
                  <div class="form-group">
                    <label class="font-weight-normal">Address Line 2:</label>
                    <input type="text" name="postal_add2" class="form-control" id="postal_add2" value="{{ ucwords(strtolower($appData->postal_address_line2)) }}" readonly/>
                  </div>
                </div>
                <div class="col col-md-3 col-sm-12">
                  <div class="form-group">
                    <label class="font-weight-normal">Address Line 3:</label>
                    <input type="text" name="postal_add3" class="form-control" id="postal_add3" value="{{ ucwords(strtolower($appData->postal_address_line3)) }}" readonly/>
                  </div>
                </div>
                <div class="col col-md-3 col-sm-12">
                  <div class="form-group">
                    <label class="font-weight-normal">City: <span style="color: #ff0000;">*</span></label>
                    <select name="postal_city_id" id="postal_city_id" class="select2bs5" style="width: 100%">
                      <option value="" selected>Select City</option>

                      <option value="{{ $appData->postal_address_city }}" selected>{{ ucfirst($appData->PoCity->name_en)}}</option>

                    </select>
                    <span class="text-danger">@error('postal_city_id'){{$message}}@enderror</span>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col col-md-4 col-sm-12">
                  <div class="form-group">
                    <label>Citizenship of Applicant: <span style="color: #ff0000;">*</span></label>
                    <input type="text" name="citizenship" class="form-control" id="citizenship" placeholder="Ex:-Sri Lankan" value="Sri Lankan" readonly/>
                    <span class="text-danger">@error('citizenship'){{$message}}@enderror</span>
                  </div>
                </div>
                <div class="col col-md-4 col-sm-12">
                  <div class="form-group">
                    <label>If a Citizen of Sri Lanka, how obtained :<span style="color: #ff0000;">*</span></label>
                    <select name="state_of_citizenship_id" id="state_of_citizenship_id" class="select2bs4" style="width: 100%">

                      @foreach($citizenships as $citizenship)
                      @if($citizenship->category_name ==  $appData->citizen_sri_lanka_obtained)
                      <option value="{{ $citizenship->id }}" selected>{{ ucfirst($citizenship->category_name)}}</option>
                      @endif
                      @endforeach

                    </select>
                    <span class="text-danger">@error('state_of_citizenship_id'){{$message}}@enderror</span>
                  </div>
                </div>
                 @if($appData->citizen_sri_lanka_obtained == 'By Registration')
                <div class="col col-md-4 col-sm-12">
                  <div class="form-group"  id="reg_num" style="display:none">
                    <label>Citizen Registration No.: <span style="color: #ff0000;">*</span></label>
                    <input type="text" name="citizen_registration_no" class="form-control" id="citizen_registration_no" value="{{ $appData->citizen_registration_no }}"/>
                    <span class="text-danger">@error('citizen_registration_no'){{$message}}@enderror</span>
                  </div>
                </div>
                @endif
              </div>

        </div>
        <br>
        <div class="card card-outline card-primary" >
          <div class="card-body">
            <div class="row">
              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>File Reference No:</label>
                  <input type="text" name="file_reference_number" class="form-control" id="file_reference_number" value="{{ $appData->file_reference_number }}" readonly/>
                </div>
              </div>

              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Main Branch: <span style="color: #ff0000;">*</span></label>
                  <select name="main_branch_id" id="main_branch_id" class="select2bs5" style="width: 100%">
                    <option value="53" selected>Non Academic</option>
                  </select>
                  <span class="text-danger">@error('main_branch_id'){{$message}}@enderror</span>
                </div>
              </div>


            </div>
            <div class="row">

                <div class="col col-md-6 col-sm-12">
                    <div class="form-group">
                      <label>Designation: <span style="color: #ff0000;">*</span></label>
                      <select name="designation_id" id="designation_id" class="select2bs5" style="width: 100%">

                        <option value="{{ $vacancy->designation_id }}"  selected>{{ ucfirst($vacancy->designations->designation_name)}} ( {{ $vacancy->designations->salary_code }} ) </option>

                      </select>
                      <span class="text-danger">@error('designation_id'){{$message}}@enderror</span>
                    </div>
                  </div>

                  <div class="col col-md-6 col-sm-12">
                    <div class="form-group">
                        <label>Basic Salary (Current): <span style="color: #ff0000;">*</span></label>
                        <input type="text" placeholder="Current Basic Salary" name="current_basic_salary" class="form-control" id="current_basic_salary" value="{{ $appData->current_basic_salary }}" readonly/>
                        <span class="text-danger">@error('current_basic_salary'){{$message}}@enderror</span>
                    </div>
                </div>
                </div>

            <div class="row">
                <div class="col col-md-4 col-sm-12">
                <h3 class="card-title"><b>Working Location</b></h3>
                </div>
              </div>
            <div class="row">
              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label for="">Faculty:<span class="text-danger"> *</span></label>
                  <select name="faculty_id" id="faculty_id" class="select2bs5" style="width: 100%" >
                    <option value="{{ $appData->faculty_id }}" selected>{{ ucfirst($appData->faculties->faculty_name)}}</option>
                  </select>
                  <span class="text-danger">@error('faculty_id'){{$message}}@enderror</span>
                </div>
                </div><!-- col-md-6 -->
                <div class="col col-md-6 col-sm-12">
                  <div class="form-group">
                    <label for="">Department:<span class="text-danger"> *</span></label>
                    <div class="controls">
                      <select name="department_id" id="department_id" class="select2bs5" style="width: 100%" >
                        <option value="{{ $appData->department_id }}" selected>{{ ucfirst($appData->departments->department_name)}}</option>

                      </select>

                     <span class="text-danger">@error('department_id'){{$message}}@enderror</span>

                     </div>
                       </div>
                  </div><!-- col-md-6 -->


                  </div><!-- row -->

                  <div class="row">
                <div class="col col-md-4 col-sm-12">
                <h3 class="card-title"><b>Carder Location</b></h3>
                </div>
              </div>
            <div class="row">
              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label for="">Faculty:<span class="text-danger"> *</span></label>
                  <select name="carder_faculty_id" id="carder_faculty_id" class="select2bs5" style="width: 100%" >
                    <option value="{{ $appData->carder_faculty_id }}" selected>{{ ucfirst($appData->carderfaculties->faculty_name)}}</option>
                  </select>
                  <span class="text-danger">@error('carder_faculty_id'){{$message}}@enderror</span>
                </div>
                </div><!-- col-md-6 -->
                <div class="col col-md-6 col-sm-12">
                  <div class="form-group">
                    <label for="">Department:<span class="text-danger"> *</span></label>
                    <div class="controls">
                      <select name="carder_department_id" id="carder_department_id" class="select2bs5" style="width: 100%" >
                        <option value="{{ $appData->carder_department_id }}" selected>{{ ucfirst($appData->carderdepartments->department_name)}}</option>

                      </select>

                     <span class="text-danger">@error('carder_department_id'){{$message}}@enderror</span>

                     </div>
                       </div>
                  </div><!-- col-md-6 -->


                  </div><!-- row -->



            <div class="row">

              <div class="col col-md-4 col-sm-12">
                <div class="form-group">
                  <label lass="font-weight-normal">Appointment Date (Initial): <span style="color: #ff0000;">*</span></label>
                    <div class="input-group date" id="initial_appointment_date" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#initial_appointment_date" name="initial_appointment_date" id="initial_appointment_date_val" value="{{ date("d-M-Y", strtotime($appData->duty_assume_date)) }}" placeholder="Ex:- 01-Jan-2000" data-target="#initial_appointment_date" data-toggle="datetimepicker" disabled>
                        <div class="input-group-append" data-target="#initial_appointment_date" data-toggle="datetimepicker">
                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                        </div>
                    </div>
                    <span class="text-danger">@error('initial_appointment_date'){{$message}}@enderror</span>
                </div>
              </div>

              <div class="col col-md-4 col-sm-12">
                <div class="form-group">
                  <label lass="font-weight-normal">Appointment Date (Continue Service): <span style="color: #ff0000;">*</span></label>
                    <div class="input-group date" id="gratuity_cal_date" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#gratuity_cal_date" name="gratuity_cal_date" id="gratuity_cal_date_val" value="{{ date("d-M-Y", strtotime($appData->duty_assume_date)) }}" placeholder="Ex:- 01-Jan-2000" data-target="#gratuity_cal_date" data-toggle="datetimepicker" disabled>
                        <div class="input-group-append" data-target="#gratuity_cal_date" data-toggle="datetimepicker">
                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                        </div>
                    </div>

                </div>
              </div>



            <div class="col col-md-4 col-sm-12">
              <div class="form-group">
                <label>Duty Assumed Date (Current): <span style="color: #ff0000;">*</span></label>
                  <div class="input-group date" id="current_appointment_date" data-target-input="nearest">
                      <input type="text" class="form-control datetimepicker-input" data-target="#current_appointment_date" name="current_appointment_date" value="{{ date("d-M-Y", strtotime($appData->duty_assume_date)) }}" placeholder="Ex:- 01-Jan-2000" data-target="#current_appointment_date" data-toggle="datetimepicker" disabled>
                      <div class="input-group-append" data-target="#current_appointment_date" data-toggle="datetimepicker">
                          <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                      </div>
                  </div>
                  <span class="text-danger">@error('current_appointment_date'){{$message}}@enderror</span>
              </div>
            </div>

            </div>

            <div class="row">

            <div class="col col-md-12 col-sm-12">
                <div class="form-group">
                  <label>Highest Educational Qualification: <span style="color: #ff0000;">*</span></label>
                  <select name="emp_highest_edu_level" id="emp_highest_edu_level" class="select2bs5" style="width: 100%">
                     @foreach($educationLevels as $educationLevel)\
                    @if($educationLevel->category_name ==  $appData->academic_qualification)
                    <option value="{{ $educationLevel->id}}" selected>{{ ucfirst($educationLevel->category_name )}}</option>
                    @endif
                    @endforeach

                  </select>
                  <span class="text-danger">@error('emp_highest_edu_level'){{$message}}@enderror</span>
                </div>
              </div>

              </div>

            <div class="row">
            @if($vacancy->designation_category == 138)
            <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Increment Date: </label>
                    <div class="input-group date" id="increment_date" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#increment_date" name="increment_date" value="{{ date("d-M", strtotime($appData->duty_assume_date))  }}" placeholder="Ex:- 01-Jan" data-target="#increment_date" data-toggle="datetimepicker" disabled>
                        <div class="input-group-append" data-target="#increment_date" data-toggle="datetimepicker">
                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                        </div>
                    </div>
                    <span class="text-danger">@error('increment_date'){{$message}}@enderror</span>
                </div>
              </div>
            @else
            <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Service Termination Date: <span style="color: #ff0000;">*</span></label>
                    <div class="input-group date" id="salary_termination_date_1" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#salary_termination_date_1" name="salary_termination_date_1" value="{{ date("d-M-Y", strtotime($appData->salary_termination_date)) }}" placeholder="Ex:- 01-Jan-2000" data-target="#salary_termination_date_1" data-toggle="datetimepicker" disabled>
                        <div class="input-group-append" data-target="#salary_termination_date_1" data-toggle="datetimepicker">
                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                        </div>
                    </div>
                    <span class="text-danger">@error('salary_termination_date_1'){{$message}}@enderror</span>
                </div>
              </div>
              <div class="col col-md-6 col-sm-12">
                <div class="form-group">
                  <label>Salaray Payment Type: <span style="color: #ff0000;">*</span></label>
                  <select name="salary_payment_type" id="salary_payment_type" class="select2bs5" style="width: 100%">
                    <option value="{{ $appData->id}}" selected>{{ ucfirst($appData->salarypaymenttype->category_name )}}</option>
                  </select>
                  <span class="text-danger">@error('salary_payment_type'){{$message}}@enderror</span>
                </div>
              </div>
            @endif

            </div>

        </div>

    </div>
        <br>
        <div style="background-color: #fffccd;">
            <div class="card-header">
                <h3><b>Applicant to Employee Conversion</b></h3>
              </div>
            <div class="card-body">
                <input type="hidden" name="app_id" value="{{ $appData->id }}">
                <input type="hidden" name="designation_category" id ="designation_category" value="{{ $vacancy->designation_category }}">

                <div class="row">
                    <div class="col col-md-6 col-sm-12">
                        <div class="form-group">
                          <label>File Assign MA<span style="color: #ff0000;">*</span></label>
                          <select name="operator_user" id="operator_user" class="select2bs4" style="width: 100%">
                            <option value="" selected disabled>Select Operator User</option>
                            @foreach( $operators as $operator)
                               <option value="{{$operator->employee_no}}" {{ $operator->employee_no == $appData->duty_assume_ma_emp ? 'selected' : '' }}>{{$operator->employee_no}} - {{$operator->initials}} {{$operator->last_name}}</option>
                            @endforeach
                          </select>
                          <span class="text-danger">@error('operator_user'){{$message}}@enderror</span>
                        </div>
                      </div>

                </div>




            </div>
                <!-- /.card-body -->
                <div class="card-footer">

                    <input type="submit" class="btn btn-danger btn-lg float-right ml-2" value="Create Employee" name="submit" style="background-color:#990000;">
                    <a href="{{ route('nac.vacancy.interview.completed.application.list',encrypt($vacancy->id)) }}"  class="btn btn-secondary btn-lg float-right">Back</a>
                </div>
            </form>
        </div>


        {{-- <div class="card-footer">
          <input type="submit" class="btn btn-primary" value="submit" >
        </div> --}}

      </form>
    </div>
  </section>

  <script type="text/javascript">

document.addEventListener("DOMContentLoaded", function () {
var form = document.getElementById("myForm"); // Replace "myForm" with the actual ID of your form

if (form) {
form.addEventListener("submit", function (e) {
    var operator_user = document.getElementById("operator_user");

    if (operator_user.value === "") {
        e.preventDefault(); // Prevent form submission
        Swal.fire({
            icon: 'error',
            title: 'Error! Incomplete information',
            text: 'please select File Assign MA',
        });
    }
});
}
});

    </script>
@endsection
