<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApplicationForm extends Model
{
    use HasFactory;

     protected $guarded = [];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($applicant) {
            $applicant->reference_number = 'APP-' . strtoupper(uniqid());
        });
    }

    public function applicantTitle()
    {
        return $this->belongsTo(Category::class,'title');
    }

     public function gender()
    {
        return $this->belongsTo(Category::class,'gender_id');
    }

    public function civilStatus()
    {
        return $this->belongsTo(Category::class,'civil_status');
    }

    public function pCity()
    {
        return $this->belongsTo(City::class,'permanent_address_city');
    }

    public function PoCity()
    {
        return $this->belongsTo(City::class,'postal_address_city');
    }

    public function faculties()
    {
        return $this->belongsTo(Faculty::class,'faculty_id');
    }

    public function departments()
    {
        return $this->belongsTo(Department::class,'department_id');
    }

    public function carderfaculties()
    {
        return $this->belongsTo(Faculty::class,'carder_faculty_id');
    }

    public function carderdepartments()
    {
        return $this->belongsTo(Department::class,'carder_department_id');
    }

    public function salarypaymenttype()
    {
        return $this->belongsTo(Category::class,'salary_payment_type');
    }


}
