<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Mail\TINNotificationMail;
use App\Mail\TINReminderNotificationMail;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class TINController extends Controller
{
    public function TINIndex(Request $request)
    {

        if (isset($request->empNo)) {

            $empNo = $request->empNo;

            $userDetails = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('faculties', 'employees.faculty_id', '=', 'faculties.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->select(
                    'employees.employee_no',
                    'employees.email',
                    'employees.last_name',
                    'employees.name_denoted_by_initials',
                    'employees.nic',
                    'designations.designation_name',
                    'faculties.faculty_name',
                    DB::raw('CASE WHEN employees.faculty_id IN (50, 51) THEN "None" ELSE faculties.faculty_name END AS faculty_name'),
                    'departments.department_name',
                    'designations.salary_code',
                    'employees.initials',
                    'employees.mobile_no',
                    'employees.tin_no',
                    'title.category_name as title'
                )
                ->where('employees.employee_no', $empNo)
                ->first();

            return view('frontend.tin.index', compact('empNo', 'userDetails'));
        } else {

            $message_text_E = "Employee Number is not found in the system.";
            return view('frontend.tin.error1', compact('message_text_E'));
        }
    }

    public function TINSubmit(Request $request)
    {

        $request->validate(
            [
                'tin_no' => ['required', 'regex:/^[0-9]{9}$/m'],
                'tin_no_confirm' => ['required', 'same:tin_no'],
            ],
            [
                'tin_no.required' => 'TIN number is required',
                'tin_no.regex' => 'TIN number should be 9 digits',
                'tin_no_confirm.required' => 'Confirm TIN number is required',
                'tin_no_confirm.same' => 'TIN number and Confirm TIN number should be same',
            ]
        );

        $empNo = $request->empNo;
        $tinNo = $request->tin_no;

        $updateTIN = Employee::where('employee_no', $empNo)->update(['tin_no' => $tinNo]);

        if ($updateTIN) {

            $employee = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('faculties', 'employees.faculty_id', '=', 'faculties.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->select(
                    'employees.employee_no',
                    'employees.email',
                    'employees.last_name',
                    'employees.name_denoted_by_initials',
                    'employees.nic',
                    'designations.designation_name',
                    'faculties.faculty_name',
                    DB::raw('CASE WHEN employees.faculty_id IN (50, 51) THEN "None" ELSE faculties.faculty_name END AS faculty_name'),
                    'departments.department_name',
                    'designations.salary_code',
                    'employees.initials',
                    'employees.mobile_no',
                    'employees.tin_no',
                    'title.category_name as title'
                )
                ->where('employees.employee_no', $empNo)
                ->first();

        if ($employee && $employee->email) {
            Mail::to($employee->email)->send(new TINNotificationMail($employee));
        }
            return redirect()->route('tin.success', $empNo);

        } else {
            $message_text_E = "TIN number update failed.";
            return view('frontend.tin.error1', compact('message_text_E'));
        }
    }

    public function TINSuccess($empNo)
    {
        $employee = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('faculties', 'employees.faculty_id', '=', 'faculties.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->select(
                    'employees.employee_no',
                    'employees.email',
                    'employees.last_name',
                    'employees.name_denoted_by_initials',
                    'employees.nic',
                    'designations.designation_name',
                    'faculties.faculty_name',
                    DB::raw('CASE WHEN employees.faculty_id IN (50, 51) THEN "None" ELSE faculties.faculty_name END AS faculty_name'),
                    'departments.department_name',
                    'designations.salary_code',
                    'employees.initials',
                    'employees.mobile_no',
                    'employees.tin_no',
                    'title.category_name as title'
                )
                ->where('employees.employee_no', $empNo)
                ->first();

        if (!$employee) {
            abort(404, 'Employee not found.');
        }

        $message_text_E = "TIN updated successfully.";

        return view('frontend.tin.success', compact('message_text_E', 'employee'));
    }

    public function TINPendingReminderMail()
    {
        $employees = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                      ->where('employees.employee_status_id', 110)
                      ->whereIN('designations.ugc_mis', [100, 102, 103, 135, 136])
                      ->where('employees.main_branch_id', 52)
                      ->where('employees.tin_no_reminder', 0)
                      ->where('employees.tin_no',0)
                      ->where('employees.email', '!=', '')
                      ->get();

        foreach ($employees as $employee) {

            if ($employee->email) {

               Mail::to($employee->email)->send(new TINReminderNotificationMail($employee));

               $data = Employee::find($employee->employee_no);
               $data->tin_no_reminder = 1;
               $data->save();
            }
        }

        $notification = array(
            'message' => 'TIN Reminder Mail Sent Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('admin.dashboard')->with($notification);
    }
}
