<?php

namespace App\Console\Commands;

use App\Mail\HeadExpirationNotificationMail;
use App\Models\Department;
use App\Models\DepartmentHead;
use App\Models\Employee;
use App\Models\Faculty;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class DepartmentHeadExpirationBrachCron extends Command
{

    protected $signature = 'dheadbranch:cron';

    protected $description = 'Command description';

    public function handle()
    {
        $dataAfterMonth = date('Y-m-d', strtotime('+1 week'));

        $data = DepartmentHead::whereDate('end_date', '=', $dataAfterMonth)
            ->whereDate('end_date', '!=', '1970-01-01')
            ->where('active_status', 1)
            ->get();

        $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('employees.email')
                ->whereIn('roles.id', [4,5, 6])
                ->where('users.main_branch_id', 52)
                ->get();

        foreach ($data as $head) {

            $deptId = $head->department_id;
            $headEmp = $head->emp_no;

            $emailData = [
                'departmentName' => $this->getDepartmentName($deptId),
                'headName' => $this->getDepartmentHeadName($headEmp),
                'headPosition' => $this->getHeadPositionName($deptId),
                'headTerminationDate' => $head->end_date,
                'facultyName' => $this->getFacultyName($deptId),
            ];

            $mail = new HeadExpirationNotificationMail($emailData);

            foreach ($users as $user) {
                if (App::environment('production')) {

               $BranchEmail = $user->email;

            } else {

                $BranchEmail = '<EMAIL>';
            }

                Mail::to($BranchEmail)->send($mail);
            }


        }

        return Command::SUCCESS;
    }

    private function getDepartmentName($deptId){

        $department = Department::where('id', $deptId)->select('department_name')->first();

        return $department ? $department->department_name : null;
    }

    private function getDepartmentHeadName($empNo){

        $departmentHeadName = Employee::join('categories', 'employees.title_id', '=', 'categories.id')->where('employee_no', $empNo)->select('initials','last_name','category_name')->first();

        return $departmentHeadName ? $departmentHeadName->category_name . ' ' . $departmentHeadName->initials . ' ' . $departmentHeadName->last_name : null;
    }

    private function getHeadPositionName($deptId){

        $departmentHead = DepartmentHead::where('department_id', $deptId)->first();

        return $departmentHead ? $departmentHead->HeadPositionName->category_name : null;
    }

    private function getFacultyName($deptId){

        $faculty =  Department::where('id', $deptId)->select('faculty_code')->first();
        $facultyName = Faculty::where('id', $faculty->faculty_code)->select('faculty_name')->first();

        return $facultyName ? $facultyName->faculty_name : null;
    }
}
