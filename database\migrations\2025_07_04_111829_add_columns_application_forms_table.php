<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            $table->integer('file_assign_ma_user')->nullable()->after('salary_payment_type');
            $table->integer('duty_assume_confirm_emp')->nullable()->after('file_assign_ma_user');
            $table->date('duty_assume_confirm_date')->nullable()->after('duty_assume_confirm_emp');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            $table->dropColumn('file_assign_ma_user');
            $table->dropColumn('duty_assume_confirm_emp');
            $table->dropColumn('duty_assume_confirm_date');
        });
    }
};
