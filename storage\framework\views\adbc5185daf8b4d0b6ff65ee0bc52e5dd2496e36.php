<aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- <PERSON> -->
    <a >
        <div class="image_area text-center" style="padding-top: 20px;padding-bottom: 10px;">
            <img src="<?php echo e(asset('backend/dist/img/logo.png')); ?>" style="width:80px;height:80px;"  class="img-responsive img-circle">
      </div>
    </a>
    <?php
        $currentDate = date('Y-m-d');
        $vacancyNotice = App\Models\VacancyNotice::where('start_date','<=',$currentDate)->where('end_date','>=',$currentDate)->get();
    ?>
    <!-- Sidebar -->
    <div class="sidebar">
        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
              <!-- Add icons to the links using the .nav-icon class
                   with font-awesome or any other icon font library -->
              <?php if(Route::currentRouteName() === 'application.form.view'): ?>
              <li class="nav-header"></li>
              <li class="nav-item">
                <a href="<?php echo e(route('nonacademic.vancancy.application.home')); ?>" type="button" class="nav-link" style="background-color:#990000;pointer-events: none;">
                    <i class="nav-icon fa fa-home text-white"></i>
                    <p class="text">Home</p>
                </a>

              </li>
              <?php else: ?>
              <li class="nav-header"></li>
              <li class="nav-item">
                <a href="<?php echo e(route('nonacademic.vancancy.application.home')); ?>" type="button" class="nav-link"  style="background-color:#990000">
                  <i class="nav-icon fa fa-home text-white"></i>
                  <p class="text">Home</p>
                </a>
              </li>
              <?php endif; ?>
              <li class="nav-header"></li>
              <li class="nav-item ">
                <a  href="<?php echo e(route('nonacademic.detail.instruction')); ?>" type="button" class="nav-link"  style="background-color:#990000" target="_blank">
                  <i class="nav-icon fa fa-clipboard text-white"></i>
                  <p class="text">Instructions</p>
                </a>
              </li>
              <li class="nav-header"></li>
              <li class="nav-item ">
                <a type="button" class="nav-link" data-toggle="modal" data-target="#exampleModalCenter1" style="background-color:#990000">
                  <i class="nav-icon fa fa-shopping-bag text-white"></i>
                  <p class="text">Vacancy Notices</p>
                </a>
              </li>
              

              <li class="nav-header"></li>
              <li class="nav-item ">
                <a type="button" href="mailto:<EMAIL>" class="nav-link" style="background-color:#990000">
                  <i class="nav-icon fa fa-phone text-white"></i>
                  <p class="text">More Information<br/><span class="font-weight-bold small">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 011 2758212 / 011 2758102</span></p>
                </a>
              </li>
              <li class="nav-header"></li>
              <li class="nav-item ">
                <a type="button" class="nav-link" style="background-color:#990000">
                  <i class="nav-icon fa fa-phone text-white"></i>
                  <p class="text">Technical Support <br/> <span class="font-weight-bold"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 011 2758759</span></p>
                </a>
              </li>

              
            </ul>
          </nav>
          <div class="bottom-li">
            <ul class="nav">
                <li class="nav-item ">
                    <a type="button" href="https://cits.sjp.ac.lk/" target="_blank" class="nav-link">
                        <p class="text">Powered by<br>
                        <span class="font-weight-bold">Centre for IT Services</span></p>
                    </a>
                </li>
            </ul>
        </div>

    </div>
    <!-- /.sidebar -->
  </aside>
  <div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h3 class="modal-title w-100"><b>Detailed Instructions</b></h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
            </div>
            <div class="modal-body">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-md-4 col-sm-12 mb-3">
                            <a href="" class="btn btn-block btn-outline-danger btn-lg p-2 p-sm-4 btn-hover-white">
                                <div class="icon">
                                    <i class="fa fa-university" style="font-size:30px"></i>
                                </div>
                                <span style="font-size:20px">Academic Vacancies Instructions</span><br>
                            </a>
                        </div>
                        <div class="col-md-4 col-sm-12 text-center">
                            <a href="" class="btn btn-block btn-outline-secondary btn-lg p-2 p-sm-4 btn-hover-white" style="pointer-events: none">
                                <div class="icon">
                                    <i class="fa fa-user" style="font-size:30px"></i>
                                </div>
                                <span style="font-size:20px">Nonacademic Vacancies Instructions</span><br>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="exampleModalCenter1" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h3 class="modal-title w-100"><b>Vacancy Notices</b></h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
            </div>
            <div class="modal-body">
                <div class="container">
                    <div class="row justify-content-center">
                        <?php $__currentLoopData = $vacancyNotice; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-6 col-sm-12 mb-3">
                            <a href="<?php echo e(route('notice.download',$notice->id)); ?>" class="btn btn-block btn-outline-danger btn-lg p-2 p-sm-4 btn-hover-white" target="_blank">
                                <div class="icon">
                                    <?php if($notice->type == 44): ?>
                                    <i class="fa fa-university" style="font-size:30px"></i>
                                    <?php elseif($notice->type == 45): ?>
                                    <i class="fa fa-universal-access" style="font-size:30px"></i>
                                    <?php elseif($notice->type == 46): ?>
                                    <i class="fa fa-user " style="font-size:30px"></i>
                                    <?php endif; ?>
                                </div>
                                <span style="font-size:20px"><?php echo e($notice->vacancyCategory->category_name); ?> Vacancies</span><br>
                                <span style="font-size:15px;">Notice on <?php echo e(date("d-M-Y", strtotime($notice->start_date))); ?></span>
                            </a>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


                    </div>
                </div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="exampleModalCenter2" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h3 class="modal-title w-100"><b>Scheme of Recruitment</b></h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
            </div>
            <div class="modal-body">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-md-4 col-sm-12 mb-3">
                            <a href="<?php echo e(route('sor.download3')); ?>" class="btn btn-block btn-outline-danger btn-lg p-2 p-sm-4 btn-hover-white" target="_blank">
                                <div class="icon">
                                    <i class="fa fa-user-md" style="font-size:30px"></i>
                                </div>
                                <span style="font-size:20px">SOR - Management Assistant</span>
                            </a>
                        </div>
                        <div class="col-md-4 col-sm-12 mb-3">
                            <a href="<?php echo e(route('sor.download2')); ?>" class="btn btn-block btn-outline-danger btn-lg p-2 p-sm-4 btn-hover-white" target="_blank">
                                <div class="icon">
                                    <i class="fa fa-user" style="font-size:30px"></i>
                                </div>
                                <span style="font-size:20px">SOR - Management Assistant</span><br>
                            </a>
                        </div>
                        <div class="col-md-4 col-sm-12 mb-3">
                            <a href="<?php echo e(route('sor.download3')); ?>" class="btn btn-block btn-outline-danger btn-lg p-2 p-sm-4 btn-hover-white" target="_blank">
                                <div class="icon">
                                    <i class="fa fa-user-md" style="font-size:30px"></i>
                                </div>
                                <span style="font-size:20px">SOR - Management Assistant</span>
                            </a>
                        </div>
                        <div class="col-md-4 col-sm-12 mb-3">
                            <a href="<?php echo e(route('sor.download2')); ?>" class="btn btn-block btn-outline-danger btn-lg p-2 p-sm-4 btn-hover-white" target="_blank">
                                <div class="icon">
                                    <i class="fa fa-user" style="font-size:30px"></i>
                                </div>
                                <span style="font-size:20px">SOR - Management Assistant</span><br>
                            </a>
                        </div>
                        <div class="col-md-4 col-sm-12 mb-3">
                            <a href="<?php echo e(route('sor.download3')); ?>" class="btn btn-block btn-outline-danger btn-lg p-2 p-sm-4 btn-hover-white" target="_blank">
                                <div class="icon">
                                    <i class="fa fa-user-md" style="font-size:30px"></i>
                                </div>
                                <span style="font-size:20px">SOR - Management Assistant</span>
                            </a>
                        </div>
                        <div class="col-md-4 col-sm-12 mb-3">
                            <a href="<?php echo e(route('sor.download2')); ?>" class="btn btn-block btn-outline-danger btn-lg p-2 p-sm-4 btn-hover-white" target="_blank">
                                <div class="icon">
                                    <i class="fa fa-user" style="font-size:30px"></i>
                                </div>
                                <span style="font-size:20px">SOR - Management Assistant</span><br>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                
            </div>
        </div>
    </div>
</div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script>
    $(document).ready(function() {
        $('#exampleModalCenter1 a').click(function() {
            $('#exampleModalCenter1').modal('hide');
        });

        $('#exampleModalCenter2 a').click(function() {
            $('#exampleModalCenter2').modal('hide');
        });
    });
</script>
<?php /**PATH D:\University\HRSystem\resources\views/frontend/nonacademic/body/sidebar.blade.php ENDPATH**/ ?>