<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {
            $table->integer('finalized_user')->nullable()->after('interview_cut_off_mark');
            $table->date('finalized_date')->nullable()->after('finalized_user');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {
            $table->dropColumn('finalized_user');
            $table->dropColumn('finalized_date');
        });
    }
};
