@extends('admin.admin_master')
@section('admin')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-12 text-center">
                    <h5 class="mb-1">
                        {{ strtoupper($vacancy->designations->designation_name) }} @if ($vacancy->display_name != '' && $vacancy->designation_category == 138)
                            ({{ strtoupper($vacancy->display_name) }})
                        @endif
                        -
                        <b>
                            <font style="font-size: 20px;">(
                                @if ($vacancy->designation_category == 138)
                                    Permanent
                                @elseif ($vacancy->designation_category == 140)
                                    Temporary
                                @elseif ($vacancy->designation_category == 141)
                                    Contract
                                @elseif ($vacancy->designation_category == 142)
                                    Assignment Basis
                                @endif
                                )
                            </font>
                        </b>
                    </h5>

                    <h6 align="center">
                        Application for @if ($vacancy->designation_category == 138)
                            Permanent
                        @elseif ($vacancy->designation_category == 140)
                            Temporary
                        @elseif ($vacancy->designation_category == 141)
                            Contract
                        @elseif ($vacancy->designation_category == 142)
                            Assignment Basis
                        @endif Non-Academic Positions
                    </h6>
                    <font style="font-size: 15px;">Application ID : <b class="text-danger"
                            style="font-size: 25px;">{{ str_pad($applicationForm->id, 3, '0', STR_PAD_LEFT) }}
                        </b> &nbsp;&nbsp;&nbsp;&nbsp;Submit Date : <b
                            class="text-dark">{{ date_format($applicationForm->created_at, 'd-M-Y') }}</b>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                    </font>
                </div><!-- /.col -->
            </div><!-- /.row -->
        </div><!-- /.container-fluid -->
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12" id="application_view">
                    <div class="card">
                        <div class="card-body">

                            <div class="card-body">
                                <!-- Personal Information Section -->
                                <div class="section">
                                    {{-- <h5 class="bg-secondary p-2">Personal Details</h5><br> --}}
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th width="40%">Title</th>
                                                    <td>
                                                        {{ $applicationForm->title_name }}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Initials</th>
                                                    <td>{{ $applicationForm->name_with_initials }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Name Denoted by Initials</th>
                                                    <td>{{ $applicationForm->name_denoted_by_initials }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Last Name</th>
                                                    <td>{{ $applicationForm->last_name }}</td>
                                                </tr>
                                                <tr>
                                                    <th>NIC</th>
                                                    <td>{{ strtoupper($applicationForm->nic) }}</td>
                                                </tr>


                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th width="40%">Date of Birth</th>
                                                    <td>{{ $applicationForm->date_of_birth }}</td>
                                                </tr>
                                                @php
                                                    use Carbon\Carbon;

                                                    // Parse DOB and closing date
                                                    $dob = Carbon::parse($applicationForm->date_of_birth);
                                                    $closingDate = Carbon::parse($vacancy->closing_date);

                                                    // Calculate actual applicant age
                                                    $applicantAge = $dob->diffInYears($closingDate);

                                                    // Check if age is within range
                                                    $minAge = $vacancy->min_age; // assumed in years
                                                    $maxAge = $vacancy->max_age;

                                                    $isAgeValid = $applicantAge >= $minAge && $applicantAge < $maxAge;
                                                @endphp

                                                <tr>
                                                    <th>Age as of the closing date</th>
                                                    <td style="color: {{ $isAgeValid ? 'inherit' : 'red' }}">
                                                        {{ $applicationForm->age_application_date_new }}
                                                    </td>
                                                </tr>



                                                <tr>
                                                    <th>Sri Lankan Citizenship</th>
                                                    <td>{{ $applicationForm->citizen_sri_lanka_obtained ?? 'N/A' }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Citizen Registration No</th>
                                                    <td>{{ $applicationForm->citizen_registration_no ?? 'N/A' }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Civil Status</th>
                                                    <td>{{ $applicationForm->civil_status_name ?? 'N/A' }}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <br>
                                    <div class="card card-primary" style="background-color: #fffccd;">
                                        <form method="POST" action="{{ route('nac.vacancy.practical.exam.result.store') }}" id="myForm">
                                            @csrf
                                            <div class="card-header">
                                                <h3><b>Practical Exam Results</b></h3>
                                              </div>
                                            <div class="card-body">
                                                <input type="hidden" name="vacancy_id" value="{{ $vacancy->id }}" >
                                                <input type="hidden" name="app_id" value="{{ $applicationForm->id }}" >
                                                <input type="hidden" name="practical_exam_contribution_percentage" value="{{  $vacancy->practical_exam_contribution_percentage }}" >

                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <label>Practical Exam Decision</label>
                                                            <div class="row">
                                                                <div class="col-1"></div>
                                                                <div class="col-2">
                                                                    <input class="form-check-input" type="radio" name="recom" id="rYes" value="1" onclick='not_recom();' {{ $applicationForm->practical_exam_status == 1 ? 'checked' : ''}}>
                                                                    <label class="form-check-label" for="flexRadioDefault1">Selected</label>
                                                                </div>
                                                                <div class="col-2">
                                                                    <input class="form-check-input" type="radio" name="recom" id="rNo" value="2" onclick='not_recom();' {{ $applicationForm->practical_exam_status == 2 ? 'checked' : ''}}>
                                                                    <label class="form-check-label" for="flexRadioDefault1">Not Selected</label>
                                                                </div>
                                                                <div class="col-2">
                                                                    <input class="form-check-input" type="radio" name="recom" id="rAb" value="3" onclick='not_recom();' {{ $applicationForm->practical_exam_status == 3 ? 'checked' : ''}}>
                                                                    <label class="form-check-label" for="flexRadioDefault1">Absent</label>
                                                                </div>
                                                            </div>
                                                            <span class="text-danger">@error('recom'){{$message}}@enderror</span>
                                                            <br>
                                                        </div>

                                                </div>

                                                </div>
                                                    @if($applicationForm->practical_exam_status != 3)
                                                    <div class="row"  id="notRecom" style="display:block;">
                                                    @else
                                                    <div class="row"  id="notRecom" style="display:none;">
                                                    @endif

                                                      <div class="col col-md-6 col-sm-12">
                                                        <div class="form-group">
                                                          <label>Practical Exam Marks [{{ $vacancy->practical_exam_contribution_percentage }}%]: <span style="color: #ff0000;">*</span></label>
                                                          <input type="number" name="practical_marks" class="form-control" id="practical_marks" value="{{ $applicationForm->practical_marks }}" max="{{ $vacancy->practical_exam_contribution_percentage }}" step="0.01"/>
                                                          <span class="text-danger">@error('practical_marks'){{$message}}@enderror</span>
                                                        </div>
                                                      </div>
                                                    </div>
                                                </div>
                                            </div>
                                                <!-- /.card-body -->
                                                <div class="card-footer">

                                                    <input type="submit" class="btn btn-danger btn-lg float-right ml-2" value="Submit" name="submit" style="background-color:#990000;">
                                                    <a href="{{ route('nac.vacancy.practical.exam.result.entry.list',encrypt($vacancy->id)) }}"  class="btn btn-secondary btn-lg float-right">Back</a>
                                                </div>
                                            </form>
                                        </div>

                                </div>


                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
    <script>

        function not_recom() {

            if (document.getElementById("rNo").checked) {
                document.getElementById("notRecom").style.display = "block";

            } else if (document.getElementById("rYes").checked) {
                document.getElementById("notRecom").style.display = "block";

            } else if (document.getElementById("rAb").checked) {
                document.getElementById("notRecom").style.display = "none";

            }
        }

        document.addEventListener("DOMContentLoaded", function () {
        var form = document.getElementById("myForm"); // Replace "myForm" with the actual ID of your form

        if (form) {
            form.addEventListener("submit", function (e) {
                var radioNotSelect = document.getElementById("rNo");
                var radioSelect = document.getElementById("rYes");
                var radioAbsent = document.getElementById("rAb");
                var practical_marks = document.getElementById("practical_marks");

              if (radioSelect.checked && practical_marks.value.trim() === "") {
                e.preventDefault(); // Prevent form submission
                Swal.fire({
                    icon: 'error',
                    title: 'Error! Incomplete information',
                    text: 'Practical exam marks are required',
                });
               }

               if (radioNotSelect.checked && practical_marks.value.trim() === "") {
                e.preventDefault(); // Prevent form submission
                Swal.fire({
                    icon: 'error',
                    title: 'Error! Incomplete information',
                    text: 'Practical exam marks are required',
                });
               }
            });
        }
    });


    </script>
@endsection
