<?php

namespace App\Console\Commands;

use App\Models\Employee;
use Illuminate\Console\Command;

class RetirmentCron extends Command
{

    protected $signature = 'retirement:cron';


    protected $description = '146-Retirement';


    public function handle()
    {
       $employees = Employee::whereDate('retirement_date', '<', date('Y-m-d'))
            ->whereIn('employee_work_type', [138, 139])
            ->where('employee_status_id', 110)
            ->get();

        foreach ($employees as $employee) {
            $employee->employee_status_id = 111;
            $employee->employee_status_type_id = 146;
            $employee->save();

            //$this->employeeRecordAdd($employee->employee_no);
        }

        return Command::SUCCESS;
    }
}
