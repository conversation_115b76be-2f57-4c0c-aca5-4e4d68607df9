@extends('frontend.frontend_admin_master')
@section('admin1')

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header justify-content-center">
                        <div class="row">
                            <div class="col-12 col-md-12 col-sm-12">
                                <h2 class="text-center"> TAXPAYER IDENTIFICATION NUMBER (TIN)</h2>
                            </div>

                        </div>
                    </div>
                    @if($userDetails != NULL)
                    <div class="card-body" style="background-color: #C9E6FF;">
                        <div class="row">
                            <div class="col-10">

                                <div class="row">
                                    <div class="col-3 p-1">
                                        <label>Employee No.</label>
                                    </div>
                                    <div class="col-4 p-1">
                                        <p><b>:</b> {{ $userDetails != NULL ? $userDetails->employee_no : "" }}</p>
                                    </div>
                                    <div class="col-2 p-1">
                                        <label>Salary Code</label>
                                    </div>
                                    <div class="col-3 p-1">
                                        <p><b>:</b>{{ $userDetails != NULL ? $userDetails->salary_code : "" }}</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-3 p-1">
                                        <label>Name with Initials</label>
                                    </div>
                                    <div class="col-9 p-1">
                                        <p><b>:</b> {{ $userDetails != NULL ? $userDetails->title : "" }} {{
                                            $userDetails != NULL ? $userDetails->initials : "" }} {{ $userDetails !=
                                            NULL ? $userDetails->last_name : "" }}</p>
                                    </div>

                                </div>
                                <div class="row">
                                    <div class="col-3 p-1">
                                        <label>Department</label>
                                    </div>
                                    <div class="col-4 p-1">
                                        <p><b>:</b> {{ $userDetails != NULL ? $userDetails->department_name : "" }}</p>
                                    </div>
                                    <div class="col-2 p-1">
                                        <label>Faculty</label>
                                    </div>
                                    <div class="col-3 p-1">
                                        <p><b>:</b> {{ $userDetails != NULL ? $userDetails->faculty_name : "" }}</p>
                                    </div>

                                </div>
                                <div class="row">
                                    <div class="col-3 p-1">
                                        <label>Designation</label>
                                    </div>
                                    <div class="col-4 p-1">
                                        <p><b>:</b> {{ $userDetails != NULL ? $userDetails->designation_name : ""
                                            }}</p>
                                    </div>

                                    <div class="col-2 p-1">
                                        <label>Mobile</label>
                                    </div>
                                    <div class="col-3 p-1">
                                        <p><b>:</b>{{ $userDetails != NULL ? $userDetails->mobile_no : "" }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="text-center">
                                    @php
                                    if ($userDetails != NULL){

                                    $empNo = $userDetails->employee_no;

                                    }

                                    $imageUrl = 'https://hrms.sjp.ac.lk/backend/dist/img/profile/'.$empNo.'.jpg';
                                    $imageExists = false;

                                    // Check if the image URL exists and is a valid image
                                    if (@getimagesize($imageUrl)) {
                                    $imageExists = true;
                                    }
                                    @endphp

                                    @if ($imageExists)
                                    <img src="{{ $imageUrl }}"
                                        class="img-responsive mx-auto d-block img-fluid border border-dark img-thumbnail"
                                        style="width: 150px;" alt="profile-image">
                                    @else
                                    <img src="https://hrms.sjp.ac.lk/backend/dist/img/profile.jpg"
                                        class="img-responsive mx-auto d-block img-fluid border border-dark img-thumbnail"
                                        style="width: 150px;" alt="profile-image">
                                    @endif


                                </div>
                            </div>
                        </div>
                    </div>

                    <br>

                    <div class="card-body">
                        <form action="{{ route('tin.submit') }}" method="POST">
                            @csrf
                            <input type="hidden" name="empNo" value="{{ $empNo }}">
                            <div class="row">
                                <div class="col col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <label> Enter Your TIN: <span style="color: #ff0000;">*</span></label>
                                        <input type="text" name="tin_no" class="form-control" id="tin_no"
                                            value="{{ ($userDetails != NULL && $userDetails->tin_no != 0) ? $userDetails->tin_no : old('tin_no', '') }}" />
                                        <span class="text-danger">@error('tin_no'){{$message}}@enderror</span>
                                    </div>
                                </div>
                                <div class="col col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <label> Confirm Your TIN: <span style="color: #ff0000;">*</span></label>
                                        <input type="text" name="tin_no_confirm" class="form-control"
                                            id="tin_no_confirm" value="{{ old('tin_no_confirm') }}"
                                            onpaste="return false" oncopy="return false" oncut="return false"
                                            oncontextmenu="return false" />
                                        <small class="form-text text-muted">
                                            Please re-enter your TIN to confirm — copy/paste is disabled.
                                        </small>
                                        <span class="text-danger">@error('tin_no_confirm'){{$message}}@enderror</span>
                                    </div>
                                </div>
                            </div>


                    </div>

                    <div class="card-footer">
                        <div class="row">
                            <div class="col-12">

                                <input type="submit" class="btn btn-lg btn-danger float-right  ml-2" value="Submit TIN"
                                    name="submit">

                            </div>
                        </div>
                    </div>
                    @else
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <p class="text-center text-danger">Employee Record Not Found.</p>
                            </div>
                        </div>
                    </div>
                    @endif
                    </form>

                </div>
            </div>
        </div>
    </div>
</section>
@endsection
