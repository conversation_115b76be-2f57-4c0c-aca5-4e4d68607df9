@extends('admin.admin_master')
@section('admin')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-8">
                <h3 class="m-0">Acative Employee Common Details - Report</h3>
            </div><!-- /.col -->
            <div class="col-sm-4">
                <ol class="breadcrumb float-sm-right">
                    <a href="{{ route('custom.filter.view') }}" style="float: right;" class="btn btn-sm btn-success mb-2 mr-2">Back</a>
                    <a href="{{ route('filter.view') }}" style="float: right;" class="btn btn-sm btn-primary mb-2">Home Filter</a>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <!-- /.box-header -->
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example5" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="5%">SN</th>
                                        <th>Employee No</th>
                                        <th>NIC</th>
                                        <th>Name with Initials (With Title)</th>
                                        <th>Name with Initials (Without Title)</th>
                                        <th>Full Name</th>
                                        <th>Mobile No</th>
                                        <th>Email</th>
                                        <th>Date of Birth</th>
                                        <th>Age</th>
                                        <th>Gender</th>
                                        <th>Civil Status</th>
                                        <th>Race</th>
                                        <th>Religion</th>
                                        <th>Permanent Address</th>
                                        <th>Postal Address</th>
                                        @role('super-admin')
                                        <th data-priority="1">Main Branch</th>
                                        @endrole
                                        <th>Designation</th>
                                        <th>Salary Code</th>
                                        <th>Salary Grade</th>
                                        <th>Designation (With grade)</th>
                                        <th>Current Basic Salary</th>
                                        <th>Faculty</th>
                                        <th>Department/Division</th>
                                        <th>Working Type</th>
                                        <th>Active Status</th>
                                        <th>Main Group</th>
                                        <th>UGC MIS Category</th>
                                        <th>UGC Finace Category</th>
                                        <th>Appointment Date Permanent (Initial)</th>
                                        <th>Appointment Date (Continue Service)</th>
                                        <th>Duty Assumed Date (Current)</th>
                                        <th>Retirement Date</th>
                                        <th>ETF No</th>
                                        <th>UPF No</th>
                                        <th>Pension No</th>
                                        <th>File Operator</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($employees as $key => $employee)
                                    <tr>
                                        <td>{{ $key+1 }}</td>
                                        <td>{{ ucfirst($employee->employee_no) }}</td>
                                        <td>{{ $employee->nic }}</td>
                                        <td>{{ $employee->title_name }} {{ ucfirst($employee->initials) }} {{ ucfirst($employee->last_name) }}</td>
                                        <td>{{ ucfirst($employee->initials) }} {{ ucfirst($employee->last_name) }}</td>
                                        <td>{{ ucfirst($employee->name_denoted_by_initials) }} {{ ucfirst($employee->last_name) }}</td>
                                        <td>{{ $employee->mobile_no }}</td>
                                        <td>{{ $employee->email }}</td>
                                        <td>{{ $employee->date_of_birth }}</td>
                                        <td>{{ \Carbon\Carbon::parse($employee->date_of_birth)->diff(\Carbon\Carbon::now())->format('%y Years, %m Months and %d Days'); }}</td>
                                        <td>{{ $employee->genderName->category_name }}</td>
                                        <td>{{ $employee->civilStatusName->category_name }}</td>
                                        <td>{{ $employee->raceName->category_name }}</td>
                                        <td>{{ $employee->religionName->category_name }}</td>
                                        <td>{{ $employee->permanent_add1 }}, @if($employee->permanent_add2 != '') {{ $employee->permanent_add2 }}, @endif @if($employee->permanent_add3 != '') {{ $employee->permanent_add3 }}, @endif {{ $employee->permanentCityName->name_en }}</td>
                                        <td>{{ $employee->postal_add1 }}, @if($employee->postal_add2 != '') {{ $employee->postal_add2 }}, @endif @if($employee->postal_add3 != '') {{ $employee->postal_add3 }}, @endif {{ $employee->postalCityName->name_en }}</td>
                                        @role('super-admin')
                                        <td>{{ $employee->mainBranch->category_name }}</td>
                                        @endrole
                                        <td>{{ $employee->designationName->designation_name }} </td>
                                        <td>{{ $employee->salary_code }}</td>
                                        <td>@if($employee->category_name != ""){{ $employee->category_name }}@endif</td>
                                        <td>{{ $employee->designationName->designation_name }} ( {{ $employee->designationName->salary_code }} )  @if($employee->category_name != "")- {{ $employee->category_name }}@endif</td>
                                        <td>{{ $employee->current_basic_salary }}</td>
                                        <td>{{ $employee->getFacultyName->faculty_name }}</td>
                                        <td>
                                            @if($employee->name_status == 1)
                                            Department of {{ $employee->getDepartmentName->department_name }}
                                            @else
                                            {{ $employee->getDepartmentName->department_name }}
                                            @endif
                                        </td>
                                        <td>{{ $employee->workTypeName->category_name }}</td>
                                        <td>{{ $employee->empStatusTypeName->category_name }}</td>
                                        <td>{{ $employee->main_group_name }}</td>
                                        <td>{{ $employee->ugc_mis_name }}</td>
                                        <td>{{ $employee->ugc_finance_name }}</td>
                                        <td>{{ date("Y-m-d", strtotime($employee->initial_appointment_date)) }}</td>
                                        <td>{{ date("Y-m-d", strtotime($employee->gratuity_cal_date)) }}</td>
                                        <td>{{ date("Y-m-d", strtotime($employee->current_appointment_date)) }}</td>
                                        <td>{{ date("Y-m-d", strtotime($employee->retirement_date)) }}</td>
                                        <td>{{ $employee->etf_no }}</td>
                                        <td>{{ $employee->upf_no }}</td>
                                        <td>{{ $employee->pension_reference_no }}</td>
                                        @if($employee->assign_ma_user_id != 0)
                                        <td>{{ $employee->getAssignOperator->initials }} {{ $employee->getAssignOperator->last_name }}</td>
                                        @else
                                        <td class="text-danger">No File Assign Operator</td>
                                        @endif
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- /.box-body -->
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection
