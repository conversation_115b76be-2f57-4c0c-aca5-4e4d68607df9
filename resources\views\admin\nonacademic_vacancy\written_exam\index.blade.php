@extends('admin.admin_master')
@section('admin')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Written Exam Vacancy List</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="#">Home</a></li>
                    <li class="breadcrumb-item active">Written Exam Vacancy List</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="box">

                    <!-- /.box-header -->
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example2" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="5%">SN</th>
                                        <th>Vacancy ID</th>
                                        <th>Vacancy</th>
                                        <th>Vacancy Category</th>
                                        <th>Written Exam Contribution Percentage</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($vacancies as $key => $vacancy)
                                    <tr>
                                        <td>{{ $key+1 }}</td>
                                        <td>{{ $vacancy->id }}</td>

                                        <td> {{ strtoupper($vacancy->designations->designation_name) }} @if ($vacancy->display_name != '' && $vacancy->designation_category == 138)
                                            ({{ strtoupper($vacancy->display_name) }}) @endif

                                        </td>

                                        <td> @if ($vacancy->designation_category == 138)
                                        Permanent
                                        @elseif ($vacancy->designation_category == 140)
                                        Temporary
                                        @elseif ($vacancy->designation_category == 141)
                                        Contract
                                        @elseif ($vacancy->designation_category == 142)
                                        Assignment Basis
                                        @endif

                                        </td>
                                        <td>{{ $vacancy->written_exam_contribution_percentage }} %</td>

                                        <td>
                                            <div class="d-flex gap-2">
                                                @role('super-admin')
                                                <a href="{{ route('nac.writing.exam.notification.send',encrypt($vacancy->id)) }}" class="btn btn-sm btn-success">Send SMS Notification</a>
                                                <a href="{{ route('nac.writing.exam.email.notification.send',encrypt($vacancy->id)) }}" class="btn btn-sm btn-success ml-2">Send Email Notification</a>
                                                @endrole
                                                <a href="{{ route('nac.vacancy.writing.exam.result.entry.list',encrypt($vacancy->id)) }}" class="btn btn-sm btn-primary ml-2">Add Exam Result</a>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection
